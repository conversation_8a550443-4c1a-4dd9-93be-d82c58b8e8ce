// Flutter imports:
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

// Project imports:
import '../l10n/localization.dart';
import '../models/note.dart';
import '../models/segment.dart';
import '../utils/text_util.dart';
import '../services/app_settings.dart';
import '../screens/quiz/quiz_dialog.dart';
import '../constants/layout_config.dart';
import 'title_image.dart';
import 'ebbinghaus_hint_dialog.dart';

class NoteCard extends StatefulWidget {
  final Note note;
  final bool isSelected;
  final TimelineLayout layout;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Function(Note)? onToggleNeverDelete;
  final Function(Note)? onRetryAI;
  final VoidCallback? onCancelAI;
  final Function(Note)? onStateReset; // 新增：状态重置回调
  final Widget? child;

  const NoteCard({
    super.key,
    required this.note,
    required this.isSelected,
    required this.layout,
    this.onTap,
    this.onLongPress,
    this.onToggleNeverDelete,
    this.onRetryAI,
    this.onCancelAI,
    this.onStateReset, // 新增：状态重置回调
    this.child,
  });

  @override
  NoteCardState createState() => NoteCardState();
}

class NoteCardState extends State<NoteCard> {
  DateTime? _statusChangedAt;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    
    // 状态一致性检查：如果笔记显示为更新中但时间过长，可能是异常状态
    _checkAndFixInconsistentState();
    
    // 如果初始状态就是1，设置开始时间
    if (widget.note.isUpdating) {
      _statusChangedAt = DateTime.now();
    }

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && widget.note.isUpdating) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(NoteCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.note.isUpdating && !oldWidget.note.isUpdating) {
      _statusChangedAt = DateTime.now();
    } else if (!widget.note.isUpdating && oldWidget.note.isUpdating) {
      _statusChangedAt = null;
    }
  }

  // 检查并修复不一致的状态
  void _checkAndFixInconsistentState() {
    if (widget.note.isUpdating) {
      // 如果笔记显示为更新中，但更新时间超过5分钟，可能是异常状态
      final now = DateTime.now();
      final updateAge = now.difference(widget.note.updatedAt);
      
      if (updateAge.inMinutes > 5) {
        // 状态可能不一致，需要重置
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _resetInconsistentState();
        });
      }
    }
  }

  // 重置不一致的状态
  void _resetInconsistentState() {
    if (widget.onStateReset != null) {
      // 创建一个带有网络错误状态的笔记副本
      final resetNote = widget.note.copyWith(
        isUpdating: false,
        status: Note.STATUS_NETWORK_ERROR,
        errorMessage: '连接已断开，请重试',
      );
      
      // 通知父组件重置笔记状态
      widget.onStateReset!(resetNote);
    }
  }

  Widget _buildFavoriteButton(Note note) {
    return GestureDetector(
      onTap: () {
        if (note.neverDelete) {
          // 如果已经收藏，直接取消收藏
          widget.onToggleNeverDelete?.call(note);
        } else {
          // 检查笔记是否创建超过7天
          final daysSinceCreation = DateTime.now().difference(note.createdAt).inDays;
          if (daysSinceCreation < 7) {
            // 如果未满7天，显示艾宾浩斯遗忘曲线提示对话框
            showDialog(
              context: context,
              builder: (context) => const EbbinghausHintDialog(),
            );
            return;
          }

          // 如果已满7天，显示答题对话框
          showDialog(
            context: context,
            builder: (context) => PopScope(
              canPop: true,
              child: QuizDialog(
                note: note,
                onToggleNeverDelete: (note) => widget.onToggleNeverDelete?.call(note),
                withAward: true,
              ),
            ),
            barrierDismissible: true,
          ).then((completed) {
            if (completed == true) {
              widget.onToggleNeverDelete?.call(note);
            }
          });
        }
      },
      child: AnimatedSwitcher(
        duration: Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: Curves.elasticOut,
            ),
            child: child,
          );
        },
        child: Icon(
          note.neverDelete ? Icons.favorite : Icons.favorite_border,
          key: ValueKey<bool>(note.neverDelete),
          color: note.neverDelete ? Colors.red : null,
        ),
      ),
    );
  }

  String _getStatusText(Note note) {
    // 如果正在更新，直接返回更新状态
    if (note.isUpdating) {
      return AppLocalizations.instance.gettingData;
    }

    // 使用 switch 语句处理不同状态
    switch (note.status) {
      case 0:
        return note.title;
      case Note.STATUS_JSON_ERROR:
        return AppLocalizations.instance.jsonParseError;
      case Note.STATUS_HTTP_ERROR:
        return AppLocalizations.instance.networkError;
      case Note.STATUS_NETWORK_ERROR:
        return AppLocalizations.instance.networkError;
      case Note.STATUS_CONNECT_TIMEOUT:
        return AppLocalizations.instance.networkError;
      case Note.STATUS_RECEIVE_TIMEOUT:
        return AppLocalizations.instance.networkError;
      case Note.STATUS_CANCELLED:
        return AppLocalizations.instance.userCancel;
      case Note.STATUS_PENDING:
        return AppLocalizations.instance.pending;
      case Note.STATUS_NOT_LOGIN:
        return AppLocalizations.instance.notLogin;
      case Note.STATUS_QUOTA_EXCEEDED:
        return AppLocalizations.instance.quotaExceeded;
      case Note.STATUS_SERVICE_UNAVAILABLE:
        return AppLocalizations.instance.otherError;
      case Note.STATUS_UNSUPPORTED_QUESTION:
        return AppLocalizations.instance.otherError;
      default:
        return AppLocalizations.instance.otherError;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: widget.layout == TimelineLayout.single ? 8 : 2,
        vertical: widget.layout == TimelineLayout.single ? 5 : 2,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        children: [
          // 主要内容
          Material(
            color: widget.isSelected ? Theme.of(context).colorScheme.primary.withOpacity(0.05) : Colors.transparent,
            child: InkWell(
              onTap: widget.onTap,
              onLongPress: widget.onLongPress,
              borderRadius: BorderRadius.circular(12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  widget.layout == TimelineLayout.single ? _buildMainContent() : _buildGridContent(),
                  _buildProgressBar(),
                ],
              ),
            ),
          ),
          // 选中状态指示器
          if (widget.isSelected)
            Positioned(
              top: 8,
              right: 8,
              child: TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 300),
                curve: Curves.elasticOut,
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        FontAwesomeIcons.check,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  // 添加网格布局的内容区域
  Widget _buildGridContent() {
    // 使用笔记创建时间的字符串表示来计算图片比例
    final sizeIndex = widget.note.createdAt.toString().hashCode % 3;
    final selectedSize = ImageSizeConfig.imageSizes[sizeIndex];
    final aspectRatio = selectedSize['width']! / selectedSize['height']!;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 图片区域 - 使用统一的 _buildTitleImage 方法
        ClipRRect(
          borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
          child: AspectRatio(
            aspectRatio: aspectRatio,
            child: _buildTitleImage(),
          ),
        ),
        // 标题和按钮区域 - 改为水平布局
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题文本 - 左对齐
              Expanded(
                child: Text(
                  _getStatusText(widget.note),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                ),
              ),
              // 按钮 - 右侧
              widget.note.status == 0 ? _buildFavoriteButton(widget.note) : _buildStatusButtons(),
            ],
          ),
        ),
      ],
    );
  }

  // 主要内容区域
  Widget _buildMainContent() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(10, 16, 16, 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧分类图片
          _buildTitleImage(),
          SizedBox(width: 12),
          // 右侧信息区域
          Expanded(child: _buildNoteInfo()),
        ],
      ),
    );
  }

  // 标题图片
  Widget _buildTitleImage() {
    return TitleImage(
      title: widget.note.status == 0 ? widget.note.title : '',
      size: widget.layout == TimelineLayout.single ? 80 : double.infinity,
      key: ValueKey('${widget.note.id}_${widget.note.title}_${widget.note.status}'),
    );
  }

  // 笔记信息区域
  Widget _buildNoteInfo() {
    return Padding(
      padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏（标题和操作按钮）
          _buildTitleBar(),
          SizedBox(height: 8),
          // 预览内容
          _buildPreviewText(),
        ],
      ),
    );
  }

  // 标题栏
  Widget _buildTitleBar() {
    return Padding(
      padding: EdgeInsets.fromLTRB(0, 0, 10, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              _getStatusText(widget.note),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          widget.note.status == 0 ? _buildFavoriteButton(widget.note) : _buildStatusButtons(),
        ],
      ),
    );
  }

  // 状态按钮（更新中或重试按钮）
  Widget _buildStatusButtons() {
    return widget.note.isUpdating
        ? Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: widget.onCancelAI,
                child: Icon(Icons.stop),
              ),
              SizedBox(width: 8),
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
                ),
              ),
            ],
          )
        : GestureDetector(
            onTap: () => widget.onRetryAI?.call(widget.note),
            child: Icon(Icons.refresh),
          );
  }

  // 预览文本
  Widget _buildPreviewText() {
    return Text(
      _getPreviewText(widget.note.content),
      style: TextStyle(color: Colors.grey[600]),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  // 底部信息
  Widget _buildBottomInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '${AppLocalizations.instance.createdAt}: ${_formatDate(widget.note.createdAt)}',
          style: TextStyle(color: Colors.grey[400], fontSize: 12),
        ),
        Row(
          children: [
            if (widget.note.isUpdating) ...[
              Text(
                AppLocalizations.instance.waitingForAIResponseLast(_getWaitingTime()),
                style: TextStyle(color: Colors.grey[400], fontSize: 12),
              ),
              SizedBox(width: 4),
            ],
            Text(
              _getRemainingLifeText(context),
              style: TextStyle(color: Colors.grey[400], fontSize: 12),
            ),
          ],
        ),
      ],
    );
  }

  // 进度条
  Widget _buildProgressBar() {
    return Column(
      children: [
        // 底部信息栏（创建时间和等待时间）
        widget.layout == TimelineLayout.single
            ? Padding(
                padding: EdgeInsets.fromLTRB(10, 0, 10, 2),
                child: _buildBottomInfo(),
              )
            : Container(),
        // 重新设计进度条
        Align(
          alignment: Alignment.bottomLeft,
          child: Container(
            height: 4,
            margin: EdgeInsets.only(bottom: 1),
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(bottom: Radius.circular(11)),
              color: Colors.grey[200],
            ),
            clipBehavior: Clip.antiAlias,
            child: widget.note.neverDelete
                ? Container(
                    decoration: BoxDecoration(
                      color: Colors.green,
                    ),
                  )
                : Row(
                    children: [
                      // 已经过去的时间（红色）
                      Expanded(
                        flex: (_calculateProgress(context, widget.note.createdAt) * 1000).round(),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.red,
                          ),
                        ),
                      ),
                      // 剩余时间（绿色）
                      Expanded(
                        flex: ((1.0 - _calculateProgress(context, widget.note.createdAt)) * 1000).round(),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.green,
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  String _getWaitingTime() {
    if (_statusChangedAt == null) return '0';

    final now = DateTime.now();
    final waitingDuration = now.difference(_statusChangedAt!);
    return waitingDuration.inSeconds.toString();
  }

  String _getPreviewText(List<StyledTextSegment> content) {
    // 1. 先将所有文本片段连接起来
    String fullText = content.map((segment) => segment.text).join();

    // 2. 处理文本：将连续的换行符和空格替换为单个空格
    fullText = fullText.replaceAll(RegExp(r'\s+'), ' ').trim();

    // 3. 基础处理
    fullText = TextUtil.processText(fullText);

    // 4. 如果处理后的文本长度小于100，直接返回
    // 如果大于100，截取前100个字符并添加省略号
    return fullText.length > 100 ? '${fullText.substring(0, 100)}...' : fullText;
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  double _calculateProgress(BuildContext context, DateTime createdAt) {
    // 使用 Get.find 获取 AppSettings 实例
    final settings = Get.find<AppSettings>();
    final now = DateTime.now();

    // 使用分钟作为计算单位，确保更精确
    final ageInMinutes = now.difference(createdAt).inMinutes;
    final maxLifetimeMinutes = settings.defaultDeleteDays * 24 * 60;

    // 计算已经过去的时间比例
    final progress = ageInMinutes / maxLifetimeMinutes;

    // 确保进度在0.0到1.0之间
    return progress.clamp(0.0, 1.0);
  }


  String _getRemainingLifeText(BuildContext context) {
    if (widget.note.neverDelete) return '永久保存';

    // 使用 Get.find 获取 AppSettings 实例
    final settings = Get.find<AppSettings>();
    final now = DateTime.now();
    final age = now.difference(widget.note.createdAt).inMinutes;
    final maxLifetimeMinutes = settings.defaultDeleteDays * 24 * 60;

    final remainingMinutes = maxLifetimeMinutes - age;
    if (remainingMinutes <= 0) return '即将删除';

    final days = remainingMinutes ~/ (24 * 60);
    final hours = (remainingMinutes % (24 * 60)) ~/ 60;
    final minutes = remainingMinutes % 60;

    if (days > 0) {
      return '$days ${AppLocalizations.instance.daysLaterDelete}';
    } else if (hours > 0) {
      return '$hours ${AppLocalizations.instance.hoursLaterDelete}';
    } else {
      return '$minutes ${AppLocalizations.instance.minutesLaterDelete}';
    }
  }
}
