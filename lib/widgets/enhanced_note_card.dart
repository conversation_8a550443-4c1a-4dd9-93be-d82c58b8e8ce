// Enhanced note card with comprehensive error handling and offline support
// Provides better user feedback and handles all error states gracefully

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/note.dart';
import '../core/error_handling/app_error.dart';
import '../core/error_handling/error_handler_service.dart';
import '../core/connectivity/connectivity_service.dart';
import '../core/offline/offline_queue_manager.dart';
import '../l10n/localization.dart';
import '../services/enhanced_ai_service.dart';
import '../widgets/countdown_progress_bar.dart';
import '../constants/layout_config.dart';

/// Enhanced note card with comprehensive error handling
class EnhancedNoteCard extends StatefulWidget {
  final Note note;
  final bool isSelected;
  final TimelineLayout layout;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Function(Note)? onToggleNeverDelete;
  final Function(Note)? onRetryAI;
  final VoidCallback? onCancelAI;
  final Widget? child;

  const EnhancedNoteCard({
    super.key,
    required this.note,
    this.isSelected = false,
    this.layout = TimelineLayout.single,
    this.onTap,
    this.onLongPress,
    this.onToggleNeverDelete,
    this.onRetryAI,
    this.onCancelAI,
    this.child,
  });

  @override
  State<EnhancedNoteCard> createState() => _EnhancedNoteCardState();
}

class _EnhancedNoteCardState extends State<EnhancedNoteCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  final ConnectivityService _connectivity = ConnectivityService.instance;
  final OfflineQueueManager _offlineQueue = OfflineQueueManager.instance;
  final ErrorHandlerService _errorHandler = ErrorHandlerService.instance;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.note.isUpdating) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(EnhancedNoteCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.note.isUpdating && !oldWidget.note.isUpdating) {
      _animationController.repeat(reverse: true);
    } else if (!widget.note.isUpdating && oldWidget.note.isUpdating) {
      _animationController.stop();
      _animationController.reset();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: widget.isSelected ? 8 : 2,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: widget.isSelected ? BorderSide(color: Theme.of(context).primaryColor, width: 2) : BorderSide.none,
      ),
      child: InkWell(
        onTap: widget.onTap,
        onLongPress: widget.onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            _buildMainContent(),
            if (widget.child != null) widget.child!,
            _buildErrorDisplay(),
            _buildOfflineIndicator(),
          ],
        ),
      ),
    );
  }

  /// Build the header with title and action buttons
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getHeaderColor(),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: widget.note.isUpdating ? _pulseAnimation.value : 1.0,
                  child: Text(
                    _getDisplayTitle(),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: _getTitleColor(),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 8),
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// Build main content area
  Widget _buildMainContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCategoryInfo(),
          const SizedBox(height: 8),
          _buildPreviewText(),
          const SizedBox(height: 12),
          _buildMetadata(),
        ],
      ),
    );
  }

  /// Build category information
  Widget _buildCategoryInfo() {
    if (widget.note.category.isEmpty) return const SizedBox.shrink();

    return Wrap(
      spacing: 4,
      children: [
        _buildCategoryChip(widget.note.category, Colors.blue),
        if (widget.note.subCategory.isNotEmpty) _buildCategoryChip(widget.note.subCategory, Colors.green),
        if (widget.note.subSubCategory.isNotEmpty) _buildCategoryChip(widget.note.subSubCategory, Colors.orange),
      ],
    );
  }

  /// Build category chip
  Widget _buildCategoryChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          color: color.withOpacity(0.8),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Build preview text
  Widget _buildPreviewText() {
    if (widget.note.content.isEmpty) return const SizedBox.shrink();

    final previewText =
        widget.note.content.map((segment) => segment.text).join(' ').replaceAll(RegExp(r'\s+'), ' ').trim();

    if (previewText.isEmpty) return const SizedBox.shrink();

    return Text(
      previewText,
      style: TextStyle(
        fontSize: 14,
        color: Colors.grey[600],
        height: 1.4,
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// Build metadata (creation time, etc.)
  Widget _buildMetadata() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '${AppLocalizations.instance.createdAt}: ${_formatDate(widget.note.createdAt)}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
        if (widget.note.isUpdating) _buildWaitingTime(),
      ],
    );
  }

  /// Build waiting time indicator
  Widget _buildWaitingTime() {
    return Text(
      AppLocalizations.instance.waitingForAIResponseLast(_getWaitingTime()),
      style: TextStyle(
        fontSize: 12,
        color: Colors.grey[500],
      ),
    );
  }

  /// Build error display section
  Widget _buildErrorDisplay() {
    if (widget.note.status == Note.STATUS_SUCCESS || widget.note.isUpdating) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getErrorBackgroundColor(),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getErrorBorderColor()),
      ),
      child: Row(
        children: [
          Icon(
            _getErrorIcon(),
            color: _getErrorIconColor(),
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getErrorTitle(),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: _getErrorTextColor(),
                  ),
                ),
                if (widget.note.errorMessage.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    widget.note.errorMessage,
                    style: TextStyle(
                      fontSize: 12,
                      color: _getErrorTextColor().withOpacity(0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (_canRetry()) ...[
            const SizedBox(width: 8),
            _buildRetryButton(),
          ],
        ],
      ),
    );
  }

  /// Build offline indicator
  Widget _buildOfflineIndicator() {
    if (_connectivity.isOnline) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.cloud_off,
            color: Colors.orange[700],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Offline - Operations will be synced when connection is restored',
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build action buttons (retry, cancel, favorite)
  Widget _buildActionButtons() {
    if (widget.note.status == Note.STATUS_SUCCESS) {
      return _buildFavoriteButton();
    }

    if (widget.note.isUpdating) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: widget.onCancelAI,
            icon: const Icon(Icons.stop),
            iconSize: 20,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
          const SizedBox(width: 4),
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      );
    }

    if (_canRetry()) {
      return IconButton(
        onPressed: () => _handleRetry(),
        icon: const Icon(Icons.refresh),
        iconSize: 20,
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
      );
    }

    return const SizedBox.shrink();
  }

  /// Build favorite button
  Widget _buildFavoriteButton() {
    return IconButton(
      onPressed: () => widget.onToggleNeverDelete?.call(widget.note),
      icon: Icon(
        widget.note.neverDelete ? Icons.favorite : Icons.favorite_border,
        color: widget.note.neverDelete ? Colors.red : null,
      ),
      iconSize: 20,
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
    );
  }

  /// Build retry button
  Widget _buildRetryButton() {
    return ElevatedButton.icon(
      onPressed: () => _handleRetry(),
      icon: const Icon(Icons.refresh, size: 16),
      label: const Text('Retry'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  /// Handle retry action
  void _handleRetry() {
    try {
      widget.onRetryAI?.call(widget.note);
    } catch (error) {
      _errorHandler.handleError(
        error,
        context: 'Note card retry',
        onRetry: () => _handleRetry(),
      );
    }
  }

  /// Get display title based on note status
  String _getDisplayTitle() {
    if (widget.note.isUpdating) {
      return AppLocalizations.instance.gettingData;
    }

    if (widget.note.status == Note.STATUS_SUCCESS) {
      return widget.note.title;
    }

    return _getErrorTitle();
  }

  /// Get error title based on status
  String _getErrorTitle() {
    switch (widget.note.status) {
      case Note.STATUS_JSON_ERROR:
        return 'Data Processing Error';
      case Note.STATUS_HTTP_ERROR:
        return 'Request Failed';
      case Note.STATUS_NETWORK_ERROR:
        return 'Network Error';
      case Note.STATUS_CANCELLED:
        return 'Cancelled';
      case Note.STATUS_PENDING:
        return 'Pending';
      case Note.STATUS_NOT_LOGIN:
        return 'Authentication Required';
      case Note.STATUS_QUOTA_EXCEEDED:
        return 'Quota Exceeded';
      case Note.STATUS_SERVICE_UNAVAILABLE:
        return 'Service Unavailable';
      case Note.STATUS_CONNECT_TIMEOUT:
        return 'Connection Timeout';
      case Note.STATUS_RECEIVE_TIMEOUT:
        return 'Response Timeout';
      case Note.STATUS_UNSUPPORTED_QUESTION:
        return 'Unsupported Request';
      default:
        return 'Error';
    }
  }

  /// Get header background color
  Color _getHeaderColor() {
    if (widget.note.isUpdating) {
      return Colors.blue.withOpacity(0.1);
    }

    if (widget.note.status == Note.STATUS_SUCCESS) {
      return Colors.transparent;
    }

    return _getErrorBackgroundColor();
  }

  /// Get title text color
  Color _getTitleColor() {
    if (widget.note.status == Note.STATUS_SUCCESS) {
      return Colors.black87;
    }

    return _getErrorTextColor();
  }

  /// Get error background color
  Color _getErrorBackgroundColor() {
    switch (widget.note.status) {
      case Note.STATUS_CANCELLED:
        return Colors.grey.withOpacity(0.1);
      case Note.STATUS_QUOTA_EXCEEDED:
      case Note.STATUS_NOT_LOGIN:
        return Colors.orange.withOpacity(0.1);
      default:
        return Colors.red.withOpacity(0.1);
    }
  }

  /// Get error border color
  Color _getErrorBorderColor() {
    switch (widget.note.status) {
      case Note.STATUS_CANCELLED:
        return Colors.grey.withOpacity(0.3);
      case Note.STATUS_QUOTA_EXCEEDED:
      case Note.STATUS_NOT_LOGIN:
        return Colors.orange.withOpacity(0.3);
      default:
        return Colors.red.withOpacity(0.3);
    }
  }

  /// Get error text color
  Color _getErrorTextColor() {
    switch (widget.note.status) {
      case Note.STATUS_CANCELLED:
        return Colors.grey[700]!;
      case Note.STATUS_QUOTA_EXCEEDED:
      case Note.STATUS_NOT_LOGIN:
        return Colors.orange[700]!;
      default:
        return Colors.red[700]!;
    }
  }

  /// Get error icon
  IconData _getErrorIcon() {
    switch (widget.note.status) {
      case Note.STATUS_CANCELLED:
        return Icons.cancel_outlined;
      case Note.STATUS_NOT_LOGIN:
        return Icons.login;
      case Note.STATUS_QUOTA_EXCEEDED:
        return Icons.warning_outlined;
      case Note.STATUS_NETWORK_ERROR:
      case Note.STATUS_CONNECT_TIMEOUT:
      case Note.STATUS_RECEIVE_TIMEOUT:
        return Icons.wifi_off;
      default:
        return Icons.error_outline;
    }
  }

  /// Get error icon color
  Color _getErrorIconColor() {
    return _getErrorTextColor();
  }

  /// Check if note can be retried
  bool _canRetry() {
    switch (widget.note.status) {
      case Note.STATUS_NETWORK_ERROR:
      case Note.STATUS_HTTP_ERROR:
      case Note.STATUS_CONNECT_TIMEOUT:
      case Note.STATUS_RECEIVE_TIMEOUT:
      case Note.STATUS_JSON_ERROR:
        return true;
      default:
        return false;
    }
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Get waiting time for AI response
  String _getWaitingTime() {
    // This would need to be implemented based on when the request started
    // For now, return a placeholder
    return '30';
  }
}
