// Enhanced loading state management with progress tracking
// Provides comprehensive loading indicators and user feedback

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Types of loading operations
enum LoadingType {
  aiRequest,
  dataSync,
  fileUpload,
  authentication,
  general,
}

/// Loading state information
class LoadingState {
  final String id;
  final LoadingType type;
  final String message;
  final double? progress;
  final DateTime startTime;
  final Duration? estimatedDuration;
  final bool isCancellable;
  final VoidCallback? onCancel;

  const LoadingState({
    required this.id,
    required this.type,
    required this.message,
    this.progress,
    required this.startTime,
    this.estimatedDuration,
    this.isCancellable = false,
    this.onCancel,
  });

  LoadingState copyWith({
    String? id,
    LoadingType? type,
    String? message,
    double? progress,
    DateTime? startTime,
    Duration? estimatedDuration,
    bool? isCancellable,
    VoidCallback? onCancel,
  }) {
    return LoadingState(
      id: id ?? this.id,
      type: type ?? this.type,
      message: message ?? this.message,
      progress: progress ?? this.progress,
      startTime: startTime ?? this.startTime,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      isCancellable: isCancellable ?? this.isCancellable,
      onCancel: onCancel ?? this.onCancel,
    );
  }

  Duration get elapsed => DateTime.now().difference(startTime);
  
  double? get estimatedProgress {
    if (estimatedDuration == null) return null;
    final ratio = elapsed.inMilliseconds / estimatedDuration!.inMilliseconds;
    return (ratio * 100).clamp(0.0, 95.0); // Never show 100% until actually complete
  }

  String get elapsedTimeString {
    final seconds = elapsed.inSeconds;
    if (seconds < 60) {
      return '${seconds}s';
    } else {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return '${minutes}m ${remainingSeconds}s';
    }
  }
}

/// Enhanced loading state manager
class LoadingStateManager extends GetxController {
  static LoadingStateManager get instance => Get.find<LoadingStateManager>();

  final Map<String, LoadingState> _loadingStates = {};
  final StreamController<Map<String, LoadingState>> _stateController = 
      StreamController<Map<String, LoadingState>>.broadcast();

  Timer? _progressTimer;

  // Getters
  Map<String, LoadingState> get loadingStates => Map.unmodifiable(_loadingStates);
  Stream<Map<String, LoadingState>> get stateStream => _stateController.stream;
  bool get hasActiveLoading => _loadingStates.isNotEmpty;
  int get activeLoadingCount => _loadingStates.length;

  @override
  void onInit() {
    super.onInit();
    _startProgressTimer();
  }

  @override
  void onClose() {
    _progressTimer?.cancel();
    _stateController.close();
    super.onClose();
  }

  /// Start a loading operation
  String startLoading({
    required LoadingType type,
    required String message,
    Duration? estimatedDuration,
    bool isCancellable = false,
    VoidCallback? onCancel,
  }) {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    
    final loadingState = LoadingState(
      id: id,
      type: type,
      message: message,
      startTime: DateTime.now(),
      estimatedDuration: estimatedDuration,
      isCancellable: isCancellable,
      onCancel: onCancel,
    );

    _loadingStates[id] = loadingState;
    _notifyStateChange();

    return id;
  }

  /// Update loading progress
  void updateProgress(String id, {
    String? message,
    double? progress,
  }) {
    final currentState = _loadingStates[id];
    if (currentState == null) return;

    _loadingStates[id] = currentState.copyWith(
      message: message,
      progress: progress,
    );
    
    _notifyStateChange();
  }

  /// Complete a loading operation
  void completeLoading(String id) {
    _loadingStates.remove(id);
    _notifyStateChange();
  }

  /// Cancel a loading operation
  void cancelLoading(String id) {
    final loadingState = _loadingStates[id];
    if (loadingState?.isCancellable == true) {
      loadingState?.onCancel?.call();
      _loadingStates.remove(id);
      _notifyStateChange();
    }
  }

  /// Cancel all loading operations
  void cancelAllLoading() {
    final cancellableStates = _loadingStates.values
        .where((state) => state.isCancellable)
        .toList();

    for (final state in cancellableStates) {
      state.onCancel?.call();
    }

    _loadingStates.clear();
    _notifyStateChange();
  }

  /// Get loading state by ID
  LoadingState? getLoadingState(String id) {
    return _loadingStates[id];
  }

  /// Get loading states by type
  List<LoadingState> getLoadingStatesByType(LoadingType type) {
    return _loadingStates.values
        .where((state) => state.type == type)
        .toList();
  }

  /// Check if specific type is loading
  bool isTypeLoading(LoadingType type) {
    return _loadingStates.values.any((state) => state.type == type);
  }

  /// Start progress timer for estimated durations
  void _startProgressTimer() {
    _progressTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      bool hasChanges = false;
      
      for (final entry in _loadingStates.entries) {
        final state = entry.value;
        if (state.estimatedDuration != null) {
          hasChanges = true;
          break;
        }
      }

      if (hasChanges) {
        _notifyStateChange();
      }
    });
  }

  /// Notify state change
  void _notifyStateChange() {
    _stateController.add(Map.from(_loadingStates));
  }

  /// Get loading statistics
  Map<String, dynamic> getLoadingStats() {
    final typeStats = <String, int>{};
    for (final state in _loadingStates.values) {
      typeStats[state.type.name] = (typeStats[state.type.name] ?? 0) + 1;
    }

    return {
      'totalActive': _loadingStates.length,
      'typeBreakdown': typeStats,
      'longestRunning': _loadingStates.values.isNotEmpty
          ? _loadingStates.values
              .reduce((a, b) => a.elapsed > b.elapsed ? a : b)
              .elapsedTimeString
          : null,
    };
  }
}

/// Enhanced loading indicator widget
class EnhancedLoadingIndicator extends StatelessWidget {
  final LoadingState loadingState;
  final bool showProgress;
  final bool showElapsedTime;
  final bool showCancel;

  const EnhancedLoadingIndicator({
    super.key,
    required this.loadingState,
    this.showProgress = true,
    this.showElapsedTime = true,
    this.showCancel = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          const SizedBox(height: 12),
          _buildProgressSection(context),
          if (showElapsedTime || (showCancel && loadingState.isCancellable)) ...[
            const SizedBox(height: 12),
            _buildFooter(context),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        _buildTypeIcon(),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            loadingState.message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTypeIcon() {
    IconData icon;
    Color color;

    switch (loadingState.type) {
      case LoadingType.aiRequest:
        icon = Icons.psychology;
        color = Colors.blue;
        break;
      case LoadingType.dataSync:
        icon = Icons.sync;
        color = Colors.green;
        break;
      case LoadingType.fileUpload:
        icon = Icons.cloud_upload;
        color = Colors.orange;
        break;
      case LoadingType.authentication:
        icon = Icons.login;
        color = Colors.purple;
        break;
      case LoadingType.general:
      default:
        icon = Icons.hourglass_empty;
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(icon, color: color, size: 24),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    if (!showProgress) return const SizedBox.shrink();

    return Column(
      children: [
        _buildProgressIndicator(),
        if (loadingState.progress != null || loadingState.estimatedProgress != null) ...[
          const SizedBox(height: 8),
          _buildProgressText(context),
        ],
      ],
    );
  }

  Widget _buildProgressIndicator() {
    final progress = loadingState.progress ?? loadingState.estimatedProgress;
    
    if (progress != null) {
      return LinearProgressIndicator(
        value: progress / 100,
        backgroundColor: Colors.grey[300],
        valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor()),
      );
    } else {
      return LinearProgressIndicator(
        backgroundColor: Colors.grey[300],
        valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor()),
      );
    }
  }

  Widget _buildProgressText(BuildContext context) {
    final progress = loadingState.progress ?? loadingState.estimatedProgress;
    if (progress == null) return const SizedBox.shrink();

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '${progress.toStringAsFixed(0)}%',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        if (loadingState.estimatedDuration != null)
          Text(
            'Est. ${_formatDuration(loadingState.estimatedDuration!)}',
            style: Theme.of(context).textTheme.bodySmall,
          ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (showElapsedTime)
          Text(
            'Elapsed: ${loadingState.elapsedTimeString}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        if (showCancel && loadingState.isCancellable)
          TextButton.icon(
            onPressed: () => LoadingStateManager.instance.cancelLoading(loadingState.id),
            icon: const Icon(Icons.cancel, size: 16),
            label: const Text('Cancel'),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
      ],
    );
  }

  Color _getProgressColor() {
    switch (loadingState.type) {
      case LoadingType.aiRequest:
        return Colors.blue;
      case LoadingType.dataSync:
        return Colors.green;
      case LoadingType.fileUpload:
        return Colors.orange;
      case LoadingType.authentication:
        return Colors.purple;
      case LoadingType.general:
      default:
        return Colors.grey;
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    
    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
}

/// Global loading overlay widget
class GlobalLoadingOverlay extends StatelessWidget {
  final Widget child;

  const GlobalLoadingOverlay({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        StreamBuilder<Map<String, LoadingState>>(
          stream: LoadingStateManager.instance.stateStream,
          builder: (context, snapshot) {
            final loadingStates = snapshot.data ?? {};
            
            if (loadingStates.isEmpty) {
              return const SizedBox.shrink();
            }

            return Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: loadingStates.values
                          .map((state) => Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: EnhancedLoadingIndicator(loadingState: state),
                              ))
                          .toList(),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
