// Enterprise-grade connectivity monitoring and management
// Implements patterns used by major mobile apps for robust offline support

import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../error_handling/app_error.dart';

/// Network connectivity states
enum ConnectivityState {
  online,
  offline,
  limited, // Connected but with limited internet access
  unknown,
}

/// Network quality levels based on connection speed and reliability
enum NetworkQuality {
  excellent, // Fast, reliable connection
  good, // Good connection with minor delays
  poor, // Slow connection, may have issues
  unavailable, // No connection
}

/// Connectivity information with detailed metrics
class ConnectivityInfo {
  final ConnectivityState state;
  final NetworkQuality quality;
  final ConnectivityResult connectionType;
  final Duration? latency;
  final DateTime timestamp;
  final bool isMetered;

  const ConnectivityInfo({
    required this.state,
    required this.quality,
    required this.connectionType,
    this.latency,
    required this.timestamp,
    this.isMetered = false,
  });

  bool get isOnline => state == ConnectivityState.online;
  bool get isOffline => state == ConnectivityState.offline;
  bool get hasLimitedConnectivity => state == ConnectivityState.limited;

  Map<String, dynamic> toJson() => {
        'state': state.name,
        'quality': quality.name,
        'connectionType': connectionType.name,
        'latency': latency?.inMilliseconds,
        'timestamp': timestamp.toIso8601String(),
        'isMetered': isMetered,
      };

  @override
  String toString() => 'ConnectivityInfo(state: $state, quality: $quality, type: $connectionType)';
}

/// Connectivity service for monitoring and managing network state
class ConnectivityService extends GetxService {
  static ConnectivityService get instance => Get.find<ConnectivityService>();

  final Connectivity _connectivity = Connectivity();

  // Reactive state management
  final Rx<ConnectivityInfo> _currentConnectivity = ConnectivityInfo(
    state: ConnectivityState.unknown,
    quality: NetworkQuality.unavailable,
    connectionType: ConnectivityResult.none,
    timestamp: DateTime.now(),
  ).obs;

  // Stream controllers for connectivity events
  final StreamController<ConnectivityInfo> _connectivityController = StreamController<ConnectivityInfo>.broadcast();

  // Configuration
  static const Duration _checkInterval = Duration(seconds: 30);
  static const Duration _fastCheckInterval = Duration(seconds: 5);
  static const List<String> _testHosts = [
    'google.com',
    'cloudflare.com',
    '*******',
  ];

  Timer? _periodicTimer;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  bool _isInitialized = false;

  // Getters
  ConnectivityInfo get currentConnectivity => _currentConnectivity.value;
  Stream<ConnectivityInfo> get connectivityStream => _connectivityController.stream;
  bool get isOnline => _currentConnectivity.value.isOnline;
  bool get isOffline => _currentConnectivity.value.isOffline;
  NetworkQuality get networkQuality => _currentConnectivity.value.quality;

  @override
  Future<void> onInit() async {
    super.onInit();
    await initialize();
  }

  @override
  void onClose() {
    _periodicTimer?.cancel();
    _connectivitySubscription?.cancel();
    _connectivityController.close();
    super.onClose();
  }

  /// Initialize the connectivity service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Get initial connectivity state
      await _updateConnectivityState();

      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _onConnectivityChanged,
        onError: (error) {
          if (kDebugMode) {
            print('Connectivity subscription error: $error');
          }
        },
      );

      // Start periodic connectivity checks
      _startPeriodicChecks();

      _isInitialized = true;

      if (kDebugMode) {
        print('ConnectivityService initialized: ${_currentConnectivity.value}');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Failed to initialize ConnectivityService: $error');
      }
      rethrow;
    }
  }

  /// Handle connectivity changes from the system
  void _onConnectivityChanged(ConnectivityResult result) async {
    if (kDebugMode) {
      print('Connectivity changed to: $result');
    }

    await _updateConnectivityState();

    // If we detect a connection, do a quick verification
    if (result != ConnectivityResult.none) {
      // Use faster checks when connectivity changes
      _restartPeriodicChecks(useFastInterval: true);
    }
  }

  /// Update the current connectivity state with detailed information
  Future<void> _updateConnectivityState() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      final connectivityInfo = await _buildConnectivityInfo(connectivityResult);

      final previousState = _currentConnectivity.value.state;
      _currentConnectivity.value = connectivityInfo;

      // Emit connectivity change event
      _connectivityController.add(connectivityInfo);

      // Log state changes
      if (previousState != connectivityInfo.state && kDebugMode) {
        print('Connectivity state changed: $previousState -> ${connectivityInfo.state}');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error updating connectivity state: $error');
      }

      // Fallback to offline state on error
      final fallbackInfo = ConnectivityInfo(
        state: ConnectivityState.offline,
        quality: NetworkQuality.unavailable,
        connectionType: ConnectivityResult.none,
        timestamp: DateTime.now(),
      );

      _currentConnectivity.value = fallbackInfo;
      _connectivityController.add(fallbackInfo);
    }
  }

  /// Build detailed connectivity information
  Future<ConnectivityInfo> _buildConnectivityInfo(ConnectivityResult result) async {
    if (result == ConnectivityResult.none) {
      return ConnectivityInfo(
        state: ConnectivityState.offline,
        quality: NetworkQuality.unavailable,
        connectionType: result,
        timestamp: DateTime.now(),
      );
    }

    // Test actual internet connectivity
    final internetTest = await _testInternetConnectivity();
    final latency = internetTest['latency'] as Duration?;
    final hasInternet = internetTest['hasInternet'] as bool;

    ConnectivityState state;
    NetworkQuality quality;

    if (!hasInternet) {
      state = ConnectivityState.limited;
      quality = NetworkQuality.unavailable;
    } else {
      state = ConnectivityState.online;
      quality = _determineNetworkQuality(result, latency);
    }

    return ConnectivityInfo(
      state: state,
      quality: quality,
      connectionType: result,
      latency: latency,
      timestamp: DateTime.now(),
      isMetered: _isMeteredConnection(result),
    );
  }

  /// Test actual internet connectivity by pinging reliable hosts
  Future<Map<String, dynamic>> _testInternetConnectivity() async {
    Duration? bestLatency;
    bool hasInternet = false;

    for (final host in _testHosts) {
      try {
        final stopwatch = Stopwatch()..start();

        final result = await InternetAddress.lookup(host).timeout(const Duration(seconds: 5));

        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
          stopwatch.stop();
          final latency = stopwatch.elapsed;

          hasInternet = true;
          if (bestLatency == null || latency < bestLatency) {
            bestLatency = latency;
          }

          // If we get a fast response, no need to test other hosts
          if (latency.inMilliseconds < 1000) {
            break;
          }
        }
      } catch (error) {
        // Continue testing other hosts
        if (kDebugMode) {
          print('Failed to reach $host: $error');
        }
      }
    }

    return {
      'hasInternet': hasInternet,
      'latency': bestLatency,
    };
  }

  /// Determine network quality based on connection type and latency
  NetworkQuality _determineNetworkQuality(ConnectivityResult result, Duration? latency) {
    if (latency == null) {
      return NetworkQuality.poor;
    }

    final latencyMs = latency.inMilliseconds;

    // Base quality on connection type
    NetworkQuality baseQuality;
    switch (result) {
      case ConnectivityResult.wifi:
        baseQuality = NetworkQuality.excellent;
        break;
      case ConnectivityResult.mobile:
        baseQuality = NetworkQuality.good;
        break;
      case ConnectivityResult.ethernet:
        baseQuality = NetworkQuality.excellent;
        break;
      default:
        baseQuality = NetworkQuality.poor;
    }

    // Adjust based on latency
    if (latencyMs < 100) {
      return baseQuality;
    } else if (latencyMs < 500) {
      return baseQuality == NetworkQuality.excellent ? NetworkQuality.good : NetworkQuality.poor;
    } else if (latencyMs < 2000) {
      return NetworkQuality.poor;
    } else {
      return NetworkQuality.unavailable;
    }
  }

  /// Check if the connection is metered (mobile data, limited wifi, etc.)
  bool _isMeteredConnection(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.mobile:
        return true;
      case ConnectivityResult.wifi:
      case ConnectivityResult.ethernet:
        return false;
      default:
        return true; // Assume metered for unknown types
    }
  }

  /// Start periodic connectivity checks
  void _startPeriodicChecks() {
    _periodicTimer?.cancel();
    _periodicTimer = Timer.periodic(_checkInterval, (_) {
      _updateConnectivityState();
    });
  }

  /// Restart periodic checks with optional fast interval
  void _restartPeriodicChecks({bool useFastInterval = false}) {
    _periodicTimer?.cancel();

    final interval = useFastInterval ? _fastCheckInterval : _checkInterval;
    _periodicTimer = Timer.periodic(interval, (_) {
      _updateConnectivityState();
    });

    // Switch back to normal interval after a few fast checks
    if (useFastInterval) {
      Timer(const Duration(seconds: 30), () {
        _startPeriodicChecks();
      });
    }
  }

  /// Force a connectivity check
  Future<ConnectivityInfo> forceCheck() async {
    await _updateConnectivityState();
    return _currentConnectivity.value;
  }

  /// Wait for online connectivity
  Future<ConnectivityInfo> waitForOnline({Duration? timeout}) async {
    if (isOnline) {
      return _currentConnectivity.value;
    }

    final completer = Completer<ConnectivityInfo>();
    StreamSubscription<ConnectivityInfo>? subscription;

    subscription = connectivityStream.listen((info) {
      if (info.isOnline) {
        subscription?.cancel();
        if (!completer.isCompleted) {
          completer.complete(info);
        }
      }
    });

    // Set timeout if provided
    if (timeout != null) {
      Timer(timeout, () {
        subscription?.cancel();
        if (!completer.isCompleted) {
          completer.completeError(
            NetworkError(
              type: NetworkErrorType.timeout,
              code: 'CONNECTIVITY_TIMEOUT',
              message: 'Timeout waiting for online connectivity',
              userMessage: 'Unable to establish internet connection. Please check your network settings.',
              timeout: timeout,
            ),
          );
        }
      });
    }

    return completer.future;
  }

  /// Check if network quality is sufficient for operation
  bool isQualitySufficient(NetworkQuality requiredQuality) {
    final currentQualityIndex = NetworkQuality.values.indexOf(networkQuality);
    final requiredQualityIndex = NetworkQuality.values.indexOf(requiredQuality);
    return currentQualityIndex <= requiredQualityIndex;
  }

  /// Get connectivity statistics for monitoring
  Map<String, dynamic> getConnectivityStats() {
    return {
      'current': _currentConnectivity.value.toJson(),
      'isInitialized': _isInitialized,
      'hasActiveTimer': _periodicTimer?.isActive ?? false,
      'hasActiveSubscription': _connectivitySubscription != null,
    };
  }
}
