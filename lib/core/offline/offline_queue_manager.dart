// Enterprise-grade offline queue management
// Implements patterns used by major apps for reliable offline-first functionality

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../connectivity/connectivity_service.dart';
import '../error_handling/app_error.dart';
import '../error_handling/retry_manager.dart';

/// Types of operations that can be queued
enum OperationType {
  createNote,
  updateNote,
  deleteNote,
  askAI,
  generateQuiz,
  uploadData,
}

/// Priority levels for queued operations
enum OperationPriority {
  low,
  normal,
  high,
  critical,
}

/// Status of a queued operation
enum OperationStatus {
  pending,
  inProgress,
  completed,
  failed,
  cancelled,
}

/// A queued operation with all necessary information for execution
class QueuedOperation {
  final String id;
  final OperationType type;
  final OperationPriority priority;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  final int retryCount;
  final int maxRetries;
  final OperationStatus status;
  final String? errorMessage;
  final Map<String, dynamic> context;

  const QueuedOperation({
    required this.id,
    required this.type,
    required this.priority,
    required this.data,
    required this.createdAt,
    this.scheduledAt,
    this.retryCount = 0,
    this.maxRetries = 3,
    this.status = OperationStatus.pending,
    this.errorMessage,
    this.context = const {},
  });

  QueuedOperation copyWith({
    String? id,
    OperationType? type,
    OperationPriority? priority,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    DateTime? scheduledAt,
    int? retryCount,
    int? maxRetries,
    OperationStatus? status,
    String? errorMessage,
    Map<String, dynamic>? context,
  }) {
    return QueuedOperation(
      id: id ?? this.id,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      retryCount: retryCount ?? this.retryCount,
      maxRetries: maxRetries ?? this.maxRetries,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      context: context ?? this.context,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.name,
    'priority': priority.name,
    'data': data,
    'createdAt': createdAt.toIso8601String(),
    'scheduledAt': scheduledAt?.toIso8601String(),
    'retryCount': retryCount,
    'maxRetries': maxRetries,
    'status': status.name,
    'errorMessage': errorMessage,
    'context': context,
  };

  factory QueuedOperation.fromJson(Map<String, dynamic> json) {
    return QueuedOperation(
      id: json['id'],
      type: OperationType.values.firstWhere((e) => e.name == json['type']),
      priority: OperationPriority.values.firstWhere((e) => e.name == json['priority']),
      data: Map<String, dynamic>.from(json['data']),
      createdAt: DateTime.parse(json['createdAt']),
      scheduledAt: json['scheduledAt'] != null ? DateTime.parse(json['scheduledAt']) : null,
      retryCount: json['retryCount'] ?? 0,
      maxRetries: json['maxRetries'] ?? 3,
      status: OperationStatus.values.firstWhere((e) => e.name == json['status']),
      errorMessage: json['errorMessage'],
      context: Map<String, dynamic>.from(json['context'] ?? {}),
    );
  }

  bool get canRetry => retryCount < maxRetries && status == OperationStatus.failed;
  bool get isExpired => DateTime.now().difference(createdAt).inDays > 7; // Expire after 7 days
  
  int get priorityValue {
    switch (priority) {
      case OperationPriority.critical:
        return 4;
      case OperationPriority.high:
        return 3;
      case OperationPriority.normal:
        return 2;
      case OperationPriority.low:
        return 1;
    }
  }
}

/// Callback function type for operation execution
typedef OperationExecutor = Future<dynamic> Function(QueuedOperation operation);

/// Offline queue manager for handling operations when offline
class OfflineQueueManager extends GetxService {
  static OfflineQueueManager get instance => Get.find<OfflineQueueManager>();

  static const String _queueKey = 'offline_operation_queue';
  static const String _statsKey = 'offline_queue_stats';
  
  final List<QueuedOperation> _queue = [];
  final Map<OperationType, OperationExecutor> _executors = {};
  final StreamController<QueuedOperation> _operationController = 
      StreamController<QueuedOperation>.broadcast();

  Timer? _processingTimer;
  bool _isProcessing = false;
  SharedPreferences? _prefs;
  StreamSubscription<ConnectivityInfo>? _connectivitySubscription;

  // Statistics
  int _totalOperations = 0;
  int _successfulOperations = 0;
  int _failedOperations = 0;

  // Getters
  List<QueuedOperation> get queue => List.unmodifiable(_queue);
  Stream<QueuedOperation> get operationStream => _operationController.stream;
  bool get isProcessing => _isProcessing;
  int get queueLength => _queue.length;
  int get pendingOperations => _queue.where((op) => op.status == OperationStatus.pending).length;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initialize();
  }

  @override
  void onClose() {
    _processingTimer?.cancel();
    _connectivitySubscription?.cancel();
    _operationController.close();
    super.onClose();
  }

  Future<void> _initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadQueue();
      await _loadStats();
      
      // Listen to connectivity changes
      _connectivitySubscription = ConnectivityService.instance.connectivityStream.listen(
        _onConnectivityChanged,
      );

      // Start processing if online
      if (ConnectivityService.instance.isOnline) {
        _startProcessing();
      }

      if (kDebugMode) {
        print('OfflineQueueManager initialized with ${_queue.length} operations');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Failed to initialize OfflineQueueManager: $error');
      }
    }
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityInfo connectivity) {
    if (connectivity.isOnline && !_isProcessing && _queue.isNotEmpty) {
      if (kDebugMode) {
        print('Connectivity restored, starting queue processing');
      }
      _startProcessing();
    } else if (connectivity.isOffline && _isProcessing) {
      if (kDebugMode) {
        print('Connectivity lost, pausing queue processing');
      }
      _stopProcessing();
    }
  }

  /// Register an executor for a specific operation type
  void registerExecutor(OperationType type, OperationExecutor executor) {
    _executors[type] = executor;
    if (kDebugMode) {
      print('Registered executor for operation type: ${type.name}');
    }
  }

  /// Add an operation to the queue
  Future<String> enqueue(QueuedOperation operation) async {
    // Remove expired operations before adding new ones
    await _cleanupExpiredOperations();

    _queue.add(operation);
    _sortQueue();
    
    await _saveQueue();
    _operationController.add(operation);

    if (kDebugMode) {
      print('Enqueued operation: ${operation.type.name} (${operation.id})');
    }

    // Try to process immediately if online
    if (ConnectivityService.instance.isOnline && !_isProcessing) {
      _startProcessing();
    }

    return operation.id;
  }

  /// Remove an operation from the queue
  Future<bool> dequeue(String operationId) async {
    final index = _queue.indexWhere((op) => op.id == operationId);
    if (index != -1) {
      final operation = _queue.removeAt(index);
      await _saveQueue();
      
      if (kDebugMode) {
        print('Dequeued operation: ${operation.type.name} (${operation.id})');
      }
      return true;
    }
    return false;
  }

  /// Cancel a pending operation
  Future<bool> cancelOperation(String operationId) async {
    final index = _queue.indexWhere((op) => op.id == operationId);
    if (index != -1) {
      final operation = _queue[index];
      if (operation.status == OperationStatus.pending || operation.status == OperationStatus.failed) {
        _queue[index] = operation.copyWith(status: OperationStatus.cancelled);
        await _saveQueue();
        
        if (kDebugMode) {
          print('Cancelled operation: ${operation.type.name} (${operation.id})');
        }
        return true;
      }
    }
    return false;
  }

  /// Start processing the queue
  void _startProcessing() {
    if (_isProcessing) return;

    _isProcessing = true;
    _processingTimer = Timer.periodic(const Duration(seconds: 2), (_) {
      _processNextOperation();
    });

    if (kDebugMode) {
      print('Started queue processing');
    }
  }

  /// Stop processing the queue
  void _stopProcessing() {
    _isProcessing = false;
    _processingTimer?.cancel();
    _processingTimer = null;

    if (kDebugMode) {
      print('Stopped queue processing');
    }
  }

  /// Process the next operation in the queue
  Future<void> _processNextOperation() async {
    if (!ConnectivityService.instance.isOnline) {
      _stopProcessing();
      return;
    }

    // Find next pending operation
    final nextOperation = _queue
        .where((op) => op.status == OperationStatus.pending)
        .firstOrNull;

    if (nextOperation == null) {
      // No pending operations, check for retryable failed operations
      final retryableOperation = _queue
          .where((op) => op.canRetry)
          .firstOrNull;

      if (retryableOperation == null) {
        _stopProcessing();
        return;
      }

      await _retryOperation(retryableOperation);
      return;
    }

    await _executeOperation(nextOperation);
  }

  /// Execute a single operation
  Future<void> _executeOperation(QueuedOperation operation) async {
    final executor = _executors[operation.type];
    if (executor == null) {
      if (kDebugMode) {
        print('No executor registered for operation type: ${operation.type.name}');
      }
      
      final updatedOperation = operation.copyWith(
        status: OperationStatus.failed,
        errorMessage: 'No executor registered for operation type',
      );
      await _updateOperation(updatedOperation);
      return;
    }

    // Mark as in progress
    final inProgressOperation = operation.copyWith(status: OperationStatus.inProgress);
    await _updateOperation(inProgressOperation);

    try {
      if (kDebugMode) {
        print('Executing operation: ${operation.type.name} (${operation.id})');
      }

      // Execute with retry logic
      final result = await RetryPatterns.networkOperation(
        () => executor(operation),
        operationName: operation.type.name,
      );

      if (result.isSuccess) {
        final completedOperation = operation.copyWith(status: OperationStatus.completed);
        await _updateOperation(completedOperation);
        _successfulOperations++;
        
        if (kDebugMode) {
          print('Operation completed successfully: ${operation.type.name} (${operation.id})');
        }
      } else {
        throw result.error!;
      }
    } catch (error) {
      final appError = error is AppError ? error : ErrorFactory.fromException(error);
      
      final failedOperation = operation.copyWith(
        status: OperationStatus.failed,
        errorMessage: appError.userMessage,
        retryCount: operation.retryCount + 1,
      );
      
      await _updateOperation(failedOperation);
      _failedOperations++;

      if (kDebugMode) {
        print('Operation failed: ${operation.type.name} (${operation.id}) - ${appError.message}');
      }
    }

    await _saveStats();
  }

  /// Retry a failed operation
  Future<void> _retryOperation(QueuedOperation operation) async {
    if (!operation.canRetry) return;

    final retryOperation = operation.copyWith(
      status: OperationStatus.pending,
      retryCount: operation.retryCount + 1,
    );

    await _updateOperation(retryOperation);
    
    if (kDebugMode) {
      print('Retrying operation: ${operation.type.name} (${operation.id}) - Attempt ${retryOperation.retryCount}');
    }
  }

  /// Update an operation in the queue
  Future<void> _updateOperation(QueuedOperation updatedOperation) async {
    final index = _queue.indexWhere((op) => op.id == updatedOperation.id);
    if (index != -1) {
      _queue[index] = updatedOperation;
      await _saveQueue();
      _operationController.add(updatedOperation);
    }
  }

  /// Sort queue by priority and creation time
  void _sortQueue() {
    _queue.sort((a, b) {
      // First sort by priority (higher priority first)
      final priorityComparison = b.priorityValue.compareTo(a.priorityValue);
      if (priorityComparison != 0) return priorityComparison;
      
      // Then sort by creation time (older first)
      return a.createdAt.compareTo(b.createdAt);
    });
  }

  /// Clean up expired operations
  Future<void> _cleanupExpiredOperations() async {
    final initialLength = _queue.length;
    _queue.removeWhere((op) => op.isExpired);
    
    if (_queue.length != initialLength) {
      await _saveQueue();
      if (kDebugMode) {
        print('Cleaned up ${initialLength - _queue.length} expired operations');
      }
    }
  }

  /// Save queue to persistent storage
  Future<void> _saveQueue() async {
    if (_prefs == null) return;

    try {
      final queueJson = _queue.map((op) => op.toJson()).toList();
      await _prefs!.setString(_queueKey, jsonEncode(queueJson));
    } catch (error) {
      if (kDebugMode) {
        print('Failed to save queue: $error');
      }
    }
  }

  /// Load queue from persistent storage
  Future<void> _loadQueue() async {
    if (_prefs == null) return;

    try {
      final queueString = _prefs!.getString(_queueKey);
      if (queueString != null) {
        final queueJson = jsonDecode(queueString) as List<dynamic>;
        _queue.clear();
        _queue.addAll(queueJson.map((json) => QueuedOperation.fromJson(json)));
        _sortQueue();
        
        if (kDebugMode) {
          print('Loaded ${_queue.length} operations from storage');
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('Failed to load queue: $error');
      }
    }
  }

  /// Save statistics to persistent storage
  Future<void> _saveStats() async {
    if (_prefs == null) return;

    try {
      final stats = {
        'totalOperations': _totalOperations,
        'successfulOperations': _successfulOperations,
        'failedOperations': _failedOperations,
      };
      await _prefs!.setString(_statsKey, jsonEncode(stats));
    } catch (error) {
      if (kDebugMode) {
        print('Failed to save stats: $error');
      }
    }
  }

  /// Load statistics from persistent storage
  Future<void> _loadStats() async {
    if (_prefs == null) return;

    try {
      final statsString = _prefs!.getString(_statsKey);
      if (statsString != null) {
        final stats = jsonDecode(statsString) as Map<String, dynamic>;
        _totalOperations = stats['totalOperations'] ?? 0;
        _successfulOperations = stats['successfulOperations'] ?? 0;
        _failedOperations = stats['failedOperations'] ?? 0;
      }
    } catch (error) {
      if (kDebugMode) {
        print('Failed to load stats: $error');
      }
    }
  }

  /// Get queue statistics
  Map<String, dynamic> getQueueStats() {
    return {
      'totalOperations': _totalOperations,
      'successfulOperations': _successfulOperations,
      'failedOperations': _failedOperations,
      'queueLength': _queue.length,
      'pendingOperations': pendingOperations,
      'isProcessing': _isProcessing,
      'successRate': _totalOperations > 0 ? _successfulOperations / _totalOperations : 0.0,
    };
  }

  /// Clear all completed operations
  Future<void> clearCompleted() async {
    final initialLength = _queue.length;
    _queue.removeWhere((op) => op.status == OperationStatus.completed);
    
    if (_queue.length != initialLength) {
      await _saveQueue();
      if (kDebugMode) {
        print('Cleared ${initialLength - _queue.length} completed operations');
      }
    }
  }

  /// Clear all operations (use with caution)
  Future<void> clearAll() async {
    _queue.clear();
    await _saveQueue();
    
    if (kDebugMode) {
      print('Cleared all operations from queue');
    }
  }
}
