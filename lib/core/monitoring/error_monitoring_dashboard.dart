// Error monitoring dashboard for debugging and analytics
// Provides comprehensive insights into app health and error patterns

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../error_handling/error_handler_service.dart';
import '../error_handling/error_logger.dart';
import '../connectivity/connectivity_service.dart';
import '../offline/offline_queue_manager.dart';
import '../services/service_initializer.dart';
import '../ui/loading_state_manager.dart';
import '../../services/enhanced_ai_service.dart';

/// Error monitoring dashboard widget
class ErrorMonitoringDashboard extends StatefulWidget {
  const ErrorMonitoringDashboard({super.key});

  @override
  State<ErrorMonitoringDashboard> createState() => _ErrorMonitoringDashboardState();
}

class _ErrorMonitoringDashboardState extends State<ErrorMonitoringDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  Map<String, dynamic>? _healthData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadHealthData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadHealthData() async {
    setState(() => _isLoading = true);
    
    try {
      _healthData = await ServiceInitializer.healthCheck();
    } catch (error) {
      _healthData = {
        'overall': 'error',
        'error': error.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
    
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error Monitoring Dashboard'),
        actions: [
          IconButton(
            onPressed: _loadHealthData,
            icon: const Icon(Icons.refresh),
          ),
          IconButton(
            onPressed: _exportLogs,
            icon: const Icon(Icons.download),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
            Tab(text: 'Errors', icon: Icon(Icons.error)),
            Tab(text: 'Connectivity', icon: Icon(Icons.wifi)),
            Tab(text: 'Queue', icon: Icon(Icons.queue)),
            Tab(text: 'Loading', icon: Icon(Icons.hourglass_empty)),
            Tab(text: 'Logs', icon: Icon(Icons.list)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildErrorsTab(),
                _buildConnectivityTab(),
                _buildQueueTab(),
                _buildLoadingTab(),
                _buildLogsTab(),
              ],
            ),
    );
  }

  Widget _buildOverviewTab() {
    if (_healthData == null) {
      return const Center(child: Text('No health data available'));
    }

    final overall = _healthData!['overall'] as String;
    final services = _healthData!['services'] as Map<String, dynamic>? ?? {};

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHealthCard(overall),
          const SizedBox(height: 16),
          _buildServiceStatusCards(services),
          const SizedBox(height: 16),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildHealthCard(String overall) {
    Color color;
    IconData icon;
    
    switch (overall) {
      case 'healthy':
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case 'unhealthy':
        color = Colors.red;
        icon = Icons.error;
        break;
      default:
        color = Colors.orange;
        icon = Icons.warning;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'System Health',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  Text(
                    overall.toUpperCase(),
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  Text(
                    'Last updated: ${DateTime.now().toString().substring(0, 19)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceStatusCards(Map<String, dynamic> services) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Status',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 8),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 2.5,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          children: services.entries.map((entry) {
            final serviceName = entry.key;
            final serviceData = entry.value as Map<String, dynamic>;
            final status = serviceData['status'] as String? ?? 'unknown';
            
            return Card(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          status == 'healthy' ? Icons.check_circle : Icons.error,
                          color: status == 'healthy' ? Colors.green : Colors.red,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            serviceName,
                            style: const TextStyle(fontWeight: FontWeight.w600),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      status,
                      style: TextStyle(
                        color: status == 'healthy' ? Colors.green : Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ElevatedButton.icon(
              onPressed: _clearErrorHistory,
              icon: const Icon(Icons.clear_all),
              label: const Text('Clear Errors'),
            ),
            ElevatedButton.icon(
              onPressed: _resetServices,
              icon: const Icon(Icons.refresh),
              label: const Text('Reset Services'),
            ),
            ElevatedButton.icon(
              onPressed: _clearOfflineQueue,
              icon: const Icon(Icons.queue_play_next),
              label: const Text('Clear Queue'),
            ),
            ElevatedButton.icon(
              onPressed: _exportLogs,
              icon: const Icon(Icons.download),
              label: const Text('Export Logs'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildErrorsTab() {
    if (!Get.isRegistered<ErrorHandlerService>()) {
      return const Center(child: Text('Error handler not available'));
    }

    final errorHandler = ErrorHandlerService.instance;
    final recentErrors = errorHandler.getRecentErrors();
    final errorStats = errorHandler.getErrorStats();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard('Error Statistics', errorStats),
          const SizedBox(height: 16),
          Text(
            'Recent Errors',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          if (recentErrors.isEmpty)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Text('No recent errors'),
              ),
            )
          else
            ...recentErrors.map((error) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ExpansionTile(
                title: Text(error.code),
                subtitle: Text(error.message),
                leading: Icon(
                  Icons.error,
                  color: _getSeverityColor(error.severity),
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildErrorDetail('User Message', error.userMessage),
                        _buildErrorDetail('Severity', error.severity.name),
                        _buildErrorDetail('Timestamp', error.timestamp.toString()),
                        if (error.context.isNotEmpty)
                          _buildErrorDetail('Context', error.context.toString()),
                        if (error.stackTrace != null)
                          _buildErrorDetail('Stack Trace', error.stackTrace!),
                      ],
                    ),
                  ),
                ],
              ),
            )),
        ],
      ),
    );
  }

  Widget _buildConnectivityTab() {
    if (!Get.isRegistered<ConnectivityService>()) {
      return const Center(child: Text('Connectivity service not available'));
    }

    final connectivity = ConnectivityService.instance;
    final stats = connectivity.getConnectivityStats();
    final currentInfo = connectivity.currentConnectivity;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildConnectivityStatusCard(currentInfo),
          const SizedBox(height: 16),
          _buildStatsCard('Connectivity Statistics', stats),
        ],
      ),
    );
  }

  Widget _buildQueueTab() {
    if (!Get.isRegistered<OfflineQueueManager>()) {
      return const Center(child: Text('Offline queue not available'));
    }

    final queue = OfflineQueueManager.instance;
    final stats = queue.getQueueStats();
    final queueItems = queue.queue;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard('Queue Statistics', stats),
          const SizedBox(height: 16),
          Text(
            'Queue Items',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          if (queueItems.isEmpty)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Text('No items in queue'),
              ),
            )
          else
            ...queueItems.map((item) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                title: Text(item.type.name),
                subtitle: Text(item.status.name),
                leading: Icon(_getOperationIcon(item.type)),
                trailing: Text(item.priority.name),
              ),
            )),
        ],
      ),
    );
  }

  Widget _buildLoadingTab() {
    if (!Get.isRegistered<LoadingStateManager>()) {
      return const Center(child: Text('Loading state manager not available'));
    }

    final loadingManager = LoadingStateManager.instance;
    final stats = loadingManager.getLoadingStats();
    final loadingStates = loadingManager.loadingStates;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard('Loading Statistics', stats),
          const SizedBox(height: 16),
          Text(
            'Active Loading Operations',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          if (loadingStates.isEmpty)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Text('No active loading operations'),
              ),
            )
          else
            ...loadingStates.values.map((state) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                title: Text(state.message),
                subtitle: Text('${state.type.name} • ${state.elapsedTimeString}'),
                leading: Icon(_getLoadingIcon(state.type)),
                trailing: state.isCancellable
                    ? IconButton(
                        onPressed: () => loadingManager.cancelLoading(state.id),
                        icon: const Icon(Icons.cancel),
                      )
                    : null,
              ),
            )),
        ],
      ),
    );
  }

  Widget _buildLogsTab() {
    final logger = ErrorLogger.instance;
    final logs = logger.recentLogs;
    final stats = logger.getLoggingStats();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard('Logging Statistics', stats),
          const SizedBox(height: 16),
          Text(
            'Recent Logs',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          if (logs.isEmpty)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Text('No recent logs'),
              ),
            )
          else
            ...logs.map((log) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ExpansionTile(
                title: Text(log.message),
                subtitle: Text('${log.level.name} • ${log.timestamp}'),
                leading: Icon(
                  _getLogLevelIcon(log.level),
                  color: _getLogLevelColor(log.level),
                ),
                children: [
                  if (log.context.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        'Context: ${log.context}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                ],
              ),
            )),
        ],
      ),
    );
  }

  Widget _buildStatsCard(String title, Map<String, dynamic> stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            ...stats.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(entry.key),
                  Text(
                    entry.value.toString(),
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectivityStatusCard(dynamic connectivityInfo) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Connectivity',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  connectivityInfo.isOnline ? Icons.wifi : Icons.wifi_off,
                  color: connectivityInfo.isOnline ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  connectivityInfo.state.toString(),
                  style: TextStyle(
                    color: connectivityInfo.isOnline ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            Text('Quality: ${connectivityInfo.quality.toString()}'),
            Text('Type: ${connectivityInfo.connectionType.toString()}'),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          Text(value),
        ],
      ),
    );
  }

  Color _getSeverityColor(dynamic severity) {
    switch (severity.toString()) {
      case 'ErrorSeverity.critical':
        return Colors.red;
      case 'ErrorSeverity.high':
        return Colors.orange;
      case 'ErrorSeverity.medium':
        return Colors.yellow;
      default:
        return Colors.blue;
    }
  }

  IconData _getOperationIcon(dynamic type) {
    switch (type.toString()) {
      case 'OperationType.askAI':
        return Icons.psychology;
      case 'OperationType.generateQuiz':
        return Icons.quiz;
      default:
        return Icons.work;
    }
  }

  IconData _getLoadingIcon(dynamic type) {
    switch (type.toString()) {
      case 'LoadingType.aiRequest':
        return Icons.psychology;
      case 'LoadingType.dataSync':
        return Icons.sync;
      default:
        return Icons.hourglass_empty;
    }
  }

  IconData _getLogLevelIcon(dynamic level) {
    switch (level.toString()) {
      case 'LogLevel.error':
      case 'LogLevel.critical':
        return Icons.error;
      case 'LogLevel.warning':
        return Icons.warning;
      case 'LogLevel.info':
        return Icons.info;
      default:
        return Icons.bug_report;
    }
  }

  Color _getLogLevelColor(dynamic level) {
    switch (level.toString()) {
      case 'LogLevel.critical':
        return Colors.red;
      case 'LogLevel.error':
        return Colors.orange;
      case 'LogLevel.warning':
        return Colors.yellow;
      case 'LogLevel.info':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _clearErrorHistory() {
    if (Get.isRegistered<ErrorHandlerService>()) {
      ErrorHandlerService.instance.clearErrorHistory();
      _loadHealthData();
      Get.snackbar('Success', 'Error history cleared');
    }
  }

  void _resetServices() async {
    await ServiceInitializer.reset();
    _loadHealthData();
    Get.snackbar('Success', 'Services reset');
  }

  void _clearOfflineQueue() async {
    if (Get.isRegistered<OfflineQueueManager>()) {
      await OfflineQueueManager.instance.clearCompleted();
      _loadHealthData();
      Get.snackbar('Success', 'Offline queue cleared');
    }
  }

  void _exportLogs() async {
    try {
      final logs = await ErrorLogger.instance.exportLogs();
      // In a real implementation, you would save this to a file or share it
      Get.snackbar('Success', 'Logs exported (${logs.length} characters)');
    } catch (error) {
      Get.snackbar('Error', 'Failed to export logs: $error');
    }
  }
}
