// Enterprise-grade error handling service
// Centralizes error processing, logging, and user notification

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app_error.dart';
import 'error_logger.dart';
import '../connectivity/connectivity_service.dart';

/// Error handling configuration
class ErrorHandlerConfig {
  final bool enableLogging;
  final bool enableUserNotifications;
  final bool enableRetryPrompts;
  final Duration notificationDuration;
  final bool enableDebugMode;

  const ErrorHandlerConfig({
    this.enableLogging = true,
    this.enableUserNotifications = true,
    this.enableRetryPrompts = true,
    this.notificationDuration = const Duration(seconds: 4),
    this.enableDebugMode = kDebugMode,
  });
}

/// User notification types for different error scenarios
enum NotificationType {
  snackbar,
  dialog,
  banner,
  toast,
  none,
}

/// Error presentation configuration
class ErrorPresentation {
  final NotificationType type;
  final String title;
  final String message;
  final String? actionLabel;
  final VoidCallback? action;
  final Color? backgroundColor;
  final IconData? icon;
  final Duration? duration;

  const ErrorPresentation({
    required this.type,
    required this.title,
    required this.message,
    this.actionLabel,
    this.action,
    this.backgroundColor,
    this.icon,
    this.duration,
  });
}

/// Centralized error handling service
class ErrorHandlerService extends GetxService {
  static ErrorHandlerService get instance => Get.find<ErrorHandlerService>();

  final ErrorHandlerConfig _config;
  final ErrorLogger _logger = ErrorLogger.instance;
  
  // Error tracking
  final Map<String, int> _errorCounts = {};
  final Map<String, DateTime> _lastErrorTimes = {};
  final List<AppError> _recentErrors = [];
  
  // Rate limiting
  static const int _maxErrorsPerMinute = 10;
  static const Duration _rateLimitWindow = Duration(minutes: 1);

  ErrorHandlerService({ErrorHandlerConfig? config}) 
      : _config = config ?? const ErrorHandlerConfig();

  @override
  void onInit() {
    super.onInit();
    
    // Set up global error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      handleFlutterError(details);
    };

    if (kDebugMode) {
      print('ErrorHandlerService initialized');
    }
  }

  /// Handle any type of error with appropriate response
  Future<void> handleError(
    dynamic error, {
    String? context,
    bool showToUser = true,
    bool enableRetry = true,
    VoidCallback? onRetry,
  }) async {
    final appError = error is AppError ? error : ErrorFactory.fromException(error, context: context);
    
    // Log the error
    if (_config.enableLogging) {
      await _logger.logError(appError, context: context);
    }

    // Track error for analytics
    _trackError(appError);

    // Check rate limiting
    if (_isRateLimited(appError)) {
      if (kDebugMode) {
        print('Error rate limited: ${appError.code}');
      }
      return;
    }

    // Show to user if enabled
    if (showToUser && _config.enableUserNotifications) {
      await _presentErrorToUser(appError, enableRetry: enableRetry, onRetry: onRetry);
    }

    // Store in recent errors for debugging
    _recentErrors.add(appError);
    if (_recentErrors.length > 50) {
      _recentErrors.removeAt(0);
    }
  }

  /// Handle Flutter framework errors
  void handleFlutterError(FlutterErrorDetails details) {
    final error = SystemError(
      type: SystemErrorType.unknown,
      code: 'FLUTTER_ERROR',
      message: details.exception.toString(),
      userMessage: 'An unexpected error occurred in the app.',
      context: {
        'library': details.library,
        'stack': details.stack?.toString(),
      },
      stackTrace: details.stack?.toString(),
    );

    handleError(error, context: 'Flutter Framework');
  }

  /// Present error to user based on error type and severity
  Future<void> _presentErrorToUser(
    AppError error, {
    bool enableRetry = true,
    VoidCallback? onRetry,
  }) async {
    final presentation = _createErrorPresentation(error, enableRetry: enableRetry, onRetry: onRetry);
    
    switch (presentation.type) {
      case NotificationType.snackbar:
        _showSnackbar(presentation);
        break;
      case NotificationType.dialog:
        await _showDialog(presentation);
        break;
      case NotificationType.banner:
        _showBanner(presentation);
        break;
      case NotificationType.toast:
        _showToast(presentation);
        break;
      case NotificationType.none:
        break;
    }
  }

  /// Create appropriate error presentation based on error characteristics
  ErrorPresentation _createErrorPresentation(
    AppError error, {
    bool enableRetry = true,
    VoidCallback? onRetry,
  }) {
    // Determine notification type based on severity
    NotificationType type;
    Color? backgroundColor;
    IconData? icon;
    Duration? duration = _config.notificationDuration;

    switch (error.severity) {
      case ErrorSeverity.low:
        type = NotificationType.toast;
        backgroundColor = Colors.blue[100];
        icon = Icons.info_outline;
        duration = const Duration(seconds: 2);
        break;
      case ErrorSeverity.medium:
        type = NotificationType.snackbar;
        backgroundColor = Colors.orange[100];
        icon = Icons.warning_outlined;
        break;
      case ErrorSeverity.high:
        type = NotificationType.snackbar;
        backgroundColor = Colors.red[100];
        icon = Icons.error_outline;
        duration = const Duration(seconds: 6);
        break;
      case ErrorSeverity.critical:
        type = NotificationType.dialog;
        backgroundColor = Colors.red[100];
        icon = Icons.error;
        break;
    }

    // Customize based on error type
    String title = _getErrorTitle(error);
    String message = _getContextualMessage(error);
    String? actionLabel;
    VoidCallback? action;

    // Add retry action if applicable
    if (enableRetry && error.isRetryable && onRetry != null && _config.enableRetryPrompts) {
      actionLabel = 'Retry';
      action = onRetry;
    }

    // Add specific actions for certain error types
    if (error is NetworkError) {
      switch (error.type) {
        case NetworkErrorType.noConnection:
          actionLabel = 'Check Connection';
          action = () => _openNetworkSettings();
          break;
        case NetworkErrorType.unauthorized:
          actionLabel = 'Login';
          action = () => _navigateToLogin();
          break;
        default:
          break;
      }
    }

    return ErrorPresentation(
      type: type,
      title: title,
      message: message,
      actionLabel: actionLabel,
      action: action,
      backgroundColor: backgroundColor,
      icon: icon,
      duration: duration,
    );
  }

  /// Get appropriate title for error
  String _getErrorTitle(AppError error) {
    if (error is NetworkError) {
      switch (error.type) {
        case NetworkErrorType.noConnection:
          return 'No Internet Connection';
        case NetworkErrorType.timeout:
          return 'Connection Timeout';
        case NetworkErrorType.serverError:
          return 'Server Error';
        case NetworkErrorType.unauthorized:
          return 'Authentication Required';
        case NetworkErrorType.forbidden:
          return 'Access Denied';
        case NetworkErrorType.rateLimited:
          return 'Rate Limited';
        default:
          return 'Network Error';
      }
    }

    if (error is BusinessError) {
      switch (error.type) {
        case BusinessErrorType.quotaExceeded:
          return 'Quota Exceeded';
        case BusinessErrorType.unsupportedQuestion:
          return 'Unsupported Request';
        case BusinessErrorType.invalidInput:
          return 'Invalid Input';
        default:
          return 'Request Error';
      }
    }

    if (error is SystemError) {
      switch (error.type) {
        case SystemErrorType.databaseError:
          return 'Database Error';
        case SystemErrorType.storageError:
          return 'Storage Error';
        case SystemErrorType.parseError:
          return 'Data Error';
        default:
          return 'System Error';
      }
    }

    return 'Error';
  }

  /// Get contextual message with helpful information
  String _getContextualMessage(AppError error) {
    String baseMessage = error.userMessage;

    // Add connectivity context
    if (error is NetworkError && !ConnectivityService.instance.isOnline) {
      baseMessage += '\n\nYou appear to be offline. The operation will be retried when connection is restored.';
    }

    // Add retry information
    if (error.isRetryable && error.maxRetries > 0) {
      baseMessage += '\n\nThis operation will be retried automatically.';
    }

    return baseMessage;
  }

  /// Show snackbar notification
  void _showSnackbar(ErrorPresentation presentation) {
    if (!Get.isSnackbarOpen) {
      Get.snackbar(
        presentation.title,
        presentation.message,
        duration: presentation.duration,
        backgroundColor: presentation.backgroundColor,
        colorText: Colors.black87,
        icon: presentation.icon != null ? Icon(presentation.icon, color: Colors.black87) : null,
        mainButton: presentation.action != null
            ? TextButton(
                onPressed: presentation.action,
                child: Text(presentation.actionLabel ?? 'Action'),
              )
            : null,
        snackPosition: SnackPosition.BOTTOM,
        margin: const EdgeInsets.all(16),
        borderRadius: 8,
      );
    }
  }

  /// Show error dialog
  Future<void> _showDialog(ErrorPresentation presentation) async {
    await Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            if (presentation.icon != null) ...[
              Icon(presentation.icon, color: Colors.red),
              const SizedBox(width: 8),
            ],
            Expanded(child: Text(presentation.title)),
          ],
        ),
        content: Text(presentation.message),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
          if (presentation.action != null)
            ElevatedButton(
              onPressed: () {
                Get.back();
                presentation.action?.call();
              },
              child: Text(presentation.actionLabel ?? 'Action'),
            ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// Show banner notification
  void _showBanner(ErrorPresentation presentation) {
    // Implementation would depend on your banner system
    // This is a placeholder for banner notifications
    if (kDebugMode) {
      print('Banner: ${presentation.title} - ${presentation.message}');
    }
  }

  /// Show toast notification
  void _showToast(ErrorPresentation presentation) {
    // Implementation would depend on your toast system
    // This is a placeholder for toast notifications
    if (kDebugMode) {
      print('Toast: ${presentation.title} - ${presentation.message}');
    }
  }

  /// Track error for analytics and rate limiting
  void _trackError(AppError error) {
    final now = DateTime.now();
    final errorKey = '${error.code}_${error.runtimeType}';
    
    _errorCounts[errorKey] = (_errorCounts[errorKey] ?? 0) + 1;
    _lastErrorTimes[errorKey] = now;

    // Clean up old entries
    _errorCounts.removeWhere((key, count) {
      final lastTime = _lastErrorTimes[key];
      return lastTime == null || now.difference(lastTime) > _rateLimitWindow;
    });
  }

  /// Check if error should be rate limited
  bool _isRateLimited(AppError error) {
    final errorKey = '${error.code}_${error.runtimeType}';
    final count = _errorCounts[errorKey] ?? 0;
    return count > _maxErrorsPerMinute;
  }

  /// Open network settings (platform-specific implementation needed)
  void _openNetworkSettings() {
    // Platform-specific implementation to open network settings
    if (kDebugMode) {
      print('Opening network settings...');
    }
  }

  /// Navigate to login page
  void _navigateToLogin() {
    // Navigate to login page
    Get.toNamed('/login');
  }

  /// Get error statistics for monitoring
  Map<String, dynamic> getErrorStats() {
    return {
      'recentErrorCount': _recentErrors.length,
      'errorCounts': Map.from(_errorCounts),
      'rateLimitedErrors': _errorCounts.entries
          .where((entry) => entry.value > _maxErrorsPerMinute)
          .map((entry) => entry.key)
          .toList(),
    };
  }

  /// Get recent errors for debugging
  List<AppError> getRecentErrors() {
    return List.unmodifiable(_recentErrors);
  }

  /// Clear error history
  void clearErrorHistory() {
    _recentErrors.clear();
    _errorCounts.clear();
    _lastErrorTimes.clear();
  }
}
