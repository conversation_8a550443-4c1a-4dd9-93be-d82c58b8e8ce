// Core error handling system for enterprise-grade error management
// Follows patterns used by major tech companies for robust error handling

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// Base class for all application errors
/// Provides consistent error structure across the application
abstract class AppError implements Exception {
  final String code;
  final String message;
  final String userMessage;
  final Map<String, dynamic> context;
  final DateTime timestamp;
  final String? stackTrace;
  final AppError? cause;

  const AppError({
    required this.code,
    required this.message,
    required this.userMessage,
    this.context = const {},
    DateTime? timestamp,
    this.stackTrace,
    this.cause,
  }) : timestamp = timestamp ?? const Duration().inMilliseconds != 0 
         ? DateTime.now() 
         : DateTime.fromMillisecondsSinceEpoch(0);

  /// Error severity levels for logging and monitoring
  ErrorSeverity get severity;
  
  /// Whether this error should be retried
  bool get isRetryable;
  
  /// Maximum retry attempts for this error type
  int get maxRetries;
  
  /// Base delay for retry attempts (will be used with exponential backoff)
  Duration get baseRetryDelay;

  @override
  String toString() => 'AppError(code: $code, message: $message)';

  /// Convert error to JSON for logging/monitoring
  Map<String, dynamic> toJson() => {
    'code': code,
    'message': message,
    'userMessage': userMessage,
    'context': context,
    'timestamp': timestamp.toIso8601String(),
    'severity': severity.name,
    'isRetryable': isRetryable,
    'stackTrace': stackTrace,
    'cause': cause?.toJson(),
  };
}

/// Error severity levels for proper categorization
enum ErrorSeverity {
  low,      // Minor issues, app continues normally
  medium,   // Noticeable issues, some functionality affected
  high,     // Major issues, significant functionality impacted
  critical, // Critical issues, app may be unusable
}

/// Network-related errors with specific handling strategies
class NetworkError extends AppError {
  final NetworkErrorType type;
  final int? statusCode;
  final Duration? timeout;

  const NetworkError({
    required this.type,
    required super.code,
    required super.message,
    required super.userMessage,
    this.statusCode,
    this.timeout,
    super.context,
    super.timestamp,
    super.stackTrace,
    super.cause,
  });

  @override
  ErrorSeverity get severity {
    switch (type) {
      case NetworkErrorType.noConnection:
      case NetworkErrorType.timeout:
        return ErrorSeverity.medium;
      case NetworkErrorType.serverError:
      case NetworkErrorType.rateLimited:
        return ErrorSeverity.high;
      case NetworkErrorType.unauthorized:
      case NetworkErrorType.forbidden:
        return ErrorSeverity.critical;
      default:
        return ErrorSeverity.medium;
    }
  }

  @override
  bool get isRetryable {
    switch (type) {
      case NetworkErrorType.noConnection:
      case NetworkErrorType.timeout:
      case NetworkErrorType.serverError:
      case NetworkErrorType.rateLimited:
        return true;
      case NetworkErrorType.unauthorized:
      case NetworkErrorType.forbidden:
      case NetworkErrorType.badRequest:
      case NetworkErrorType.notFound:
        return false;
      default:
        return false;
    }
  }

  @override
  int get maxRetries {
    switch (type) {
      case NetworkErrorType.noConnection:
        return 5;
      case NetworkErrorType.timeout:
      case NetworkErrorType.serverError:
        return 3;
      case NetworkErrorType.rateLimited:
        return 2;
      default:
        return 0;
    }
  }

  @override
  Duration get baseRetryDelay {
    switch (type) {
      case NetworkErrorType.noConnection:
        return const Duration(seconds: 2);
      case NetworkErrorType.timeout:
        return const Duration(seconds: 1);
      case NetworkErrorType.serverError:
        return const Duration(seconds: 5);
      case NetworkErrorType.rateLimited:
        return const Duration(seconds: 30);
      default:
        return const Duration(seconds: 1);
    }
  }

  @override
  Map<String, dynamic> toJson() => {
    ...super.toJson(),
    'type': type.name,
    'statusCode': statusCode,
    'timeout': timeout?.inMilliseconds,
  };
}

/// Specific network error types for granular handling
enum NetworkErrorType {
  noConnection,
  timeout,
  serverError,
  unauthorized,
  forbidden,
  badRequest,
  notFound,
  rateLimited,
  unknown,
}

/// Business logic errors for application-specific issues
class BusinessError extends AppError {
  final BusinessErrorType type;

  const BusinessError({
    required this.type,
    required super.code,
    required super.message,
    required super.userMessage,
    super.context,
    super.timestamp,
    super.stackTrace,
    super.cause,
  });

  @override
  ErrorSeverity get severity => ErrorSeverity.medium;

  @override
  bool get isRetryable => false;

  @override
  int get maxRetries => 0;

  @override
  Duration get baseRetryDelay => Duration.zero;

  @override
  Map<String, dynamic> toJson() => {
    ...super.toJson(),
    'type': type.name,
  };
}

enum BusinessErrorType {
  quotaExceeded,
  unsupportedQuestion,
  invalidInput,
  dataCorruption,
}

/// System-level errors for technical issues
class SystemError extends AppError {
  final SystemErrorType type;

  const SystemError({
    required this.type,
    required super.code,
    required super.message,
    required super.userMessage,
    super.context,
    super.timestamp,
    super.stackTrace,
    super.cause,
  });

  @override
  ErrorSeverity get severity {
    switch (type) {
      case SystemErrorType.databaseError:
      case SystemErrorType.storageError:
        return ErrorSeverity.critical;
      case SystemErrorType.parseError:
      case SystemErrorType.configurationError:
        return ErrorSeverity.high;
      default:
        return ErrorSeverity.medium;
    }
  }

  @override
  bool get isRetryable {
    switch (type) {
      case SystemErrorType.databaseError:
      case SystemErrorType.storageError:
        return true;
      default:
        return false;
    }
  }

  @override
  int get maxRetries => isRetryable ? 2 : 0;

  @override
  Duration get baseRetryDelay => const Duration(milliseconds: 500);

  @override
  Map<String, dynamic> toJson() => {
    ...super.toJson(),
    'type': type.name,
  };
}

enum SystemErrorType {
  databaseError,
  storageError,
  parseError,
  configurationError,
  unknown,
}

/// Factory class for creating appropriate error instances from exceptions
class ErrorFactory {
  static AppError fromException(dynamic exception, {String? context}) {
    if (exception is DioException) {
      return _createNetworkError(exception, context);
    }
    
    if (exception is AppError) {
      return exception;
    }

    // Default system error for unknown exceptions
    return SystemError(
      type: SystemErrorType.unknown,
      code: 'SYSTEM_UNKNOWN',
      message: exception.toString(),
      userMessage: 'An unexpected error occurred. Please try again.',
      context: {'originalException': exception.toString(), 'context': context},
      stackTrace: kDebugMode ? StackTrace.current.toString() : null,
    );
  }

  static NetworkError _createNetworkError(DioException dioException, String? context) {
    final statusCode = dioException.response?.statusCode;
    
    NetworkErrorType type;
    String code;
    String message;
    String userMessage;

    switch (dioException.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        type = NetworkErrorType.timeout;
        code = 'NETWORK_TIMEOUT';
        message = 'Request timed out';
        userMessage = 'The request is taking longer than expected. Please check your connection and try again.';
        break;
      
      case DioExceptionType.connectionError:
        type = NetworkErrorType.noConnection;
        code = 'NETWORK_NO_CONNECTION';
        message = 'No internet connection';
        userMessage = 'Please check your internet connection and try again.';
        break;
      
      case DioExceptionType.badResponse:
        if (statusCode != null) {
          if (statusCode == 401) {
            type = NetworkErrorType.unauthorized;
            code = 'NETWORK_UNAUTHORIZED';
            message = 'Authentication failed';
            userMessage = 'Your session has expired. Please log in again.';
          } else if (statusCode == 403) {
            type = NetworkErrorType.forbidden;
            code = 'NETWORK_FORBIDDEN';
            message = 'Access forbidden';
            userMessage = 'You don\'t have permission to perform this action.';
          } else if (statusCode == 429) {
            type = NetworkErrorType.rateLimited;
            code = 'NETWORK_RATE_LIMITED';
            message = 'Rate limit exceeded';
            userMessage = 'Too many requests. Please wait a moment and try again.';
          } else if (statusCode >= 500) {
            type = NetworkErrorType.serverError;
            code = 'NETWORK_SERVER_ERROR';
            message = 'Server error';
            userMessage = 'Our servers are experiencing issues. Please try again later.';
          } else {
            type = NetworkErrorType.unknown;
            code = 'NETWORK_UNKNOWN';
            message = 'Unknown network error';
            userMessage = 'An unexpected network error occurred. Please try again.';
          }
        } else {
          type = NetworkErrorType.unknown;
          code = 'NETWORK_UNKNOWN';
          message = 'Unknown network error';
          userMessage = 'An unexpected network error occurred. Please try again.';
        }
        break;
      
      default:
        type = NetworkErrorType.unknown;
        code = 'NETWORK_UNKNOWN';
        message = 'Unknown network error';
        userMessage = 'An unexpected network error occurred. Please try again.';
    }

    return NetworkError(
      type: type,
      code: code,
      message: message,
      userMessage: userMessage,
      statusCode: statusCode,
      context: {'dioErrorType': dioException.type.name, 'context': context},
      stackTrace: kDebugMode ? StackTrace.current.toString() : null,
      cause: dioException.error is AppError ? dioException.error as AppError : null,
    );
  }
}
