// Enterprise-grade error logging and monitoring
// Implements structured logging patterns used by major tech companies

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'app_error.dart';

/// Log levels for different types of messages
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// Log entry structure for consistent logging
class LogEntry {
  final String id;
  final LogLevel level;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic> context;
  final String? stackTrace;
  final String? userId;
  final String? sessionId;

  const LogEntry({
    required this.id,
    required this.level,
    required this.message,
    required this.timestamp,
    this.context = const {},
    this.stackTrace,
    this.userId,
    this.sessionId,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'level': level.name,
    'message': message,
    'timestamp': timestamp.toIso8601String(),
    'context': context,
    'stackTrace': stackTrace,
    'userId': userId,
    'sessionId': sessionId,
  };

  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      id: json['id'],
      level: LogLevel.values.firstWhere((e) => e.name == json['level']),
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      context: Map<String, dynamic>.from(json['context'] ?? {}),
      stackTrace: json['stackTrace'],
      userId: json['userId'],
      sessionId: json['sessionId'],
    );
  }
}

/// Configuration for error logging
class LoggerConfig {
  final bool enableFileLogging;
  final bool enableConsoleLogging;
  final bool enableRemoteLogging;
  final LogLevel minLogLevel;
  final int maxLogFileSize;
  final int maxLogFiles;
  final Duration logRetentionPeriod;

  const LoggerConfig({
    this.enableFileLogging = true,
    this.enableConsoleLogging = kDebugMode,
    this.enableRemoteLogging = false,
    this.minLogLevel = LogLevel.info,
    this.maxLogFileSize = 5 * 1024 * 1024, // 5MB
    this.maxLogFiles = 5,
    this.logRetentionPeriod = const Duration(days: 7),
  });
}

/// Enterprise-grade error logger
class ErrorLogger {
  static ErrorLogger? _instance;
  static ErrorLogger get instance => _instance ??= ErrorLogger._();

  ErrorLogger._();

  final LoggerConfig _config = const LoggerConfig();
  final List<LogEntry> _memoryBuffer = [];
  final StreamController<LogEntry> _logController = StreamController<LogEntry>.broadcast();

  String? _logDirectory;
  String? _currentLogFile;
  String? _sessionId;
  String? _userId;
  bool _isInitialized = false;

  // Getters
  Stream<LogEntry> get logStream => _logController.stream;
  List<LogEntry> get recentLogs => List.unmodifiable(_memoryBuffer);
  bool get isInitialized => _isInitialized;

  /// Initialize the logger
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Generate session ID
      _sessionId = DateTime.now().millisecondsSinceEpoch.toString();

      // Set up log directory
      if (_config.enableFileLogging) {
        await _setupLogDirectory();
      }

      // Load user ID if available
      await _loadUserId();

      // Clean up old logs
      await _cleanupOldLogs();

      _isInitialized = true;

      await logInfo('ErrorLogger initialized', context: {
        'sessionId': _sessionId,
        'logDirectory': _logDirectory,
        'config': {
          'enableFileLogging': _config.enableFileLogging,
          'enableConsoleLogging': _config.enableConsoleLogging,
          'minLogLevel': _config.minLogLevel.name,
        },
      });
    } catch (error) {
      if (kDebugMode) {
        print('Failed to initialize ErrorLogger: $error');
      }
    }
  }

  /// Log an error with full context
  Future<void> logError(
    AppError error, {
    String? context,
    Map<String, dynamic>? additionalContext,
  }) async {
    final logContext = <String, dynamic>{
      'errorCode': error.code,
      'errorType': error.runtimeType.toString(),
      'severity': error.severity.name,
      'isRetryable': error.isRetryable,
      'maxRetries': error.maxRetries,
      'userMessage': error.userMessage,
      'context': context,
      ...error.context,
      ...?additionalContext,
    };

    await _log(
      level: _mapSeverityToLogLevel(error.severity),
      message: error.message,
      context: logContext,
      stackTrace: error.stackTrace,
    );
  }

  /// Log informational message
  Future<void> logInfo(
    String message, {
    Map<String, dynamic>? context,
  }) async {
    await _log(
      level: LogLevel.info,
      message: message,
      context: context ?? {},
    );
  }

  /// Log warning message
  Future<void> logWarning(
    String message, {
    Map<String, dynamic>? context,
  }) async {
    await _log(
      level: LogLevel.warning,
      message: message,
      context: context ?? {},
    );
  }

  /// Log debug message
  Future<void> logDebug(
    String message, {
    Map<String, dynamic>? context,
  }) async {
    await _log(
      level: LogLevel.debug,
      message: message,
      context: context ?? {},
    );
  }

  /// Log critical message
  Future<void> logCritical(
    String message, {
    Map<String, dynamic>? context,
    String? stackTrace,
  }) async {
    await _log(
      level: LogLevel.critical,
      message: message,
      context: context ?? {},
      stackTrace: stackTrace,
    );
  }

  /// Core logging method
  Future<void> _log({
    required LogLevel level,
    required String message,
    required Map<String, dynamic> context,
    String? stackTrace,
  }) async {
    // Check if we should log this level
    if (level.index < _config.minLogLevel.index) {
      return;
    }

    final logEntry = LogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      level: level,
      message: message,
      timestamp: DateTime.now(),
      context: context,
      stackTrace: stackTrace,
      userId: _userId,
      sessionId: _sessionId,
    );

    // Add to memory buffer
    _memoryBuffer.add(logEntry);
    if (_memoryBuffer.length > 100) {
      _memoryBuffer.removeAt(0);
    }

    // Emit to stream
    _logController.add(logEntry);

    // Console logging
    if (_config.enableConsoleLogging) {
      _logToConsole(logEntry);
    }

    // File logging
    if (_config.enableFileLogging && _logDirectory != null) {
      await _logToFile(logEntry);
    }

    // Remote logging (if implemented)
    if (_config.enableRemoteLogging) {
      await _logToRemote(logEntry);
    }
  }

  /// Log to console with formatting
  void _logToConsole(LogEntry entry) {
    final timestamp = entry.timestamp.toIso8601String();
    final level = entry.level.name.toUpperCase().padRight(8);
    final message = entry.message;
    
    String logLine = '[$timestamp] $level $message';
    
    if (entry.context.isNotEmpty) {
      logLine += '\n  Context: ${jsonEncode(entry.context)}';
    }
    
    if (entry.stackTrace != null) {
      logLine += '\n  Stack: ${entry.stackTrace}';
    }

    // Use appropriate print method based on level
    switch (entry.level) {
      case LogLevel.debug:
        debugPrint(logLine);
        break;
      case LogLevel.error:
      case LogLevel.critical:
        debugPrint('🔴 $logLine');
        break;
      case LogLevel.warning:
        debugPrint('🟡 $logLine');
        break;
      default:
        debugPrint(logLine);
    }
  }

  /// Log to file with rotation
  Future<void> _logToFile(LogEntry entry) async {
    try {
      if (_currentLogFile == null) {
        await _createNewLogFile();
      }

      final file = File(_currentLogFile!);
      
      // Check file size and rotate if necessary
      if (await file.exists()) {
        final fileSize = await file.length();
        if (fileSize > _config.maxLogFileSize) {
          await _rotateLogFile();
        }
      }

      // Write log entry
      final logLine = '${jsonEncode(entry.toJson())}\n';
      await file.writeAsString(logLine, mode: FileMode.append);
    } catch (error) {
      if (kDebugMode) {
        print('Failed to write to log file: $error');
      }
    }
  }

  /// Log to remote service (placeholder for implementation)
  Future<void> _logToRemote(LogEntry entry) async {
    // Implementation would depend on your remote logging service
    // This could be Firebase Crashlytics, Sentry, custom API, etc.
    if (kDebugMode) {
      print('Remote logging: ${entry.toJson()}');
    }
  }

  /// Set up log directory
  Future<void> _setupLogDirectory() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _logDirectory = '${appDir.path}/logs';
      
      final logDir = Directory(_logDirectory!);
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }
    } catch (error) {
      if (kDebugMode) {
        print('Failed to setup log directory: $error');
      }
    }
  }

  /// Create new log file
  Future<void> _createNewLogFile() async {
    if (_logDirectory == null) return;

    final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
    _currentLogFile = '$_logDirectory/app_log_$timestamp.jsonl';
  }

  /// Rotate log file when it gets too large
  Future<void> _rotateLogFile() async {
    await _createNewLogFile();
    await _cleanupOldLogs();
  }

  /// Clean up old log files
  Future<void> _cleanupOldLogs() async {
    if (_logDirectory == null) return;

    try {
      final logDir = Directory(_logDirectory!);
      if (!await logDir.exists()) return;

      final files = await logDir.list().where((entity) => entity is File).cast<File>().toList();
      
      // Sort by modification time (newest first)
      files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      // Remove files beyond retention period
      final cutoffTime = DateTime.now().subtract(_config.logRetentionPeriod);
      final filesToDelete = files.where((file) => file.lastModifiedSync().isBefore(cutoffTime));
      
      for (final file in filesToDelete) {
        await file.delete();
      }

      // Remove excess files beyond max count
      if (files.length > _config.maxLogFiles) {
        final excessFiles = files.skip(_config.maxLogFiles);
        for (final file in excessFiles) {
          await file.delete();
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('Failed to cleanup old logs: $error');
      }
    }
  }

  /// Load user ID from preferences
  Future<void> _loadUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _userId = prefs.getString('user_id');
    } catch (error) {
      if (kDebugMode) {
        print('Failed to load user ID: $error');
      }
    }
  }

  /// Set user ID for logging context
  Future<void> setUserId(String? userId) async {
    _userId = userId;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      if (userId != null) {
        await prefs.setString('user_id', userId);
      } else {
        await prefs.remove('user_id');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Failed to save user ID: $error');
      }
    }
  }

  /// Map error severity to log level
  LogLevel _mapSeverityToLogLevel(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return LogLevel.info;
      case ErrorSeverity.medium:
        return LogLevel.warning;
      case ErrorSeverity.high:
        return LogLevel.error;
      case ErrorSeverity.critical:
        return LogLevel.critical;
    }
  }

  /// Get log files for export or analysis
  Future<List<File>> getLogFiles() async {
    if (_logDirectory == null) return [];

    try {
      final logDir = Directory(_logDirectory!);
      if (!await logDir.exists()) return [];

      final files = await logDir.list().where((entity) => entity is File).cast<File>().toList();
      return files;
    } catch (error) {
      if (kDebugMode) {
        print('Failed to get log files: $error');
      }
      return [];
    }
  }

  /// Export logs as JSON string
  Future<String> exportLogs() async {
    final logs = _memoryBuffer.map((entry) => entry.toJson()).toList();
    return jsonEncode(logs);
  }

  /// Clear all logs
  Future<void> clearLogs() async {
    _memoryBuffer.clear();
    
    if (_logDirectory != null) {
      try {
        final logDir = Directory(_logDirectory!);
        if (await logDir.exists()) {
          await logDir.delete(recursive: true);
          await _setupLogDirectory();
        }
      } catch (error) {
        if (kDebugMode) {
          print('Failed to clear log files: $error');
        }
      }
    }
  }

  /// Get logging statistics
  Map<String, dynamic> getLoggingStats() {
    final levelCounts = <String, int>{};
    for (final entry in _memoryBuffer) {
      levelCounts[entry.level.name] = (levelCounts[entry.level.name] ?? 0) + 1;
    }

    return {
      'totalLogs': _memoryBuffer.length,
      'levelCounts': levelCounts,
      'sessionId': _sessionId,
      'userId': _userId,
      'logDirectory': _logDirectory,
      'currentLogFile': _currentLogFile,
    };
  }

  /// Dispose resources
  void dispose() {
    _logController.close();
  }
}
