// Enterprise-grade retry mechanism with exponential backoff
// Implements patterns used by major cloud providers and tech companies

import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'app_error.dart';

/// Configuration for retry behavior
class RetryConfig {
  final int maxAttempts;
  final Duration baseDelay;
  final Duration maxDelay;
  final double backoffMultiplier;
  final double jitterFactor;
  final bool enableJitter;

  const RetryConfig({
    this.maxAttempts = 3,
    this.baseDelay = const Duration(seconds: 1),
    this.maxDelay = const Duration(seconds: 30),
    this.backoffMultiplier = 2.0,
    this.jitterFactor = 0.1,
    this.enableJitter = true,
  });

  /// Predefined configurations for common scenarios
  static const RetryConfig network = RetryConfig(
    maxAttempts: 3,
    baseDelay: Duration(seconds: 1),
    maxDelay: Duration(seconds: 10),
    backoffMultiplier: 2.0,
  );

  static const RetryConfig database = RetryConfig(
    maxAttempts: 2,
    baseDelay: Duration(milliseconds: 500),
    maxDelay: Duration(seconds: 5),
    backoffMultiplier: 1.5,
  );

  static const RetryConfig critical = RetryConfig(
    maxAttempts: 5,
    baseDelay: Duration(seconds: 2),
    maxDelay: Duration(seconds: 60),
    backoffMultiplier: 2.0,
  );
}

/// Result of a retry operation
class RetryResult<T> {
  final T? data;
  final AppError? error;
  final int attemptCount;
  final Duration totalDuration;
  final bool isSuccess;

  const RetryResult({
    this.data,
    this.error,
    required this.attemptCount,
    required this.totalDuration,
    required this.isSuccess,
  });

  factory RetryResult.success(T data, int attemptCount, Duration totalDuration) {
    return RetryResult<T>(
      data: data,
      attemptCount: attemptCount,
      totalDuration: totalDuration,
      isSuccess: true,
    );
  }

  factory RetryResult.failure(AppError error, int attemptCount, Duration totalDuration) {
    return RetryResult<T>(
      error: error,
      attemptCount: attemptCount,
      totalDuration: totalDuration,
      isSuccess: false,
    );
  }
}

/// Circuit breaker states for preventing cascading failures
enum CircuitBreakerState {
  closed,    // Normal operation
  open,      // Failing fast, not attempting calls
  halfOpen,  // Testing if service has recovered
}

/// Circuit breaker for preventing cascading failures
class CircuitBreaker {
  final String name;
  final int failureThreshold;
  final Duration timeout;
  final Duration resetTimeout;

  CircuitBreakerState _state = CircuitBreakerState.closed;
  int _failureCount = 0;
  DateTime? _lastFailureTime;
  DateTime? _nextAttemptTime;

  CircuitBreaker({
    required this.name,
    this.failureThreshold = 5,
    this.timeout = const Duration(seconds: 60),
    this.resetTimeout = const Duration(seconds: 30),
  });

  CircuitBreakerState get state => _state;
  int get failureCount => _failureCount;

  bool get canExecute {
    switch (_state) {
      case CircuitBreakerState.closed:
        return true;
      case CircuitBreakerState.open:
        if (_nextAttemptTime != null && DateTime.now().isAfter(_nextAttemptTime!)) {
          _state = CircuitBreakerState.halfOpen;
          return true;
        }
        return false;
      case CircuitBreakerState.halfOpen:
        return true;
    }
  }

  void recordSuccess() {
    _failureCount = 0;
    _state = CircuitBreakerState.closed;
    _lastFailureTime = null;
    _nextAttemptTime = null;
  }

  void recordFailure() {
    _failureCount++;
    _lastFailureTime = DateTime.now();

    if (_failureCount >= failureThreshold) {
      _state = CircuitBreakerState.open;
      _nextAttemptTime = DateTime.now().add(resetTimeout);
    }
  }

  Map<String, dynamic> toJson() => {
    'name': name,
    'state': _state.name,
    'failureCount': _failureCount,
    'lastFailureTime': _lastFailureTime?.toIso8601String(),
    'nextAttemptTime': _nextAttemptTime?.toIso8601String(),
  };
}

/// Enterprise-grade retry manager with circuit breaker pattern
class RetryManager {
  static final Map<String, CircuitBreaker> _circuitBreakers = {};
  static final Random _random = Random();

  /// Execute a function with retry logic and circuit breaker protection
  static Future<RetryResult<T>> execute<T>(
    Future<T> Function() operation, {
    RetryConfig config = RetryConfig.network,
    String? circuitBreakerKey,
    bool Function(AppError error)? shouldRetry,
    void Function(int attempt, AppError error)? onRetry,
  }) async {
    final stopwatch = Stopwatch()..start();
    CircuitBreaker? circuitBreaker;

    // Initialize circuit breaker if key provided
    if (circuitBreakerKey != null) {
      circuitBreaker = _circuitBreakers.putIfAbsent(
        circuitBreakerKey,
        () => CircuitBreaker(name: circuitBreakerKey),
      );

      if (!circuitBreaker.canExecute) {
        return RetryResult.failure(
          NetworkError(
            type: NetworkErrorType.serverError,
            code: 'CIRCUIT_BREAKER_OPEN',
            message: 'Circuit breaker is open for $circuitBreakerKey',
            userMessage: 'Service is temporarily unavailable. Please try again later.',
            context: {'circuitBreaker': circuitBreaker.toJson()},
          ),
          0,
          stopwatch.elapsed,
        );
      }
    }

    AppError? lastError;
    
    for (int attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        final result = await operation();
        circuitBreaker?.recordSuccess();
        
        return RetryResult.success(result, attempt, stopwatch.elapsed);
      } catch (exception) {
        final error = ErrorFactory.fromException(exception, context: 'Attempt $attempt');
        lastError = error;

        // Record failure in circuit breaker
        circuitBreaker?.recordFailure();

        // Check if we should retry
        final shouldRetryError = shouldRetry?.call(error) ?? error.isRetryable;
        
        if (attempt >= config.maxAttempts || !shouldRetryError) {
          break;
        }

        // Calculate delay with exponential backoff and jitter
        final delay = _calculateDelay(attempt, config);
        
        // Notify about retry
        onRetry?.call(attempt, error);
        
        if (kDebugMode) {
          print('Retry attempt $attempt failed: ${error.message}. Retrying in ${delay.inMilliseconds}ms');
        }

        // Wait before next attempt
        await Future.delayed(delay);
      }
    }

    return RetryResult.failure(
      lastError ?? SystemError(
        type: SystemErrorType.unknown,
        code: 'RETRY_UNKNOWN_ERROR',
        message: 'Unknown error during retry operation',
        userMessage: 'An unexpected error occurred. Please try again.',
      ),
      config.maxAttempts,
      stopwatch.elapsed,
    );
  }

  /// Calculate delay with exponential backoff and optional jitter
  static Duration _calculateDelay(int attempt, RetryConfig config) {
    // Calculate exponential backoff
    final exponentialDelay = config.baseDelay.inMilliseconds * 
        pow(config.backoffMultiplier, attempt - 1);
    
    // Apply jitter if enabled
    double finalDelay = exponentialDelay.toDouble();
    if (config.enableJitter) {
      final jitter = finalDelay * config.jitterFactor * (_random.nextDouble() - 0.5);
      finalDelay += jitter;
    }

    // Ensure delay doesn't exceed maximum
    final delayMs = finalDelay.clamp(
      config.baseDelay.inMilliseconds.toDouble(),
      config.maxDelay.inMilliseconds.toDouble(),
    );

    return Duration(milliseconds: delayMs.round());
  }

  /// Get circuit breaker status for monitoring
  static Map<String, dynamic> getCircuitBreakerStatus() {
    return _circuitBreakers.map((key, breaker) => MapEntry(key, breaker.toJson()));
  }

  /// Reset a specific circuit breaker
  static void resetCircuitBreaker(String key) {
    final breaker = _circuitBreakers[key];
    if (breaker != null) {
      breaker.recordSuccess();
    }
  }

  /// Reset all circuit breakers
  static void resetAllCircuitBreakers() {
    for (final breaker in _circuitBreakers.values) {
      breaker.recordSuccess();
    }
  }

  /// Remove a circuit breaker
  static void removeCircuitBreaker(String key) {
    _circuitBreakers.remove(key);
  }

  /// Clear all circuit breakers
  static void clearAllCircuitBreakers() {
    _circuitBreakers.clear();
  }
}

/// Utility class for common retry patterns
class RetryPatterns {
  /// Retry pattern for network operations
  static Future<RetryResult<T>> networkOperation<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) {
    return RetryManager.execute(
      operation,
      config: RetryConfig.network,
      circuitBreakerKey: operationName != null ? 'network_$operationName' : null,
      onRetry: (attempt, error) {
        if (kDebugMode) {
          print('Network operation retry $attempt: ${error.userMessage}');
        }
      },
    );
  }

  /// Retry pattern for database operations
  static Future<RetryResult<T>> databaseOperation<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) {
    return RetryManager.execute(
      operation,
      config: RetryConfig.database,
      circuitBreakerKey: operationName != null ? 'database_$operationName' : null,
      shouldRetry: (error) => error is SystemError && error.type == SystemErrorType.databaseError,
      onRetry: (attempt, error) {
        if (kDebugMode) {
          print('Database operation retry $attempt: ${error.message}');
        }
      },
    );
  }

  /// Retry pattern for critical operations
  static Future<RetryResult<T>> criticalOperation<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) {
    return RetryManager.execute(
      operation,
      config: RetryConfig.critical,
      circuitBreakerKey: operationName != null ? 'critical_$operationName' : null,
      onRetry: (attempt, error) {
        if (kDebugMode) {
          print('Critical operation retry $attempt: ${error.userMessage}');
        }
      },
    );
  }
}
