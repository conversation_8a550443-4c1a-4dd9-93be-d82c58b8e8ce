// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:get/get.dart';

// Project imports:
import '../../l10n/localization.dart';
import '../../services/version_service.dart';
import '../../services/notification_service.dart';
import '../../models/app_notification.dart';

/// Dialog types for queue management
enum DialogType {
  versionUpdate,
  notification,
  tutorial,
  permission,
  custom,
}

/// Dialog configuration for queue
class DialogConfig {
  final DialogType type;
  final String id;
  final Widget Function(BuildContext context) builder;
  final VoidCallback? onDismissed;
  final bool barrierDismissible;
  final int priority; // Lower number = higher priority

  const DialogConfig({
    required this.type,
    required this.id,
    required this.builder,
    this.onDismissed,
    this.barrierDismissible = true,
    this.priority = 100,
  });
}

/// Sequential Dialog Queue Manager
/// Ensures only one dialog is shown at a time and manages proper sequencing
class DialogQueueManager extends GetxController {
  static DialogQueueManager get instance => Get.find<DialogQueueManager>();

  final RxBool _isDialogShowing = false.obs;
  final RxBool _isInitialized = false.obs;
  final List<DialogConfig> _dialogQueue = [];
  DialogConfig? _currentDialog;

  bool get isDialogShowing => _isDialogShowing.value;
  bool get isInitialized => _isInitialized.value;

  @override
  void onInit() {
    super.onInit();
    _isInitialized.value = true;
  }

  /// Add a dialog to the queue
  void enqueueDialog(DialogConfig config) {
    // Check if dialog with same ID already exists
    if (_dialogQueue.any((d) => d.id == config.id)) {
      return; // Prevent duplicates
    }

    _dialogQueue.add(config);
    
    // Sort by priority (lower number = higher priority)
    _dialogQueue.sort((a, b) => a.priority.compareTo(b.priority));
    
    // Process queue if no dialog is currently showing
    if (!_isDialogShowing.value) {
      _processQueue();
    }
  }

  /// Remove a dialog from the queue
  void removeDialog(String id) {
    _dialogQueue.removeWhere((d) => d.id == id);
  }

  /// Clear all dialogs from the queue
  void clearQueue() {
    _dialogQueue.clear();
  }

  /// Process the next dialog in the queue
  void _processQueue() {
    if (_dialogQueue.isEmpty || _isDialogShowing.value) {
      return;
    }

    final nextDialog = _dialogQueue.removeAt(0);
    _showDialog(nextDialog);
  }

  /// Show a dialog and handle its lifecycle
  void _showDialog(DialogConfig config) {
    if (!Get.context!.mounted) return;

    _isDialogShowing.value = true;
    _currentDialog = config;

    showDialog(
      context: Get.context!,
      barrierDismissible: config.barrierDismissible,
      builder: config.builder,
    ).then((_) {
      // Dialog was dismissed
      _isDialogShowing.value = false;
      _currentDialog = null;
      
      // Call onDismissed callback
      config.onDismissed?.call();
      
      // Process next dialog in queue
      Future.delayed(const Duration(milliseconds: 300), () {
        _processQueue();
      });
    });
  }

  /// Initialize startup dialogs in proper sequence
  Future<void> initializeStartupDialogs() async {
    if (!_isInitialized.value) {
      await Future.delayed(const Duration(milliseconds: 100));
      return initializeStartupDialogs();
    }

    // 1. Check for version update dialog (highest priority)
    await _checkVersionUpdateDialog();

    // 2. Check for notification dialog (medium priority)
    await _checkNotificationDialog();

    // Note: Microphone permission will be handled separately when needed
  }

  /// Check and queue version update dialog
  Future<void> _checkVersionUpdateDialog() async {
    try {
      final versionService = VersionService();
      final needsForceUpdate = await versionService.checkForceUpdate();
      
      if (needsForceUpdate) {
        enqueueDialog(DialogConfig(
          type: DialogType.versionUpdate,
          id: 'version_update',
          priority: 1, // Highest priority
          barrierDismissible: false,
          builder: (context) => _buildVersionUpdateDialog(context),
        ));
      }
    } catch (e) {
      debugPrint('Error checking version update: $e');
    }
  }

  /// Check and queue notification dialog
  Future<void> _checkNotificationDialog() async {
    try {
      final notificationService = NotificationService();
      final hasNew = await notificationService.hasNewNotifications();
      
      if (hasNew) {
        final latestNotification = await notificationService.getLatestNotification();
        if (latestNotification != null) {
          enqueueDialog(DialogConfig(
            type: DialogType.notification,
            id: 'new_notification',
            priority: 2, // Medium priority
            builder: (context) => _buildNotificationDialog(context, latestNotification),
          ));
        }
      }
    } catch (e) {
      debugPrint('Error checking notifications: $e');
    }
  }

  /// Build version update dialog
  Widget _buildVersionUpdateDialog(BuildContext context) {
    final l10n = AppLocalizations.instance;
    return AlertDialog(
      title: Text(l10n.newVersionTitle),
      content: Text(l10n.forceUpdateMessage),
      actions: [
        TextButton(
          onPressed: () async {
            final versionService = VersionService();
            await versionService.openOfficialWebsite();
            // Note: App will exit after opening website
          },
          child: Text(l10n.updateNow),
        ),
      ],
    );
  }

  /// Build notification dialog
  Widget _buildNotificationDialog(BuildContext context, AppNotification notification) {
    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.notifications_active,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              notification.title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            notification.content,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.4,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            notification.formattedDate,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(AppLocalizations.instance.close),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            Get.toNamed('/notifications');
          },
          child: Text(AppLocalizations.instance.seeAll),
        ),
      ],
    );
  }

  /// Request microphone permission when needed
  Future<bool> requestMicrophonePermissionWhenNeeded() async {
    // Only request permission when no other dialogs are showing
    if (_isDialogShowing.value) {
      return false;
    }

    // This will be implemented to show permission dialog only when needed
    // For now, return true to maintain existing functionality
    return true;
  }
}
