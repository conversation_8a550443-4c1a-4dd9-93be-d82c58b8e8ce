// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project imports:
import '../../l10n/localization.dart';
import '../../services/version_service.dart';
import '../../services/notification_service.dart';
import '../../models/notification.dart';
import '../../widgets/feature_hint.dart';
import 'package:flutter_sound_record/flutter_sound_record.dart';

/// Dialog types for queue management
enum DialogType {
  versionUpdate,
  notification,
  tutorial,
  permission,
  custom,
}

/// Dialog configuration for queue
class DialogConfig {
  final DialogType type;
  final String id;
  final Widget Function(BuildContext context) builder;
  final VoidCallback? onDismissed;
  final bool barrierDismissible;
  final int priority; // Lower number = higher priority

  const DialogConfig({
    required this.type,
    required this.id,
    required this.builder,
    this.onDismissed,
    this.barrierDismissible = true,
    this.priority = 100,
  });
}

/// Sequential Dialog Queue Manager
/// Ensures only one dialog is shown at a time and manages proper sequencing
class DialogQueueManager extends GetxController {
  static DialogQueueManager get instance => Get.find<DialogQueueManager>();

  final RxBool _isDialogShowing = false.obs;
  final RxBool _isInitialized = false.obs;
  final List<DialogConfig> _dialogQueue = [];
  bool _testMode = false;

  bool get isDialogShowing => _isDialogShowing.value;
  bool get isInitialized => _isInitialized.value;

  // For testing purposes
  List<DialogConfig> get dialogQueue => List.unmodifiable(_dialogQueue);

  /// Enable test mode to prevent automatic dialog processing
  void enableTestMode() {
    _testMode = true;
  }

  /// Disable test mode to allow normal dialog processing
  void disableTestMode() {
    _testMode = false;
  }

  @override
  void onInit() {
    super.onInit();
    _isInitialized.value = true;
  }

  /// Add a dialog to the queue
  void enqueueDialog(DialogConfig config) {
    // Check if dialog with same ID already exists
    if (_dialogQueue.any((d) => d.id == config.id)) {
      return; // Prevent duplicates
    }

    _dialogQueue.add(config);

    // Sort by priority (lower number = higher priority)
    _dialogQueue.sort((a, b) => a.priority.compareTo(b.priority));

    // Process queue if no dialog is currently showing and not in test mode
    if (!_isDialogShowing.value && !_testMode) {
      _processQueue();
    }
  }

  /// Remove a dialog from the queue
  void removeDialog(String id) {
    _dialogQueue.removeWhere((d) => d.id == id);
  }

  /// Clear all dialogs from the queue
  void clearQueue() {
    _dialogQueue.clear();
  }

  /// Process the next dialog in the queue
  void _processQueue() {
    if (_dialogQueue.isEmpty || _isDialogShowing.value) {
      return;
    }

    final nextDialog = _dialogQueue.removeAt(0);
    _showDialog(nextDialog);
  }

  /// Show a dialog and handle its lifecycle
  void _showDialog(DialogConfig config) {
    // Check if context is available (for testing)
    if (Get.context == null || !Get.context!.mounted) {
      // In test environment, just mark as showing and immediately dismiss
      _isDialogShowing.value = true;
      Future.delayed(const Duration(milliseconds: 100), () {
        _isDialogShowing.value = false;
        config.onDismissed?.call();
        Future.delayed(const Duration(milliseconds: 300), () {
          _processQueue();
        });
      });
      return;
    }

    _isDialogShowing.value = true;

    showDialog(
      context: Get.context!,
      barrierDismissible: config.barrierDismissible,
      builder: config.builder,
    ).then((_) {
      // Dialog was dismissed
      _isDialogShowing.value = false;

      // Call onDismissed callback
      config.onDismissed?.call();

      // Process next dialog in queue
      Future.delayed(const Duration(milliseconds: 300), () {
        _processQueue();
      });
    });
  }

  /// Initialize startup dialogs in proper sequence
  Future<void> initializeStartupDialogs() async {
    if (!_isInitialized.value) {
      await Future.delayed(const Duration(milliseconds: 100));
      return initializeStartupDialogs();
    }

    // 1. Check for version update dialog (highest priority)
    await _checkVersionUpdateDialog();

    // 2. Check for notification dialog (medium priority)
    await _checkNotificationDialog();

    // 3. Check for tutorial dialog (lower priority)
    await _checkTutorialDialog();

    // 4. Check for microphone permission (lowest priority)
    // This will show our custom explanation dialog, not the system dialog
    await _checkMicrophonePermission();
  }

  /// Check and queue version update dialog
  Future<void> _checkVersionUpdateDialog() async {
    try {
      final versionService = VersionService();

      // 首先检查服务器是否有更新信息
      await versionService.checkUpdateInBackground();

      // 然后检查是否需要显示强制更新对话框
      final needsForceUpdate = await versionService.checkForceUpdate();

      debugPrint('Version check - needsForceUpdate: $needsForceUpdate');
      debugPrint('Version check - current version: ${VersionService.currentVersion}');

      if (needsForceUpdate) {
        debugPrint('Queueing version update dialog');
        enqueueDialog(DialogConfig(
          type: DialogType.versionUpdate,
          id: 'version_update',
          priority: 1, // Highest priority
          barrierDismissible: false,
          builder: (context) => _buildVersionUpdateDialog(context),
        ));
      } else {
        debugPrint('No version update needed');
      }
    } catch (e) {
      debugPrint('Error checking version update: $e');
    }
  }

  /// Check and queue notification dialog
  Future<void> _checkNotificationDialog() async {
    try {
      final notificationService = NotificationService();
      final hasNew = await notificationService.hasNewNotifications();

      if (hasNew) {
        final latestNotification = await notificationService.getLatestNotification();
        if (latestNotification != null) {
          enqueueDialog(DialogConfig(
            type: DialogType.notification,
            id: 'new_notification',
            priority: 2, // Medium priority
            builder: (context) => _buildNotificationDialog(context, latestNotification),
          ));
        }
      }
    } catch (e) {
      debugPrint('Error checking notifications: $e');
    }
  }

  /// Check and queue tutorial dialog
  Future<void> _checkTutorialDialog() async {
    try {
      // Check if tutorial should be shown using FeatureHintManager
      const tutorialId = 'add_button_hint';
      final hintManager = FeatureHintManager();
      final shouldShow = await hintManager.shouldShowHint(tutorialId);

      if (shouldShow) {
        enqueueDialog(DialogConfig(
          type: DialogType.tutorial,
          id: tutorialId,
          priority: 3, // Lower priority than notifications
          builder: (context) => _buildTutorialDialog(context),
        ));
      }
    } catch (e) {
      debugPrint('Error checking tutorial dialog: $e');
    }
  }

  /// Check and queue microphone permission request
  Future<void> _checkMicrophonePermission() async {
    try {
      // 检查是否需要显示麦克风权限说明对话框
      // 我们不直接检查系统权限，而是检查用户是否已经看过说明
      final prefs = await SharedPreferences.getInstance();
      final hasSeenMicPermissionExplanation = prefs.getBool('has_seen_mic_permission_explanation') ?? false;

      if (!hasSeenMicPermissionExplanation) {
        enqueueDialog(DialogConfig(
          type: DialogType.permission,
          id: 'microphone_permission',
          priority: 4, // Lowest priority
          barrierDismissible: false,
          builder: (context) => _buildMicrophonePermissionDialog(context),
        ));
      }
    } catch (e) {
      debugPrint('Error checking microphone permission: $e');
    }
  }

  /// Build version update dialog
  Widget _buildVersionUpdateDialog(BuildContext context) {
    final l10n = AppLocalizations.instance;
    return AlertDialog(
      title: Text(l10n.newVersionTitle),
      content: Text(l10n.forceUpdateMessage),
      actions: [
        TextButton(
          onPressed: () async {
            final versionService = VersionService();

            // 标记这个版本的对话框已显示
            final latestVersion = (await SharedPreferences.getInstance()).getString('latest_version');
            if (latestVersion != null) {
              await versionService.markVersionDialogShown(latestVersion);
            }

            await versionService.openOfficialWebsite();
            // Note: App will exit after opening website
          },
          child: Text(l10n.updateNow),
        ),
      ],
    );
  }

  /// Build notification dialog
  Widget _buildNotificationDialog(BuildContext context, AppNotification notification) {
    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.notifications_active,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              notification.title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            notification.content,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.4,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            notification.date,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(AppLocalizations.instance.close),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            Get.toNamed('/notifications');
          },
          child: Text(AppLocalizations.instance.seeAll),
        ),
      ],
    );
  }

  /// Build tutorial dialog
  Widget _buildTutorialDialog(BuildContext context) {
    final l10n = AppLocalizations.instance;
    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.add_circle_outline,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              l10n.addButtonHintTitle,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.addButtonHintContent,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.4,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            // Mark as "don't show again"
            FeatureHintManager().dismissHint('add_button_hint');
            Navigator.of(context).pop();
          },
          child: Text("不再提醒"),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text("知道了"),
        ),
      ],
    );
  }

  /// Build microphone permission dialog
  Widget _buildMicrophonePermissionDialog(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.mic,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              "麦克风权限",
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "应用需要麦克风权限来录制语音笔记。点击允许后，系统将询问您是否授予麦克风权限。",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.4,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () async {
            // 先关闭说明对话框
            Navigator.of(context).pop();

            // 标记用户已看过麦克风权限说明
            final prefs = await SharedPreferences.getInstance();
            await prefs.setBool('has_seen_mic_permission_explanation', true);

            // 延迟一下确保对话框完全关闭
            await Future.delayed(const Duration(milliseconds: 300));

            // 现在触发系统权限请求
            try {
              final recorder = FlutterSoundRecord();
              await recorder.hasPermission();
            } catch (e) {
              debugPrint('Error requesting microphone permission: $e');
            }
          },
          child: Text("允许"),
        ),
      ],
    );
  }

  /// Request microphone permission when needed
  Future<bool> requestMicrophonePermissionWhenNeeded() async {
    // Only request permission when no other dialogs are showing
    if (_isDialogShowing.value) {
      return false;
    }

    // This will be implemented to show permission dialog only when needed
    // For now, return true to maintain existing functionality
    return true;
  }
}
