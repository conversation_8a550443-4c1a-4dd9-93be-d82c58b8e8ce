// Service initialization for enterprise-grade error handling system
// Properly initializes and configures all error handling services

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../error_handling/error_handler_service.dart';
import '../error_handling/error_logger.dart';
import '../connectivity/connectivity_service.dart';
import '../offline/offline_queue_manager.dart';
import '../../services/enhanced_ai_service.dart';

/// Service initialization configuration
class ServiceConfig {
  final bool enableErrorLogging;
  final bool enableOfflineSupport;
  final bool enableConnectivityMonitoring;
  final bool enableEnhancedAI;
  final bool enableDebugMode;

  const ServiceConfig({
    this.enableErrorLogging = true,
    this.enableOfflineSupport = true,
    this.enableConnectivityMonitoring = true,
    this.enableEnhancedAI = true,
    this.enableDebugMode = kDebugMode,
  });
}

/// Centralized service initializer for the error handling system
class ServiceInitializer {
  static bool _isInitialized = false;
  static ServiceConfig? _config;

  /// Initialize all error handling services
  static Future<void> initialize({ServiceConfig? config}) async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('Services already initialized');
      }
      return;
    }

    _config = config ?? const ServiceConfig();

    try {
      if (kDebugMode) {
        print('Initializing enterprise error handling services...');
      }

      // Initialize services in dependency order
      await _initializeErrorLogger();
      await _initializeErrorHandler();
      await _initializeConnectivityService();
      await _initializeOfflineQueueManager();
      await _initializeEnhancedAIService();

      _isInitialized = true;

      if (kDebugMode) {
        print('All services initialized successfully');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Failed to initialize services: $error');
      }
      rethrow;
    }
  }

  /// Initialize error logger
  static Future<void> _initializeErrorLogger() async {
    if (!_config!.enableErrorLogging) return;

    try {
      await ErrorLogger.instance.initialize();
      
      if (kDebugMode) {
        print('✓ ErrorLogger initialized');
      }
    } catch (error) {
      if (kDebugMode) {
        print('✗ Failed to initialize ErrorLogger: $error');
      }
      rethrow;
    }
  }

  /// Initialize error handler service
  static Future<void> _initializeErrorHandler() async {
    try {
      final errorHandlerConfig = ErrorHandlerConfig(
        enableLogging: _config!.enableErrorLogging,
        enableUserNotifications: true,
        enableRetryPrompts: true,
        enableDebugMode: _config!.enableDebugMode,
      );

      Get.put(ErrorHandlerService(config: errorHandlerConfig), permanent: true);
      
      if (kDebugMode) {
        print('✓ ErrorHandlerService initialized');
      }
    } catch (error) {
      if (kDebugMode) {
        print('✗ Failed to initialize ErrorHandlerService: $error');
      }
      rethrow;
    }
  }

  /// Initialize connectivity service
  static Future<void> _initializeConnectivityService() async {
    if (!_config!.enableConnectivityMonitoring) return;

    try {
      Get.put(ConnectivityService(), permanent: true);
      
      // Wait for initialization to complete
      await ConnectivityService.instance.initialize();
      
      if (kDebugMode) {
        print('✓ ConnectivityService initialized');
      }
    } catch (error) {
      if (kDebugMode) {
        print('✗ Failed to initialize ConnectivityService: $error');
      }
      rethrow;
    }
  }

  /// Initialize offline queue manager
  static Future<void> _initializeOfflineQueueManager() async {
    if (!_config!.enableOfflineSupport) return;

    try {
      Get.put(OfflineQueueManager(), permanent: true);
      
      if (kDebugMode) {
        print('✓ OfflineQueueManager initialized');
      }
    } catch (error) {
      if (kDebugMode) {
        print('✗ Failed to initialize OfflineQueueManager: $error');
      }
      rethrow;
    }
  }

  /// Initialize enhanced AI service
  static Future<void> _initializeEnhancedAIService() async {
    if (!_config!.enableEnhancedAI) return;

    try {
      Get.put(EnhancedAIService(), permanent: true);
      
      if (kDebugMode) {
        print('✓ EnhancedAIService initialized');
      }
    } catch (error) {
      if (kDebugMode) {
        print('✗ Failed to initialize EnhancedAIService: $error');
      }
      rethrow;
    }
  }

  /// Check if services are initialized
  static bool get isInitialized => _isInitialized;

  /// Get current configuration
  static ServiceConfig? get config => _config;

  /// Reinitialize services (useful for testing or configuration changes)
  static Future<void> reinitialize({ServiceConfig? config}) async {
    await dispose();
    _isInitialized = false;
    await initialize(config: config);
  }

  /// Dispose all services
  static Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      if (kDebugMode) {
        print('Disposing services...');
      }

      // Dispose services in reverse order
      if (Get.isRegistered<EnhancedAIService>()) {
        Get.delete<EnhancedAIService>();
      }

      if (Get.isRegistered<OfflineQueueManager>()) {
        Get.delete<OfflineQueueManager>();
      }

      if (Get.isRegistered<ConnectivityService>()) {
        Get.delete<ConnectivityService>();
      }

      if (Get.isRegistered<ErrorHandlerService>()) {
        Get.delete<ErrorHandlerService>();
      }

      // Dispose error logger
      ErrorLogger.instance.dispose();

      _isInitialized = false;
      _config = null;

      if (kDebugMode) {
        print('All services disposed');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error disposing services: $error');
      }
    }
  }

  /// Get service status for monitoring
  static Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'config': _config != null ? {
        'enableErrorLogging': _config!.enableErrorLogging,
        'enableOfflineSupport': _config!.enableOfflineSupport,
        'enableConnectivityMonitoring': _config!.enableConnectivityMonitoring,
        'enableEnhancedAI': _config!.enableEnhancedAI,
        'enableDebugMode': _config!.enableDebugMode,
      } : null,
      'services': {
        'errorLogger': ErrorLogger.instance.isInitialized,
        'errorHandler': Get.isRegistered<ErrorHandlerService>(),
        'connectivity': Get.isRegistered<ConnectivityService>(),
        'offlineQueue': Get.isRegistered<OfflineQueueManager>(),
        'enhancedAI': Get.isRegistered<EnhancedAIService>(),
      },
      'connectivity': Get.isRegistered<ConnectivityService>() 
          ? ConnectivityService.instance.getConnectivityStats()
          : null,
      'offlineQueue': Get.isRegistered<OfflineQueueManager>()
          ? OfflineQueueManager.instance.getQueueStats()
          : null,
      'errorHandler': Get.isRegistered<ErrorHandlerService>()
          ? ErrorHandlerService.instance.getErrorStats()
          : null,
    };
  }

  /// Validate service dependencies
  static bool validateDependencies() {
    if (!_isInitialized) return false;

    final requiredServices = <Type, bool>{
      ErrorHandlerService: _config!.enableErrorLogging,
      ConnectivityService: _config!.enableConnectivityMonitoring,
      OfflineQueueManager: _config!.enableOfflineSupport,
      EnhancedAIService: _config!.enableEnhancedAI,
    };

    for (final entry in requiredServices.entries) {
      if (entry.value && !Get.isRegistered(entry.key)) {
        if (kDebugMode) {
          print('Missing required service: ${entry.key}');
        }
        return false;
      }
    }

    return true;
  }

  /// Get service health check
  static Future<Map<String, dynamic>> healthCheck() async {
    final health = <String, dynamic>{
      'overall': 'healthy',
      'services': <String, dynamic>{},
      'timestamp': DateTime.now().toIso8601String(),
    };

    try {
      // Check error logger
      if (_config!.enableErrorLogging) {
        health['services']['errorLogger'] = {
          'status': ErrorLogger.instance.isInitialized ? 'healthy' : 'unhealthy',
          'stats': ErrorLogger.instance.getLoggingStats(),
        };
      }

      // Check connectivity service
      if (_config!.enableConnectivityMonitoring && Get.isRegistered<ConnectivityService>()) {
        final connectivity = ConnectivityService.instance;
        health['services']['connectivity'] = {
          'status': 'healthy',
          'isOnline': connectivity.isOnline,
          'quality': connectivity.networkQuality.name,
          'stats': connectivity.getConnectivityStats(),
        };
      }

      // Check offline queue
      if (_config!.enableOfflineSupport && Get.isRegistered<OfflineQueueManager>()) {
        final queue = OfflineQueueManager.instance;
        health['services']['offlineQueue'] = {
          'status': 'healthy',
          'queueLength': queue.queueLength,
          'isProcessing': queue.isProcessing,
          'stats': queue.getQueueStats(),
        };
      }

      // Check enhanced AI service
      if (_config!.enableEnhancedAI && Get.isRegistered<EnhancedAIService>()) {
        final aiService = EnhancedAIService.instance;
        health['services']['enhancedAI'] = {
          'status': 'healthy',
          'stats': aiService.getServiceStats(),
        };
      }

      // Check error handler
      if (Get.isRegistered<ErrorHandlerService>()) {
        final errorHandler = ErrorHandlerService.instance;
        health['services']['errorHandler'] = {
          'status': 'healthy',
          'stats': errorHandler.getErrorStats(),
        };
      }

    } catch (error) {
      health['overall'] = 'unhealthy';
      health['error'] = error.toString();
    }

    return health;
  }

  /// Reset all services to clean state
  static Future<void> reset() async {
    if (!_isInitialized) return;

    try {
      if (kDebugMode) {
        print('Resetting services...');
      }

      // Reset individual services
      if (Get.isRegistered<OfflineQueueManager>()) {
        await OfflineQueueManager.instance.clearCompleted();
      }

      if (Get.isRegistered<ErrorHandlerService>()) {
        ErrorHandlerService.instance.clearErrorHistory();
      }

      if (Get.isRegistered<EnhancedAIService>()) {
        EnhancedAIService.instance.reset();
      }

      if (kDebugMode) {
        print('Services reset completed');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error resetting services: $error');
      }
    }
  }
}
