// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class AppLocalizations {
  static AppLocalizations? _instance;

  static void init(BuildContext context) {
    _instance = Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static AppLocalizations get instance {
    assert(_instance != null, 'AppLocalizations not initialized. Call AppLocalizations.init() first.');
    return _instance!;
  }

  // 保留 of 方法供系统使用
  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  AppLocalizations(this.locale);

  final Locale locale;

  static const _localizedValues = <String, Map<String, String>>{
    'en': {
      'timelineAppBarTitle': 'Tempognize·知时',
      'SocratesWords': 'I cannot teach anyone anything, I can only make them think.',
      'Socrates': 'by <PERSON><PERSON>',
      'homeTitle': 'Home',
      'helloWorld': 'Hello World!',
      'settings': 'Settings',
      'language': 'Language',
      'system': 'System',
      'chinese': '中文',
      'english': 'English',
      'colorScheme': 'Color Scheme',
      'profile': 'Profile',
      'home': 'Home',
      'explore': 'Explore',
      'edit': 'Edit',
      'noteLifespan': 'Note Lifespan',
      'days': 'days',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'search': 'Search',
      'searchPlaceholder': 'Search Notes...',
      'clearSearchHistory': 'Clear Search History',
      'clearSearchHistoryConfirm': 'Are you sure you want to clear all search history?',
      'clearSearchHistoryConfirmDescription': 'Clearing will make it impossible to recover',
      'clearConfirm': 'Confirm Clear',
      'setNoteLifespan': 'Set Note Lifespan',
      'enterNumberBetween': 'A number between 8 and 30',
      'timeline': 'Timeline',
      'trending': 'Trending',
      'favorites': 'Favorites',
      'descAddFiveAI': 'Plus 5 AI quotas',
      'removeFavorites': 'Remove from favorites',
      'coreSettings': 'Core Settings',
      'aiKeyManagement': 'AI Key Management',
      'aiKey': 'AI Key',
      'apiKey': 'API Key',
      'apiUrl': 'API URL',
      'model': 'Model',
      'apiKeyPlaceholder': 'Enter API Key',
      'apiUrlPlaceholder': 'Enter API URL',
      'modelPlaceholder': 'Select Model',
      'lookAndFeel': 'Look and Feel',
      'lookAndFeelDescription': 'Set the appearance and feel of the app',
      'lookAndFeelTitle': 'Look and Feel',
      'lookAndFeelSubtitle': 'Set the appearance and feel of the app',
      'createdAt': 'Created At',
      'favoritedNotes': 'Favorite',
      'neverDelete': 'Never Delete',
      'noFavoritedNotes': 'No Favorited Notes',
      'waitingForAIResponse': 'Waiting for AI response...',
      'copiedToClipboard': 'Copied to clipboard',
      'copy': 'Copy',
      'retry': 'Retry',
      'retryingAIResponse': 'Retrying AI response...',
      'aiResponseParsingFailed': 'AI response parsing failed',
      'networkRequestFailed': 'Network request failed',
      'networkError': 'Network error',
      'aiUpdateSuccess': 'AI update success',
      'configurationName': 'Configuration Name',
      'modelName': 'Model Name',
      'newModelConfiguration': 'New Model Configuration',
      'modelType': 'Model Type',
      'modelTypePlaceholder': 'Enter Model Type',
      'modelTypeDescription': 'Model type, such as: gpt-4o-mini',
      'verifyAndSave': 'Verify and Save',
      'configurationNameHint': 'Only used to help you distinguish different configurations',
      'apiKeyHint': 'Copy and paste more accurately',
      'cannotFindLinkedNote': 'Cannot find linked note',
      'waitingForAITitle': 'Waiting for AI to name the note...',
      'createdNewNote': 'New note created, waiting for AI to name the note...',
      'createNoteFailed': 'Create note failed',
      'clipboardIsEmpty': 'Clipboard is empty',
      'waitingForAIGenerateTitleAndCategories': 'Waiting for AI to name the note...',
      'createdNewNoteAndWaitingForAIResponse': 'New note created, waiting for AI to name the note...',
      'ask': 'Ask',
      'askPlaceholder': 'Please enter your question...',
      'noRelatedNotesFound': 'No related notes found',
      'notesCreatedFormat': '%d Notes collected.',
      'contributionGraphTitle': 'From %s to now in 364 days',
      'logout': 'Logout',
      'login': 'Login',
      'notLogin': 'Not Login',
      'clickAvatarToLogin': 'Click Avatar to Login',
      'pleaseEnterValidEmail': 'Please enter a valid email address',
      'pleaseEnterEmail': 'Please enter email',
      'pleaseEnterPassword': 'Please enter password',
      'passwordAtLeast6Characters': 'Password must be at least 6 characters',
      'email': 'Email',
      'password': 'Password',
      'loginFailed': 'Login Failed',
      'loginFailedDescription': 'Please check your email and password',
      'emailAutoRegister': 'Email auto register account when first login',
      'loginExpired': 'Your login has expired, please log in again.',
      'loginNotLogin': 'You are not logged in, only browsing is allowed.',
      'alreadyLoggedIn': 'You are already logged in, please log out first.',
      'clickPlusButtonToInputText': 'Click + button to input text.',
      'longPressPlusButtonToPasteClipboardText': 'Long press + button to say something.',
      'timelineTitle': 'Here is the end of the universe',
      'daysLaterDelete': 'days left',
      'hoursLaterDelete': 'hours left',
      'minutesLaterDelete': 'minutes left',
      'what': 'What',
      'why': 'Why',
      'howToDo': 'How',
      'more': 'More',
      'debate': 'Debate',
      'askByMe': 'Enter Question',
      'example': 'Example',
      'delete': 'Delete',
      'clear': 'Clear',
      'jsonParseError': 'JSON parsing error',
      'remainingQuota': 'Remaining Quota',
      'totalQuota': 'Total Quota',
      'usedQuota': 'Used Quota',
      'quotaLevel': 'Quota Level',
      'times': 'times',
      'reGetNote': 'Re-get Note',
      'reGetNoteConfirm': 'Are you sure you want to re-get the AI response? The current response will be overwritten.',
      'clearConfirmDescription': 'Are you sure you want to clear all highlighted text? This action cannot be undone.',
      'deleteConfirm': 'Confirm Delete',
      'deleteConfirmDescription': 'Are you sure you want to delete this note? This action cannot be undone.',
      'confirmDeleteSelectedNotes': 'Are you sure you want to delete selected %d notes?',
      'clearAllHighlights': 'Clear All Highlights',
      'share': 'Share',
      'askAboutFullNote': 'Please enter your question about the full note',
      'askAboutFullNoteTitle': 'Ask about the full note',
      'askAboutHighlightedText': 'Please enter your question about the highlighted text',
      'askAboutHighlightedTextTitle': 'Ask about the highlighted text',
      'noteStillGenerating': 'Note still generating...',
      'openLinkedNote': 'Open Linked Note',
      'pleaseLoginFirst': 'Please login first',
      'canDeleteFavNote': 'Can delete saved note.',
      'selectedNoteDeleted': 'Selected Notes Deleted.',
      'deleteFailed': 'Delete failed.',
      'gettingData': 'Getting Data.',
      'quotaExceeded': 'Quota Exceeded',
      'pending': 'Pending',
      'otherError': 'Other Error',
      'userCancel': 'User canceled.',
      'generateTitleAndCategoriesPrompt': '''
Please generate a short title for the following content and classify it into three levels.
- Please answer in English.
- Here is the original text:
''',
      'askPrompt': '''
Now someone asks you a question, please answer accordingly.
- Please answer in English.
- Please answer patiently and carefully
- Here are the questions asked by the other party:
%s
''',
      'whatPrompt': '''
Now someone is reading an article and has a question about a paragraph. He wants to know what this paragraph means, or what this noun means, or where this address is, etc.?
- Please answer in English
- Here is the article the other party reads:
%s
- Here is the text provided by the other party:
%s
''',
      'whyPrompt': '''
Now someone reads an article and has a question about a paragraph. He wants you to understand the cause and effect of this paragraph.
- Please answer in English
- Analyze the cause and effect
- Proof by contradiction can be used when necessary
- Deep logical relationship may need to be designed
- Here is the article the other party reads:
%s
- Here is the text provided by the other party:
%s
''',
      'morePrompt': '''
Now someone reads an article and wants to do more in-depth study and research on a paragraph of the text. He wants to learn more about the knowledge points involved in this paragraph, especially in-depth knowledge, which may include the definition of key terms, upstream and downstream knowledge points, causal relationship knowledge points, or the core and extension knowledge of the theory, but other more appropriate classification methods can also be used.
- Please answer in English.
- The following is the article read by the other party:
%s
- The following is the text provided by the other party:
%s
''',
      'howtoPrompt': '''
Now someone reads an article and wants to know what is mentioned in one paragraph. He is very interested and wants to know what to do and what to pay attention to.
- Please answer in English
- Give specific methods and steps
- Give attention to
- Here is the article the other party reads:
%s
- Here is the text provided by the other party:
%s
''',
      'debatePrompt': '''
Now someone reads an article, and he hopes you can help dialectically analyze the viewpoint expressed in one paragraph.
- Please answer in English
- You need to use a debate method to give both positive and negative views
- You need to explain the connection between the positive and negative aspects
- The answer must be explained by giving examples
- Here is the article the other party reads:
%s
- Here is the text provided by the other party:
%s
''',
      'examplePrompt': '''
Now someone is reading an article and is very interested in a paragraph. I hope you can give an example.
- You need to give at least three examples to explain in detail the knowledge in the field or operation methods involved in this paragraph.
- Please answer in English
- Here is the article the other party read:
%s
- Here is the text provided by the other party:
%s
''',
      'askAboutHighlightedTextPrompt': '''
Now someone reads an article and has some questions and thoughts about a paragraph. Please give your professional opinions based on the content of the article, the text he is interested in and his thoughts.
- Please answer in English
- Here is the article the other party reads:
%s
- Here is the text the other party is interested in:
%s
- Here is the other party's thoughts:
%s
''',
      'askAboutFullNotePrompt': '''
Now someone reads an article and has some questions and ideas. Please give your professional opinions based on the content of the article and his ideas.
- Please answer in English
- Here is the article the other party reads:
%s
- Here are the other party's ideas:
%s
''',
      'getQuizQuestion': '''
Please generate 3 exam questions based on the content of the knowledge notes below. The questions should not exceed the scope of this article.
- Return in json format
- The following is an example of json format:
- {"questions":[{"id":1,"question":"Question 1's stem","options":[{"id":"A","text":"Question 1's option A"},{"id":"B","text":"Question 1's option B"},{"id":"C","text":"Question 1's option C"}],"correct_answer":"Correct answer id for question 1"},{"id":2,"question":"Question 2's stem","options":[{"id":"A","text":"Question 2's option A"}, {"id":"B","text":"Question 2 Option B"},{"id":"C","text":"Question 2 Option C"}],"correct_answer":"Correct answer id for question 2"},{"id":3,"question":"Question 3 Question Stem","options":[{"id":"A","text":"Question 3 Option A"},{"id":"B","text":"Question 3 Option B"},{"id":"C","text":"Question 3 Option C"}],"correct_answer":"Correct answer id for question 3"}]}
- The question needs to be in English.
- In terms of difficulty, the three questions should be of increasing difficulty. The first question is a basic question, the second question is a medium-difficulty question, and the third question is a difficult question.
- Don't have the same correct answer for every question, for example, the correct answer for all three questions should be A. There should be 3 different correct answer IDs.- The question needs to be multiple choice, with 3 options.
- The question needs to be single choice, with only 1 correct answer.
- The following is the original knowledge note:
%s
''',
      'answerQuiz': 'Answer the quiz',
      'generateQuizFailed': 'Generate quiz failed, please try again',
      'generateQuizSuccess': 'Generate quiz success',
      'generateQuizDescription':
          'To permanently save this note, you need to answer three related questions first. After answering all questions correctly, you can permanently save this note and get 5 AI conversation opportunities.',
      'quizQuestion': 'Question',
      'submitAnswer': 'Submit answer',
      'answerCorrect': '✓ Answer correct',
      'answerIncorrect': '✗ Answer incorrect',
      'answerCorrectDescription': 'Answer correctly, get 5 AI conversation opportunities!',
      'addQuotaFailed': 'Add quota failed',
      'addQuotaSuccess': 'Add quota success',
      'quizResult': 'Quiz result',
      'quizResultDescription': 'Knowledge test',
      'quizTitleDescription':
          'Please answer the following questions, and you can add the note to favorites after answering all questions correctly.',
      'quizAllCorrect': 'Congratulations, you answered all questions correctly!',
      'quizNotAllCorrect': 'Sorry, the quiz was not answered correctly, and the note cannot be added to favorites.',
      'addNoteToFavorites': 'Add to favorites',
      'return': 'Return',
      'loginAgreementPrefix': 'Login means you agree to',
      'termsOfService': 'Terms of Service',
      'and': 'and',
      'privacyPolicy': 'Privacy Policy',
      'aboutTempognize': 'About Tempognize',
      'unknownError': 'Unknown error',
      'getInterestingKnowledge': '''
I hope to learn about %s. I have read the following articles:
%s
- I hope you can provide me with other knowledge related to this field besides these articles to broaden my horizons.
- Please answer in English
''',
      'getDifferentKnowledge': 'Get More Interesting Knowledge',
      'pleaseAgreeToTerms': 'Please agree to the Terms of Service and Privacy Policy',
      'addButtonHintTitle': 'Quick Tips',
      'addButtonHintContent':
          '• Click the "+" button to manually enter the question\n• Long press the "+" button to enable quick voice input',
      'resetFeatureHints': 'Reset Feature Hints',
      'resetFeatureHintsDescription': 'Show all feature hints again',
      'resetFeatureHintsConfirm':
          'Are you sure you want to reset all feature hints? You will see all tips and tutorials again.',
      'resetFeatureHintsSuccess': 'Feature hints have been reset',
      'textSelectionHintTitle': 'Smart Text Selection',
      'textSelectionHintContent':
          'Long press for 300 milliseconds to activate text selection mode, use your finger to circle the text you want to highlight, and the selected text can be used to ask AI questions',
      'doNotRemind': 'Don\'t remind me again',
      'iKnow': 'I know',
      'questionButtonHintTitle': 'Question button',
      'myQuizzes': 'My quizzes',
      'quiz': 'Quiz',
      'tapToStartQuiz': 'Tap to start quiz',
      'review': 'Review',
      'reviewWithAward': 'Review - Add 3 AI quota',
      'noQuizzes': 'No quizzes for this note, you can generate a new quiz.',
      'generateQuiz': 'Generate a new quiz',
      'deleteAccount': 'Delete Account',
      'deleteAccountConfirm': 'Delete Account Confirmation',
      'deleteAccountDescription':
          'Are you sure you want to delete your account? This action cannot be undone. Your remaining AI quota will be cleared.',
      'deleteAccountSuccess': 'Account deleted successfully',
      'deleteAccountFailed': 'Failed to delete account',
      'selectImageSource': 'Select Image Source',
      'selectFromGallery': 'Select from Gallery',
      'takePhoto': 'Take Photo',
      'editImage': 'Edit Image',
      'imageSavedSuccessfully': 'Image saved successfully',
      'failedToSaveImage': 'Failed to save image',
      'transcribeAudio': 'Long press to record audio',
      'recording': 'Recording...',
      'recordingFailed': 'Recording failed',
      'recordingNotFound': 'Recording not found',
      'transcribing': 'Transcribing...',
      'transcribeFailed': 'Transcribe failed',
      'longPressToRecord': 'Long press to record',
      'voiceInput': 'Voice input',
      'recordingTooShort': 'Recording time is less than 2 seconds, cannot transcribe',
      'recordingTooQuiet': 'The volume change is too small or the average volume is too low, it may be white noise',
      'noRecordingPermission': 'No recording permission',
      'transcribeCancelled': 'Transcribe cancelled',
      'cancelTranscribing': 'Cancel transcribing',
      'recordingCancelled': 'Recording cancelled',
      'stopRecording': 'Stop recording',
      'pasteFromClipboard': 'Paste from clipboard',
      'pasteFromClipboardQuestion': 'Are you sure you want to paste the clipboard content as a new note?',
      'releaseToStop': 'Release to stop',
      'releaseToCancel': 'Release to cancel',
      'clearTitleImageCache': 'Clear Category Image Cache',
      'clearTitleImageCacheDescription': 'Delete all downloaded category images',
      'clearTitleImageCacheConfirm': 'Are you sure to delete all downloaded category images?',
      'clearTitleImageCacheSuccess': 'Category image cache cleared',
      'switchToGridView': 'Switch to Grid View',
      'switchToListView': 'Switch to List View',
      'justNow': 'Just now',
      'minutesAgo': '%d minutes ago',
      'hoursAgo': '%d hours ago',
      'daysAgo': '%d days ago',
      'translate': 'Translate',
      'originalText': 'Original Text',
      'translatedText': 'Translated Text',
      'alternativeTranslations': 'Alternative Translations',
      'selectLanguage': 'Select Target Language',
      'newVersionTitle': 'New Version Available',
      'forceUpdateMessage': 'A new version is required to continue using the app. Would you like to update now?',
      'updateNow': 'Update Now',
      'about': 'About',
      'version': 'Version',
      'message': 'Message',
      'recommendKnowledge': 'Recommend Knowledge',
      'noteCategory': 'Note Category',
      'proModeDescription': 'Pro mode requires more AI usage balance and longer processing time, be patient.',
      'waitingForAIResponseLast': 'Has waited for %ss.',
      'trainingDataGenerationSuccess': 'New training content generated',
      'trainingDataGenerationFailed': 'Failed to generate training data',
      'databaseLoadFailedGenerateNew': 'Database load failed, new training content will be generated',
      'previousTrainingProgressLoaded': 'Previous training progress loaded',
      'failedToLoadTrainingData': 'Failed to load training data: ',
      'saveFailed': 'Save failed: ',
      'willContinueWithoutSaving': 'will continue but progress won\'t be saved',
      'playbackFailed': 'Playback failed: ',
      'microphonePermissionRequired': 'Microphone permission required',
      'speechRecognitionFailed': 'Could not recognize speech content',
      'voiceTranscriptionFailed': 'Voice transcription failed: ',
      'evaluationFailed': 'Evaluation failed: ',
      'loadFailed': 'Load failed: ',
      'overallAssessmentFailed': 'Failed to generate overall assessment: ',
      'regenerateTrainingContentFailed': 'Failed to regenerate training content: ',
      'playbackControlFailed': 'Playback control failed: ',
      'initializeDefaultNotes': 'Initialize Default Notes',
      'initializeDefaultNotesDescription': 'Create product introduction and user guide notes',
      'initializeDefaultNotesConfirm': 'Initialize Default Notes',
      'initializeDefaultNotesConfirmDescription':
          'This will create product introduction and user guide notes. Continue?',
      'initializeDefaultNotesSuccess': 'Default notes initialized successfully',
      'unsupportedQuestion': 'Unsupported question',
      'translationTraining': 'Study abroad',
      'ttsTest': 'TTS Test',
      'enterTextToSpeak': 'Enter text to speak',
      'speak': 'Speak',
      'stopping': 'Stopping...',
      'editScreenTitle': 'Edit Note',
      'favoritedNotesFormat': 'Total Favorites: %d Notes',
      'imageDownloadSuccessful': 'Image Download Successful',
      'notifications': 'Notifications',
      'noNotifications': 'No notifications available',
      'failedToLoadNotifications': 'Failed to load notifications',
      'tryAgain': 'Try Again',
      'goToSettingsForAllNotifications': 'You can view all notifications in Settings.',
      'close': 'Close',
      'seeAll': 'See All',
      'submit': 'Submit',
      'preItem': 'Previous',
      'nextItem': 'Next',
      'nextSentence': 'Next',
      'blackboard': 'Blackboard content',
      'keyWords': 'Key words',
      'teacherExplanation': 'Teacher',
      'yourUnderstanding': 'Your understanding',
      'watchSummary': 'Summary',
      'knowledgeItem': 'Knowledge Item',
      'sayYourUnderstanding': 'Tell me what you understand about the teacher' 's explanation...',
      'tryMore': 'Try again',
      'inputByVoice': 'Voice',
      'stop': 'Stop',
      'adjustPlaybackSpeed': 'Adjust playback speed',
      'hideText': 'Hide text',
      'showText': 'Show text',
      'regenerateAudio': 'Regenerate audio',
      'continuePlay': 'Continue',
      'ebbinghausHintTitle': 'Wait 7 Days Before Favoriting',
      'ebbinghausHintContent':
          'According to the Ebbinghaus forgetting curve, waiting 7 days before favoriting a note helps with better learning and memory retention. This gives your brain time to process and consolidate the information.',
      'gotIt': 'Got it',
      'parentNote': 'Master Note',
      'childNotes': 'Topic Notes',
      'noChildNotes': 'No child notes yet',
      'noteHierarchy': 'Note Hierarchy',
      'today': 'Today',
      'yesterday': 'Yesterday',
      'faqAlreadyExists': 'FAQ already exists for this note',
    },
    'zh': {
      'timelineAppBarTitle': 'Tempognize·知时',
      'SocratesWords': '我不能教任何人任何东西，我只能让他们思考。',
      'Socrates': '—— 苏格拉底',
      'homeTitle': '主页',
      'helloWorld': '你好,世界!',
      'settings': '设置',
      'language': '语言',
      'system': '跟随系统',
      'chinese': '中文',
      'english': 'English',
      'colorScheme': '颜色方案',
      'profile': '我的',
      'home': '首页',
      'explore': '探索',
      'edit': '编辑',
      'noteLifespan': '笔记寿命',
      'days': '天',
      'cancel': '取消',
      'confirm': '确定',
      'search': '搜索',
      'searchPlaceholder': '搜索笔记...',
      'clearSearchHistory': '清除搜索历史',
      'clearSearchHistoryConfirm': '是否清除全部搜索历史？',
      'clearSearchHistoryConfirmDescription': '清除后，将无法恢复',
      'clearConfirm': '确认清除',
      'setNoteLifespan': '设置笔记寿命',
      'enterNumberBetween': '请输入8到30之间的数字',
      'timeline': '时间线',
      'trending': '热搜',
      'favorites': '收藏',
      'descAddFiveAI': '增加5次AI余额',
      'removeFavorites': '取消收藏',
      'coreSettings': '核心设置',
      'aiKeyManagement': 'AI密钥管理',
      'aiKey': 'AI密钥',
      'apiKey': 'API密钥',
      'apiUrl': 'API URL',
      'model': '模型',
      'apiKeyPlaceholder': '请输入API密钥',
      'apiUrlPlaceholder': '请输入API URL',
      'modelPlaceholder': '请选择模型',
      'lookAndFeel': '外观和感觉',
      'lookAndFeelDescription': '设置应用的外观和感觉',
      'lookAndFeelTitle': '外观和感觉',
      'lookAndFeelSubtitle': '设置应用的外观和感觉',
      'createdAt': '创建时间',
      'favoritedNotes': '收藏笔记',
      'neverDelete': '永久保存',
      'noFavoritedNotes': '暂无收藏的笔记',
      'waitingForAIResponse': '正在等待AI响应...',
      'copiedToClipboard': '已复制到剪贴板',
      'copy': '复制',
      'retry': '重试',
      'retryingAIResponse': '正在重新获取AI回复...',
      'aiResponseParsingFailed': 'AI 响应解析失败，如果多次出现这个问题，可以考虑更换大语言模型',
      'networkRequestFailed': '网络请求失败',
      'networkError': '网络异常',
      'aiUpdateSuccess': 'AI 更新成功',
      'configurationName': '配置名称',
      'modelName': '模型名称',
      'newModelConfiguration': '新建模型配置',
      'modelType': '模型类型',
      'modelTypePlaceholder': '请输入模型类型',
      'modelTypeDescription': '模型类型，例如：gpt-4o-mini',
      'verifyAndSave': '验证并保存',
      'configurationNameHint': '仅用于帮助您区分不同的配置',
      'apiKeyHint': '拷贝粘贴更准确哟',
      'cannotFindLinkedNote': '无法找到链接的笔记',
      'firstRunSettings': '首次运行设置',
      'firstRunSettingsDescription': '欢迎使用！首次运行需要配置 AI API Key，是否现在进行配置？',
      'waitingForAITitle': '等待AI给笔记命名……',
      'createdNewNote': '已创建新笔记，正在等待AI给笔记命名……',
      'createNoteFailed': '创建笔记失败',
      'clipboardIsEmpty': '剪贴板为空',
      'waitingForAIGenerateTitleAndCategories': '等待AI给笔记命名……',
      'createdNewNoteAndWaitingForAIResponse': '已创建新笔记，正在获取AI回复……',
      'ask': '提问',
      'askPlaceholder': '请输入您的问题...',
      'noRelatedNotesFound': '没有找到相关笔记',
      'notesCreatedFormat': '总收集了 %d 条笔记',
      'contributionGraphTitle': '从 %s 至今的364天中',
      'logout': '退出登录',
      'login': '注册 / 登录',
      'notLogin': '未登录',
      'clickAvatarToLogin': '点击头像登录',
      'email': '邮箱',
      'password': '密码',
      'pleaseEnterValidEmail': '请输入有效的邮箱地址',
      'pleaseEnterEmail': '请输入邮箱',
      'pleaseEnterPassword': '请输入密码',
      'passwordAtLeast6Characters': '密码至少6位',
      'loginFailed': '登录失败',
      'loginFailedDescription': '请检查邮箱和密码是否正确',
      'emailAutoRegister': '邮箱初次登录自动注册账号',
      'loginExpired': '您的登录已过期，请重新登录。',
      'loginNotLogin': '你还没有登录，只能浏览。',
      'alreadyLoggedIn': '你已经登录了，请先退出登录。',
      'clickPlusButtonToInputText': '点击 + 按钮：输入文字。',
      'longPressPlusButtonToPasteClipboardText': '长按 + 按钮：语音输入文字。',
      'timelineTitle': '这里是宇宙的尽头',
      'daysLaterDelete': '天后删除',
      'hoursLaterDelete': '小时后删除',
      'minutesLaterDelete': '分钟后删除',
      'what': '是什么',
      'why': '为什么',
      'howToDo': '怎么做',
      'more': '深入点',
      'debate': '辩一下',
      'askByMe': '手动输入问题',
      'example': '举个例',
      'delete': '删除',
      'clear': '清除',
      'jsonParseError': 'JSON解析错误',
      'remainingQuota': '余额',
      'totalQuota': '总配额',
      'usedQuota': '已用配额',
      'quotaLevel': '配额等级',
      'times': '次',
      'reGetNote': '重新获取笔记',
      'reGetNoteConfirm': '确定要重新获取AI回复吗？当前的回复内容将被覆盖。',
      'clearConfirmDescription': '确定要清除所有高亮文字吗？此操作不可撤销。',
      'deleteConfirm': '确认删除',
      'deleteConfirmDescription': '您确定要删除这个笔记吗？此操作不可撤销。',
      'confirmDeleteSelectedNotes': '确定要删除选中的 %d 条笔记吗？',
      'clearAllHighlights': '清除所有高亮',
      'share': '分享',
      'askAboutFullNote': '请输入您关于全文的问题',
      'askAboutFullNoteTitle': '问全文',
      'askAboutHighlightedText': '请输入您关于高亮文字的问题',
      'askAboutHighlightedTextTitle': '问高亮文字',
      'noteStillGenerating': '笔记生成中...',
      'openLinkedNote': '打开链接笔记',
      'pleaseLoginFirst': '请先登录',
      'canDeleteFavNote': '收藏的笔记不能被删除',
      'selectedNoteDeleted': '已删除选中的笔记',
      'deleteFailed': '删除错误',
      'gettingData': '正在获取数据',
      'quotaExceeded': '配额不足',
      'pending': '等待处理',
      'otherError': '发生未知错误',
      'userCancel': '用户取消',
      'generateTitleAndCategoriesPrompt': '''
请对以下内容生成一个简短的标题，并进行三级分类。
- 请你用中文回答
- 下面是原文：
''',
      'askPrompt': '''
现在有人向你请教问题，请根据回答。
- 请你用中文回答
- 请你回答的仔细且认真
- 下面是对方向你提出的问题：
%s
''',
      'whatPrompt': '''
现在有人阅读到一篇文章，他对其中一段文字有问题，他希望了解这段文字是什么意思，或者这个名词是什么意思，或者这个地址在哪里等等？
- 请你用中文回答
- 下面是对方阅读到的文章：
%s
- 下面是对方有问题的文字：
%s
''',
      'whyPrompt': '''
现在有人阅读到一篇文章，他对其中一段文字有问题，他希望你了解这段文字的因果关系。
- 请你用中文回答
- 要分析因果关系
- 必要时可以用反证法进行证明
- 可能需要设计深层次的逻辑关系
- 下面是对方阅读到的文章：
%s
- 下面是对方希望了解因果关系的文字：
%s
''',
      'morePrompt': '''
现在有人阅读到一篇文章，他希望针对其中一段文字，做更深入的学习和研究，希望了解更多与这段文字所涉及知识点相关的更多信息，尤其是深入的知识，可能包括关键名词的定义、上下游知识点、因果关系知识点、或者理论的内核与外延知识，但也可以使用其他更合适的分类方式。
- 请你用中文回答。
- 下面是对方阅读到的文章：
%s
- 下面是对方希望深入研究的文字：
%s
''',
      'howtoPrompt': '''
现在有人阅读到一篇文章，他希望了解其中一段文字所提到的内容很感兴趣，希望了解具体应该怎么做，做什么，有什么注意事项？
- 请你用中文回答
- 要给出具体的方法和步骤
- 要给出注意事项
- 下面是对方阅读到的文章：
%s
- 下面是对方提到的文章中的具体文字：
%s
''',
      'debatePrompt': '''
现在有人阅读到一篇文章，他希望你能帮忙辩证地分析其中一段文字所表达的观点。
- 请你用中文回答
- 你要用辩论的方式，给出正反两面的观点
- 你要讲解正面和反面之间的联系
- 回答必须用举例的方法进行讲解
- 下面是对方阅读到的文章：
%s
- 下面是对方希望你辩证分析的文字：
%s
''',
      'examplePrompt': '''
现在有人阅读到一篇文章，对其中一段文字很感兴趣，希望你能举例说明。
- 你需要给出起码三个以上的例子，详细说明这段文字所涉及的领域的知识，或者操作方法等。
- 请你用中文回答
- 下面是对方阅读到的文章：
%s
- 下面是对方感兴趣的文字：
%s
''',
      'askAboutHighlightedTextPrompt': '''
现在有人阅读到一篇文章，他对其中一段文字有一些问题和想法，请根据文章内容、他感兴趣的文字和他的想法，给出你专业的意见。
- 请你用中文回答
- 下面是对方阅读到的文章：
%s
- 下面是对方感兴趣的文字：
%s
- 下面是对方的想法：
%s
''',
      'askAboutFullNotePrompt': '''
现在有人阅读到一篇文章，他有了一些问题和想法，请根据文章内容和他的想法，给出你专业的意见。
- 请你用中文回答
- 下面是对方阅读到的文章：
%s
- 下面是对方的想法：
%s
''',
      'getQuizQuestion': '''
请根据下面知识笔记的内容，生成3个考试题目。题目不要超出这篇文章内容的范围。
- 要用json格式返回
- 下面是json格式示例：
- {"questions":[{"id":1,"question":"题目1的题干","options":[{"id":"A","text":"题目1的选项A"},{"id":"B","text":"题目1的选项B"},{"id":"C","text":"题目1的选项C"}],"correct_answer":"题目1的正确答案id"},{"id":2,"question":"题目2的题干","options":[{"id":"A","text":"题目2的选项A"},{"id":"B","text":"题目2的选项B"},{"id":"C","text":"题目2的选项C"}],"correct_answer":"A"},{"id":3,"question":"题目3的题干","options":[{"id":"题目2的正确答案id","text":"题目3的选项A"},{"id":"B","text":"题目3的选项B"},{"id":"C","text":"题目3的选项C"}],"correct_answer":"题目3的正确答案id"}]}
- 题目需要是中文的。
- 从难度上说，三道题目的难度需要递进，第一题是基础题，第二题是中等难度题，第三题是难题。
- 不要每道题的正确答案都是同样的选项，例如3个题目的正确答案都是A。应该有3个不同的正确答案ID。
- 题目需要是选择题，提供3个选项。
- 题目需要是单选题，只能有1个正确答案。
- 下面是知识笔记原文：
%s
''',
      'answerQuiz': '回答问题',
      'generateQuizFailed': '生成题目失败，请重试',
      'generateQuizSuccess': '生成题目成功',
      'generateQuizDescription': '要永久保存这篇笔记，需要先回答三个相关的问题。完全回答正确后，可以永久保存这篇笔记。并且增加5次AI使用机会。',
      'quizQuestion': '问题',
      'submitAnswer': '提交答案',
      'answerCorrect': '✓ 回答正确',
      'answerIncorrect': '✗ 回答错误',
      'answerCorrectDescription': '答题正确，获得5次AI对话机会！',
      'addQuotaFailed': '增加配额失败',
      'addQuotaSuccess': '增加配额成功',
      'quizResult': '答题结果',
      'quizResultDescription': '知识检验',
      'quizTitleDescription': '请回答以下问题，全部回答正确后可以将笔记添加到收藏。',
      'quizAllCorrect': '恭喜你全部回答正确！',
      'quizNotAllCorrect': '很遗憾，答题未全部正确，不能添加到收藏。',
      'addNoteToFavorites': '添加到收藏',
      'return': '返回',
      'loginAgreementPrefix': '登录即表示您同意',
      'termsOfService': '服务条款',
      'and': '和',
      'privacyPolicy': '隐私政策',
      'aboutTempognize': '关于知时',
      'unknownError': '未知错误',
      'getInterestingKnowledge': '''
我希望学习 %s 的知识。我已经阅读过以下文章：
%s 
- 我希望你给我提供一个这些文章之外的、这个领域相关的其他知识，以拓展我的视野。
- 请用中文回答
''',
      'getDifferentKnowledge': '来点不一样的',
      'pleaseAgreeToTerms': '请同意服务条款和隐私政策',
      'addButtonHintTitle': '快速提示',
      'addButtonHintContent': '• 点击"+"按钮手动输入问题\n• 长按"+"按钮开启快捷语音输入',
      'resetFeatureHints': '重置功能提示',
      'resetFeatureHintsDescription': '重新显示所有功能提示',
      'resetFeatureHintsConfirm': '确定要重置所有功能提示吗？您将重新看到所有提示和教程。',
      'resetFeatureHintsSuccess': '功能提示已重置',
      'textSelectionHintTitle': '智能文本选择',
      'textSelectionHintContent': '长按300毫秒激活文本选择模式，使用手指圈选您想要高亮的文本，选中的文字可以用来向AI提问',
      'doNotRemind': '不再提醒',
      'iKnow': '我知道了',
      'questionButtonHintTitle': '提问功能',
      'myQuizzes': '我的测验',
      'quiz': '测验',
      'tapToStartQuiz': '点击开始测验',
      'review': '复习',
      'reviewWithAward': '复习 - 全对可以增加3次AI余额',
      'noQuizzes': '没有这篇笔记的复习资料，您可以生成新的试卷。',
      'generateQuiz': '生成新的试卷',
      'deleteAccount': '注销账户',
      'deleteAccountConfirm': '注销账户确认',
      'deleteAccountDescription': '您确定要注销账户吗？此操作不可撤销。您的剩余AI使用配额将被清零。',
      'deleteAccountSuccess': '账户已成功注销',
      'deleteAccountFailed': '注销账户失败',
      'selectImageSource': '选择图片来源',
      'selectFromGallery': '从相册选择',
      'takePhoto': '拍照',
      'editImage': '编辑图片',
      'imageSavedSuccessfully': '图片已成功保存到相册',
      'failedToSaveImage': '保存图片失败',
      'transcribeAudio': '长按录音',
      'recording': '正在录音...',
      'recordingFailed': '录音失败',
      'recordingNotFound': '未找到录音文件',
      'transcribing': '正在转写...',
      'transcribeFailed': '转写失败',
      'longPressToRecord': '长按开始录音',
      'voiceInput': '语音输入',
      'recordingTooShort': '录音时间短于2秒，无法转写',
      'recordingTooQuiet': '音量变化太小或平均音量太低，可能是白噪音',
      'noRecordingPermission': '没有录音权限',
      'transcribeCancelled': '取消语音识别',
      'cancelTranscribing': '取消转写',
      'recordingCancelled': '录音已取消',
      'stopRecording': '停止录音',
      'pasteFromClipboard': '从剪贴板粘贴',
      'pasteFromClipboardQuestion': '确定要粘贴剪贴板内容作为笔记吗？',
      'releaseToStop': '松开停止录音',
      'releaseToCancel': '松开取消录音',
      'clearTitleImageCache': '清除分类图片缓存',
      'clearTitleImageCacheDescription': '删除所有已下载的分类图片',
      'clearTitleImageCacheConfirm': '确定要删除所有已下载的分类图片吗？',
      'clearTitleImageCacheSuccess': '分类图片缓存已清除',
      'switchToGridView': '切换到网格视图',
      'switchToListView': '切换到列表视图',
      'justNow': '刚刚',
      'minutesAgo': '%d分钟前',
      'hoursAgo': '%d小时前',
      'daysAgo': '%d天前',
      'translate': '翻译',
      'originalText': '原文',
      'translatedText': '译文',
      'alternativeTranslations': '其他翻译',
      'selectLanguage': '选择目标语言',
      'newVersionTitle': '发现新版本',
      'forceUpdateMessage': '当前版本需要更新才能继续使用，是否立即更新？',
      'updateNow': '立即更新',
      'about': '关于',
      'version': '版本',
      'message': '消息',
      'recommendKnowledge': '推荐知识点',
      'noteCategory': '笔记分类',
      'proModeDescription': 'Pro模式需要消耗更多的AI使用余额，需要更长的时间，请耐心等待。',
      'waitingForAIResponseLast': '已等待%s秒.',
      'trainingDataGenerationSuccess': '已生成新的训练内容',
      'trainingDataGenerationFailed': '生成训练数据失败',
      'databaseLoadFailedGenerateNew': '数据库加载失败，将生成新的训练内容',
      'previousTrainingProgressLoaded': '已加载上次的训练进度',
      'failedToLoadTrainingData': '加载训练数据失败: ',
      'saveFailed': '保存失败: ',
      'willContinueWithoutSaving': '将继续进行但不会保存进度',
      'playbackFailed': '播放失败: ',
      'microphonePermissionRequired': '需要麦克风权限',
      'speechRecognitionFailed': '未能识别语音内容',
      'voiceTranscriptionFailed': '语音转写失败: ',
      'evaluationFailed': '评估失败: ',
      'loadFailed': '加载失败: ',
      'overallAssessmentFailed': '生成整体评估失败: ',
      'regenerateTrainingContentFailed': '重新生成训练内容失败: ',
      'playbackControlFailed': '播放控制失败: ',
      'initializeDefaultNotes': '初始化默认笔记',
      'initializeDefaultNotesDescription': '创建产品介绍和使用指南笔记',
      'initializeDefaultNotesConfirm': '初始化默认笔记',
      'initializeDefaultNotesConfirmDescription': '这将创建产品介绍和使用指南笔记，是否继续？',
      'initializeDefaultNotesSuccess': '默认笔记初始化成功',
      'unsupportedQuestion': '不支持的问题',
      'translationTraining': '模拟留学',
      'ttsTest': 'TTS测试',
      'enterTextToSpeak': '输入要朗读的文本',
      'speak': '朗读',
      'stopping': '停止中...',
      'editScreenTitle': '编辑笔记',
      'favoritedNotesFormat': '总收藏了 %d 条笔记',
      'imageDownloadSuccessful': '图片下载成功',
      'notifications': '通知',
      'noNotifications': '暂无通知',
      'failedToLoadNotifications': '加载通知失败',
      'tryAgain': '重试',
      'goToSettingsForAllNotifications': '您可以在设置页面查看所有通知。',
      'close': '关闭',
      'seeAll': '查看全部',
      'submit': '提交',
      'preItem': '上一项',
      'nextItem': '下一项',
      'nextSentence': '下一句',
      'blackboard': '黑板内容',
      'keyWords': '重点单词',
      'teacherExplanation': '教师讲解',
      'yourUnderstanding': '你的理解',
      'watchSummary': '查看训练总结',
      'knowledgeItem': '知识点',
      'sayYourUnderstanding': '说出你对教师讲解的理解...',
      'tryMore': '再试一次',
      'inputByVoice': '语音输入',
      'stop': '结束',
      'adjustPlaybackSpeed': '调整播放速度',
      'hideText': '隐藏文本',
      'showText': '显示文本',
      'regenerateAudio': '重新生成语音',
      'continuePlay': '继续',
      'ebbinghausHintTitle': '收藏前请等待7天',
      'ebbinghausHintContent': '根据艾宾浩斯遗忘曲线，等待7天后再收藏笔记有助于更好的学习和记忆保持。这给你的大脑足够的时间来处理和巩固信息。',
      'gotIt': '知道了',
      'parentNote': '主笔记',
      'childNotes': '子笔记',
      'noChildNotes': '暂无子笔记',
      'noteHierarchy': '笔记层级',
      'today': '今天',
      'yesterday': '昨天',
      'faqAlreadyExists': '该笔记已有FAQ总结',
    },
  };
  String get timelineAppBarTitle => _localizedValues[locale.languageCode]!['timelineAppBarTitle']!;
  String get socratesWords => _localizedValues[locale.languageCode]!['SocratesWords']!;
  String get socrates => _localizedValues[locale.languageCode]!['Socrates']!;
  String get homeTitle => _localizedValues[locale.languageCode]!['homeTitle']!;
  String get helloWorld => _localizedValues[locale.languageCode]!['helloWorld']!;
  String get settings => _localizedValues[locale.languageCode]!['settings']!;
  String get language => _localizedValues[locale.languageCode]!['language']!;
  String get system => _localizedValues[locale.languageCode]!['system']!;
  String get chinese => _localizedValues[locale.languageCode]!['chinese']!;
  String get english => _localizedValues[locale.languageCode]!['english']!;
  String get colorScheme => _localizedValues[locale.languageCode]!['colorScheme']!;
  String get profile => _localizedValues[locale.languageCode]!['profile']!;
  String get home => _localizedValues[locale.languageCode]!['home']!;
  String get explore => _localizedValues[locale.languageCode]!['explore']!;
  String get edit => _localizedValues[locale.languageCode]!['edit']!;
  String get noteLifespan => _localizedValues[locale.languageCode]!['noteLifespan']!;
  String get days => _localizedValues[locale.languageCode]!['days']!;
  String get cancel => _localizedValues[locale.languageCode]!['cancel']!;
  String get confirm => _localizedValues[locale.languageCode]!['confirm']!;
  String get search => _localizedValues[locale.languageCode]!['search']!;
  String get searchPlaceholder => _localizedValues[locale.languageCode]!['searchPlaceholder']!;
  String get clearSearchHistory => _localizedValues[locale.languageCode]!['clearSearchHistory']!;
  String get clearSearchHistoryConfirm => _localizedValues[locale.languageCode]!['clearSearchHistoryConfirm']!;
  String get clearSearchHistoryConfirmDescription =>
      _localizedValues[locale.languageCode]!['clearSearchHistoryConfirmDescription']!;
  String get clearConfirm => _localizedValues[locale.languageCode]!['clearConfirm']!;
  String get setNoteLifespan => _localizedValues[locale.languageCode]!['setNoteLifespan']!;
  String get enterNumberBetween => _localizedValues[locale.languageCode]!['enterNumberBetween']!;
  String get timeline => _localizedValues[locale.languageCode]!['timeline']!;
  String get trending => _localizedValues[locale.languageCode]!['trending']!;
  String get favorites => _localizedValues[locale.languageCode]!['favorites']!;
  String get descAddFiveAI => _localizedValues[locale.languageCode]!['descAddFiveAI']!;
  String get removeFavorites => _localizedValues[locale.languageCode]!['removeFavorites']!;
  String get coreSettings => _localizedValues[locale.languageCode]!['coreSettings']!;
  String get aiKeyManagement => _localizedValues[locale.languageCode]!['aiKeyManagement']!;
  String get aiKey => _localizedValues[locale.languageCode]!['aiKey']!;
  String get apiKey => _localizedValues[locale.languageCode]!['apiKey']!;
  String get apiUrl => _localizedValues[locale.languageCode]!['apiUrl']!;
  String get model => _localizedValues[locale.languageCode]!['model']!;
  String get apiKeyPlaceholder => _localizedValues[locale.languageCode]!['apiKeyPlaceholder']!;
  String get apiUrlPlaceholder => _localizedValues[locale.languageCode]!['apiUrlPlaceholder']!;
  String get modelPlaceholder => _localizedValues[locale.languageCode]!['modelPlaceholder']!;
  String get lookAndFeel => _localizedValues[locale.languageCode]!['lookAndFeel']!;
  String get lookAndFeelDescription => _localizedValues[locale.languageCode]!['lookAndFeelDescription']!;
  String get lookAndFeelTitle => _localizedValues[locale.languageCode]!['lookAndFeelTitle']!;
  String get lookAndFeelSubtitle => _localizedValues[locale.languageCode]!['lookAndFeelSubtitle']!;
  String get createdAt => _localizedValues[locale.languageCode]!['createdAt']!;
  String get favoritedNotes => _localizedValues[locale.languageCode]!['favoritedNotes']!;
  String get neverDelete => _localizedValues[locale.languageCode]!['neverDelete']!;
  String get noFavoritedNotes => _localizedValues[locale.languageCode]!['noFavoritedNotes']!;
  String get waitingForAIResponse => _localizedValues[locale.languageCode]!['waitingForAIResponse']!;
  String get copiedToClipboard => _localizedValues[locale.languageCode]!['copiedToClipboard']!;
  String get copy => _localizedValues[locale.languageCode]!['copy']!;
  String get retry => _localizedValues[locale.languageCode]!['retry']!;
  String get retryingAIResponse => _localizedValues[locale.languageCode]!['retryingAIResponse']!;
  String get aiResponseParsingFailed => _localizedValues[locale.languageCode]!['aiResponseParsingFailed']!;
  String get networkRequestFailed => _localizedValues[locale.languageCode]!['networkRequestFailed']!;
  String get networkError => _localizedValues[locale.languageCode]!['networkError']!;
  String get aiUpdateSuccess => _localizedValues[locale.languageCode]!['aiUpdateSuccess']!;
  String get configurationName => _localizedValues[locale.languageCode]!['configurationName']!;
  String get modelName => _localizedValues[locale.languageCode]!['modelName']!;
  String get newModelConfiguration => _localizedValues[locale.languageCode]!['newModelConfiguration']!;
  String get modelType => _localizedValues[locale.languageCode]!['modelType']!;
  String get modelTypePlaceholder => _localizedValues[locale.languageCode]!['modelTypePlaceholder']!;
  String get modelTypeDescription => _localizedValues[locale.languageCode]!['modelTypeDescription']!;
  String get verifyAndSave => _localizedValues[locale.languageCode]!['verifyAndSave']!;
  String get configurationNameHint => _localizedValues[locale.languageCode]!['configurationNameHint']!;
  String get apiKeyHint => _localizedValues[locale.languageCode]!['apiKeyHint']!;
  String get cannotFindLinkedNote => _localizedValues[locale.languageCode]!['cannotFindLinkedNote']!;
  String get firstRunSettings => _localizedValues[locale.languageCode]!['firstRunSettings']!;
  String get firstRunSettingsDescription => _localizedValues[locale.languageCode]!['firstRunSettingsDescription']!;
  String get waitingForAITitle => _localizedValues[locale.languageCode]!['waitingForAITitle']!;
  String get createdNewNote => _localizedValues[locale.languageCode]!['createdNewNote']!;
  String get createNoteFailed => _localizedValues[locale.languageCode]!['createNoteFailed']!;
  String get clipboardIsEmpty => _localizedValues[locale.languageCode]!['clipboardIsEmpty']!;
  String get waitingForAIGenerateTitleAndCategories =>
      _localizedValues[locale.languageCode]!['waitingForAIGenerateTitleAndCategories']!;
  String get createdNewNoteAndWaitingForAIResponse =>
      _localizedValues[locale.languageCode]!['createdNewNoteAndWaitingForAIResponse']!;
  String get ask => _localizedValues[locale.languageCode]!['ask']!;
  String get askPlaceholder => _localizedValues[locale.languageCode]!['askPlaceholder']!;
  String get noRelatedNotesFound => _localizedValues[locale.languageCode]!['noRelatedNotesFound']!;
  String notesCreatedText(int count) {
    String format = _localizedValues[locale.languageCode]!['notesCreatedFormat']!;
    return format.replaceAll('%d', count.toString());
  }

  String favoritedNotesText(int count) {
    String format = _localizedValues[locale.languageCode]!['favoritedNotesFormat']!;
    return format.replaceAll('%d', count.toString());
  }

  String contributionGraphTitle(String startDate) {
    String format = _localizedValues[locale.languageCode]!['contributionGraphTitle']!;
    return format.replaceAll('%s', startDate);
  }

  String get logout => _localizedValues[locale.languageCode]!['logout']!;
  String get login => _localizedValues[locale.languageCode]!['login']!;
  String get notLogin => _localizedValues[locale.languageCode]!['notLogin']!;
  String get clickAvatarToLogin => _localizedValues[locale.languageCode]!['clickAvatarToLogin']!;
  String get pleaseEnterValidEmail => _localizedValues[locale.languageCode]!['pleaseEnterValidEmail']!;
  String get pleaseEnterEmail => _localizedValues[locale.languageCode]!['pleaseEnterEmail']!;
  String get pleaseEnterPassword => _localizedValues[locale.languageCode]!['pleaseEnterPassword']!;
  String get passwordAtLeast6Characters => _localizedValues[locale.languageCode]!['passwordAtLeast6Characters']!;
  String get email => _localizedValues[locale.languageCode]!['email']!;
  String get password => _localizedValues[locale.languageCode]!['password']!;
  String get loginFailed => _localizedValues[locale.languageCode]!['loginFailed']!;
  String get loginFailedDescription => _localizedValues[locale.languageCode]!['loginFailedDescription']!;
  String get emailAutoRegister => _localizedValues[locale.languageCode]!['emailAutoRegister']!;
  String get loginExpired => _localizedValues[locale.languageCode]!['loginExpired']!;
  String get loginNotLogin => _localizedValues[locale.languageCode]!['loginNotLogin']!;
  String get alreadyLoggedIn => _localizedValues[locale.languageCode]!['alreadyLoggedIn']!;
  String get clickPlusButtonToInputText => _localizedValues[locale.languageCode]!['clickPlusButtonToInputText']!;
  String get longPressPlusButtonToPasteClipboardText =>
      _localizedValues[locale.languageCode]!['longPressPlusButtonToPasteClipboardText']!;
  String get timelineTitle => _localizedValues[locale.languageCode]!['timelineTitle']!;
  String get daysLaterDelete => _localizedValues[locale.languageCode]!['daysLaterDelete']!;
  String get hoursLaterDelete => _localizedValues[locale.languageCode]!['hoursLaterDelete']!;
  String get minutesLaterDelete => _localizedValues[locale.languageCode]!['minutesLaterDelete']!;
  String get what => _localizedValues[locale.languageCode]!['what']!;
  String get why => _localizedValues[locale.languageCode]!['why']!;
  String get howToDo => _localizedValues[locale.languageCode]!['howToDo']!;
  String get more => _localizedValues[locale.languageCode]!['more']!;
  String get debate => _localizedValues[locale.languageCode]!['debate']!;
  String get askByMe => _localizedValues[locale.languageCode]!['askByMe']!;
  String get example => _localizedValues[locale.languageCode]!['example']!;
  String get delete => _localizedValues[locale.languageCode]!['delete']!;
  String get clear => _localizedValues[locale.languageCode]!['clear']!;
  String get jsonParseError => _localizedValues[locale.languageCode]!['jsonParseError']!;
  String get remainingQuota => _localizedValues[locale.languageCode]!['remainingQuota']!;
  String get totalQuota => _localizedValues[locale.languageCode]!['totalQuota']!;
  String get usedQuota => _localizedValues[locale.languageCode]!['usedQuota']!;
  String get quotaLevel => _localizedValues[locale.languageCode]!['quotaLevel']!;
  String get times => _localizedValues[locale.languageCode]!['times']!;
  String get reGetNote => _localizedValues[locale.languageCode]!['reGetNote']!;
  String get reGetNoteConfirm => _localizedValues[locale.languageCode]!['reGetNoteConfirm']!;
  String get clearConfirmDescription => _localizedValues[locale.languageCode]!['clearConfirmDescription']!;
  String get deleteConfirm => _localizedValues[locale.languageCode]!['deleteConfirm']!;
  String get deleteConfirmDescription => _localizedValues[locale.languageCode]!['deleteConfirmDescription']!;
  String get openLinkedNote => _localizedValues[locale.languageCode]!['openLinkedNote']!;
  String get pleaseLoginFirst => _localizedValues[locale.languageCode]!['pleaseLoginFirst']!;

  String confirmDeleteSelectedNotes(int count) {
    String format = _localizedValues[locale.languageCode]!['confirmDeleteSelectedNotes']!;
    return format.replaceAll('%d', count.toString());
  }

  String get clearAllHighlights => _localizedValues[locale.languageCode]!['clearAllHighlights']!;
  String get share => _localizedValues[locale.languageCode]!['share']!;
  String get askAboutFullNote => _localizedValues[locale.languageCode]!['askAboutFullNote']!;
  String get askAboutFullNoteTitle => _localizedValues[locale.languageCode]!['askAboutFullNoteTitle']!;
  String get askAboutHighlightedText => _localizedValues[locale.languageCode]!['askAboutHighlightedText']!;
  String get askAboutHighlightedTextTitle => _localizedValues[locale.languageCode]!['askAboutHighlightedTextTitle']!;
  String get noteStillGenerating => _localizedValues[locale.languageCode]!['noteStillGenerating']!;
  String get canDeleteFavNote => _localizedValues[locale.languageCode]!['canDeleteFavNote']!;
  String get selectedNoteDeleted => _localizedValues[locale.languageCode]!['selectedNoteDeleted']!;
  String get deleteFailed => _localizedValues[locale.languageCode]!['deleteFailed']!;
  String get gettingData => _localizedValues[locale.languageCode]!['gettingData']!;
  String get quotaExceeded => _localizedValues[locale.languageCode]!['quotaExceeded']!;
  String get pending => _localizedValues[locale.languageCode]!['pending']!;

  String get otherError => _localizedValues[locale.languageCode]!['otherError']!;
  String get userCancel => _localizedValues[locale.languageCode]!['userCancel']!;

  String get generateTitleAndCategoriesPrompt =>
      _localizedValues[locale.languageCode]!['generateTitleAndCategoriesPrompt']!;

  String askPrompt(String question) {
    String format = _localizedValues[locale.languageCode]!['askPrompt']!;
    return format.replaceFirst('%s', question);
  }

  String whatPrompt(String article, String selectedText) {
    String format = _localizedValues[locale.languageCode]!['whatPrompt']!;
    return format.replaceFirst('%s', article).replaceFirst('%s', selectedText);
  }

  String whyPrompt(String article, String selectedText) {
    String format = _localizedValues[locale.languageCode]!['whyPrompt']!;
    return format.replaceFirst('%s', article).replaceFirst('%s', selectedText);
  }

  String morePrompt(String article, String selectedText) {
    String format = _localizedValues[locale.languageCode]!['morePrompt']!;
    return format.replaceFirst('%s', article).replaceFirst('%s', selectedText);
  }

  String howtoPrompt(String article, String selectedText) {
    String format = _localizedValues[locale.languageCode]!['howtoPrompt']!;
    return format.replaceFirst('%s', article).replaceFirst('%s', selectedText);
  }

  String debatePrompt(String article, String selectedText) {
    String format = _localizedValues[locale.languageCode]!['debatePrompt']!;
    return format.replaceFirst('%s', article).replaceFirst('%s', selectedText);
  }

  String examplePrompt(String article, String selectedText) {
    String format = _localizedValues[locale.languageCode]!['examplePrompt']!;
    return format.replaceFirst('%s', article).replaceFirst('%s', selectedText);
  }

  String askAboutHighlightedTextPrompt(String article, String selectedText, String thought) {
    String format = _localizedValues[locale.languageCode]!['askAboutHighlightedTextPrompt']!;
    return format.replaceFirst('%s', article).replaceFirst('%s', selectedText).replaceFirst('%s', thought);
  }

  String askAboutFullNotePrompt(String article, String thought) {
    String format = _localizedValues[locale.languageCode]!['askAboutFullNotePrompt']!;
    return format.replaceFirst('%s', article).replaceFirst('%s', thought);
  }

  String getQuizQuestion(String article) {
    String format = _localizedValues[locale.languageCode]!['getQuizQuestion']!;
    return format.replaceFirst('%s', article);
  }

  String get answerQuiz => _localizedValues[locale.languageCode]!['answerQuiz']!;
  String get generateQuizFailed => _localizedValues[locale.languageCode]!['generateQuizFailed']!;
  String get generateQuizSuccess => _localizedValues[locale.languageCode]!['generateQuizSuccess']!;
  String get generateQuizDescription => _localizedValues[locale.languageCode]!['generateQuizDescription']!;
  String get quizTitleDescription => _localizedValues[locale.languageCode]!['quizTitleDescription']!;
  String get quizAllCorrect => _localizedValues[locale.languageCode]!['quizAllCorrect']!;
  String get quizNotAllCorrect => _localizedValues[locale.languageCode]!['quizNotAllCorrect']!;
  String get quizQuestion => _localizedValues[locale.languageCode]!['quizQuestion']!;
  String get submitAnswer => _localizedValues[locale.languageCode]!['submitAnswer']!;
  String get answerCorrect => _localizedValues[locale.languageCode]!['answerCorrect']!;
  String get answerIncorrect => _localizedValues[locale.languageCode]!['answerIncorrect']!;
  String get answerCorrectDescription => _localizedValues[locale.languageCode]!['answerCorrectDescription']!;
  String get addQuotaFailed => _localizedValues[locale.languageCode]!['addQuotaFailed']!;
  String get addQuotaSuccess => _localizedValues[locale.languageCode]!['addQuotaSuccess']!;
  String get quizResult => _localizedValues[locale.languageCode]!['quizResult']!;
  String get quizResultDescription => _localizedValues[locale.languageCode]!['quizResultDescription']!;
  String get addNoteToFavorites => _localizedValues[locale.languageCode]!['addNoteToFavorites']!;
  String get returnText => _localizedValues[locale.languageCode]!['return']!;

  String get loginAgreementPrefix => _localizedValues[locale.languageCode]!['loginAgreementPrefix']!;
  String get termsOfService => _localizedValues[locale.languageCode]!['termsOfService']!;
  String get and => _localizedValues[locale.languageCode]!['and']!;
  String get privacyPolicy => _localizedValues[locale.languageCode]!['privacyPolicy']!;
  String get aboutTempognize => _localizedValues[locale.languageCode]!['aboutTempognize']!;
  String get unknownError => _localizedValues[locale.languageCode]!['unknownError']!;

  String getInterestingKnowledge(String category, String noteTitles) {
    String format = _localizedValues[locale.languageCode]!['getInterestingKnowledge']!;
    return format.replaceFirst('%s', category).replaceFirst('%s', noteTitles);
  }

  String get getDifferentKnowledge => _localizedValues[locale.languageCode]!['getDifferentKnowledge']!;
  String get pleaseAgreeToTerms => _localizedValues[locale.languageCode]!['pleaseAgreeToTerms']!;

  String get addButtonHintTitle => _localizedValues[locale.languageCode]!['addButtonHintTitle']!;
  String get addButtonHintContent => _localizedValues[locale.languageCode]!['addButtonHintContent']!;

  String get resetFeatureHints => _localizedValues[locale.languageCode]!['resetFeatureHints']!;
  String get resetFeatureHintsDescription => _localizedValues[locale.languageCode]!['resetFeatureHintsDescription']!;
  String get resetFeatureHintsConfirm => _localizedValues[locale.languageCode]!['resetFeatureHintsConfirm']!;
  String get resetFeatureHintsSuccess => _localizedValues[locale.languageCode]!['resetFeatureHintsSuccess']!;

  String get scrollbarHintTitle => _localizedValues[locale.languageCode]!['scrollbarHintTitle']!;
  String get scrollbarHintContent => _localizedValues[locale.languageCode]!['scrollbarHintContent']!;
  String get textSelectionHintTitle => _localizedValues[locale.languageCode]!['textSelectionHintTitle']!;
  String get textSelectionHintContent => _localizedValues[locale.languageCode]!['textSelectionHintContent']!;
  String get doNotRemind => _localizedValues[locale.languageCode]!['doNotRemind']!;
  String get iKnow => _localizedValues[locale.languageCode]!['iKnow']!;

  String get scrollbarOnRight => _localizedValues[locale.languageCode]!['scrollbarOnRight']!;
  String get scrollbarOnLeft => _localizedValues[locale.languageCode]!['scrollbarOnLeft']!;

  // 在类中添加新的getter
  String get questionButtonHintTitle => _localizedValues[locale.languageCode]!['questionButtonHintTitle']!;

  String get myQuizzes => _localizedValues[locale.languageCode]!['myQuizzes']!;
  String get quiz => _localizedValues[locale.languageCode]!['quiz']!;
  String get tapToStartQuiz => _localizedValues[locale.languageCode]!['tapToStartQuiz']!;
  String get review => _localizedValues[locale.languageCode]!['review']!;
  String get reviewWithAward => _localizedValues[locale.languageCode]!['reviewWithAward']!;
  String get noQuizzes => _localizedValues[locale.languageCode]!['noQuizzes']!;
  String get generateQuiz => _localizedValues[locale.languageCode]!['generateQuiz']!;

  String get deleteAccount => _localizedValues[locale.languageCode]!['deleteAccount']!;
  String get deleteAccountConfirm => _localizedValues[locale.languageCode]!['deleteAccountConfirm']!;
  String get deleteAccountDescription => _localizedValues[locale.languageCode]!['deleteAccountDescription']!;
  String get deleteAccountSuccess => _localizedValues[locale.languageCode]!['deleteAccountSuccess']!;
  String get deleteAccountFailed => _localizedValues[locale.languageCode]!['deleteAccountFailed']!;

  String get selectImageSource => _localizedValues[locale.languageCode]!['selectImageSource']!;
  String get selectFromGallery => _localizedValues[locale.languageCode]!['selectFromGallery']!;
  String get takePhoto => _localizedValues[locale.languageCode]!['takePhoto']!;
  String get editImage => _localizedValues[locale.languageCode]!['editImage']!;

  String get imageSavedSuccessfully => _localizedValues[locale.languageCode]!['imageSavedSuccessfully']!;
  String get failedToSaveImage => _localizedValues[locale.languageCode]!['failedToSaveImage']!;
  String get transcribeAudio => _localizedValues[locale.languageCode]!['transcribeAudio']!;

  String get recording => _localizedValues[locale.languageCode]!['recording']!;
  String get recordingFailed => _localizedValues[locale.languageCode]!['recordingFailed']!;
  String get recordingNotFound => _localizedValues[locale.languageCode]!['recordingNotFound']!;
  String get transcribing => _localizedValues[locale.languageCode]!['transcribing']!;
  String get transcribeFailed => _localizedValues[locale.languageCode]!['transcribeFailed']!;
  String get longPressToRecord => _localizedValues[locale.languageCode]!['longPressToRecord']!;
  String get voiceInput => _localizedValues[locale.languageCode]!['voiceInput']!;
  String get recordingTooShort => _localizedValues[locale.languageCode]!['recordingTooShort']!;
  String get recordingTooQuiet => _localizedValues[locale.languageCode]!['recordingTooQuiet']!;
  String get noRecordingPermission => _localizedValues[locale.languageCode]!['noRecordingPermission']!;
  String get transcribeCancelled => _localizedValues[locale.languageCode]!['transcribeCancelled']!;
  String get cancelTranscribing => _localizedValues[locale.languageCode]!['cancelTranscribing']!;
  String get recordingCancelled => _localizedValues[locale.languageCode]!['recordingCancelled']!;
  String get stopRecording => _localizedValues[locale.languageCode]!['stopRecording']!;
  String get pasteFromClipboard => _localizedValues[locale.languageCode]!['pasteFromClipboard']!;
  String get pasteFromClipboardQuestion => _localizedValues[locale.languageCode]!['pasteFromClipboardQuestion']!;
  String get releaseToStop => _localizedValues[locale.languageCode]!['releaseToStop']!;
  String get releaseToCancel => _localizedValues[locale.languageCode]!['releaseToCancel']!;
  String get clearTitleImageCache => _localizedValues[locale.languageCode]!['clearTitleImageCache']!;
  String get clearTitleImageCacheDescription =>
      _localizedValues[locale.languageCode]!['clearTitleImageCacheDescription']!;
  String get clearTitleImageCacheConfirm => _localizedValues[locale.languageCode]!['clearTitleImageCacheConfirm']!;
  String get clearTitleImageCacheSuccess => _localizedValues[locale.languageCode]!['clearTitleImageCacheSuccess']!;

  String get switchToGridView => _localizedValues[locale.languageCode]!['switchToGridView']!;
  String get switchToListView => _localizedValues[locale.languageCode]!['switchToListView']!;

  String get justNow => _localizedValues[locale.languageCode]!['justNow']!;

  String minutesAgo(int minutes) {
    String format = _localizedValues[locale.languageCode]!['minutesAgo']!;
    return format.replaceAll('%d', minutes.toString());
  }

  String hoursAgo(int hours) {
    String format = _localizedValues[locale.languageCode]!['hoursAgo']!;
    return format.replaceAll('%d', hours.toString());
  }

  String daysAgo(int days) {
    String format = _localizedValues[locale.languageCode]!['daysAgo']!;
    return format.replaceAll('%d', days.toString());
  }

  String get translate => _localizedValues[locale.languageCode]!['translate']!;
  String get originalText => _localizedValues[locale.languageCode]!['originalText']!;
  String get translatedText => _localizedValues[locale.languageCode]!['translatedText']!;
  String get alternativeTranslations => _localizedValues[locale.languageCode]!['alternativeTranslations']!;
  String get selectLanguage => _localizedValues[locale.languageCode]!['selectLanguage']!;

  String get newVersionTitle => _localizedValues[locale.languageCode]!['newVersionTitle']!;
  String get forceUpdateMessage => _localizedValues[locale.languageCode]!['forceUpdateMessage']!;
  String get updateNow => _localizedValues[locale.languageCode]!['updateNow']!;

  String get about => _localizedValues[locale.languageCode]!['about']!;
  String get version => _localizedValues[locale.languageCode]!['version']!;
  String get message => _localizedValues[locale.languageCode]!['message']!;
  String get recommendKnowledge => _localizedValues[locale.languageCode]!['recommendKnowledge']!;
  String get noteCategory => _localizedValues[locale.languageCode]!['noteCategory']!;
  String get proModeDescription => _localizedValues[locale.languageCode]!['proModeDescription']!;

  String waitingForAIResponseLast(String seconds) {
    String format = _localizedValues[locale.languageCode]!['waitingForAIResponseLast']!;
    return format.replaceAll('%s', seconds);
  }

  String get trainingDataGenerationSuccess => _localizedValues[locale.languageCode]!['trainingDataGenerationSuccess']!;
  String get trainingDataGenerationFailed => _localizedValues[locale.languageCode]!['trainingDataGenerationFailed']!;
  String get databaseLoadFailedGenerateNew => _localizedValues[locale.languageCode]!['databaseLoadFailedGenerateNew']!;
  String get previousTrainingProgressLoaded =>
      _localizedValues[locale.languageCode]!['previousTrainingProgressLoaded']!;
  String get failedToLoadTrainingData => _localizedValues[locale.languageCode]!['failedToLoadTrainingData']!;
  String get saveFailed => _localizedValues[locale.languageCode]!['saveFailed']!;
  String get willContinueWithoutSaving => _localizedValues[locale.languageCode]!['willContinueWithoutSaving']!;
  String get playbackFailed => _localizedValues[locale.languageCode]!['playbackFailed']!;
  String get microphonePermissionRequired => _localizedValues[locale.languageCode]!['microphonePermissionRequired']!;
  String get speechRecognitionFailed => _localizedValues[locale.languageCode]!['speechRecognitionFailed']!;
  String get voiceTranscriptionFailed => _localizedValues[locale.languageCode]!['voiceTranscriptionFailed']!;
  String get evaluationFailed => _localizedValues[locale.languageCode]!['evaluationFailed']!;
  String get loadFailed => _localizedValues[locale.languageCode]!['loadFailed']!;
  String get overallAssessmentFailed => _localizedValues[locale.languageCode]!['overallAssessmentFailed']!;
  String get regenerateTrainingContentFailed =>
      _localizedValues[locale.languageCode]!['regenerateTrainingContentFailed']!;
  String get playbackControlFailed => _localizedValues[locale.languageCode]!['playbackControlFailed']!;
  String get initializeDefaultNotes => _localizedValues[locale.languageCode]!['initializeDefaultNotes']!;
  String get initializeDefaultNotesDescription =>
      _localizedValues[locale.languageCode]!['initializeDefaultNotesDescription']!;
  String get initializeDefaultNotesConfirm => _localizedValues[locale.languageCode]!['initializeDefaultNotesConfirm']!;
  String get initializeDefaultNotesConfirmDescription =>
      _localizedValues[locale.languageCode]!['initializeDefaultNotesConfirmDescription']!;
  String get initializeDefaultNotesSuccess => _localizedValues[locale.languageCode]!['initializeDefaultNotesSuccess']!;
  String get unsupportedQuestion => _localizedValues[locale.languageCode]!['unsupportedQuestion']!;
  String get translationTraining => _localizedValues[locale.languageCode]!['translationTraining']!;
  String get ttsTest => _localizedValues[locale.languageCode]!['ttsTest']!;
  String get enterTextToSpeak => _localizedValues[locale.languageCode]!['enterTextToSpeak']!;
  String get speak => _localizedValues[locale.languageCode]!['speak']!;
  String get stopping => _localizedValues[locale.languageCode]!['stopping']!;

  String get notifications => _localizedValues[locale.languageCode]!['notifications']!;
  String get noNotifications => _localizedValues[locale.languageCode]!['noNotifications']!;
  String get failedToLoadNotifications => _localizedValues[locale.languageCode]!['failedToLoadNotifications']!;
  String get tryAgain => _localizedValues[locale.languageCode]!['tryAgain']!;
  String get goToSettingsForAllNotifications =>
      _localizedValues[locale.languageCode]!['goToSettingsForAllNotifications']!;
  String get close => _localizedValues[locale.languageCode]!['close']!;
  String get seeAll => _localizedValues[locale.languageCode]!['seeAll']!;
  String get submit => _localizedValues[locale.languageCode]!['submit']!;
  String get preItem => _localizedValues[locale.languageCode]!['preItem']!;
  String get nextItem => _localizedValues[locale.languageCode]!['nextItem']!;
  String get nextSentence => _localizedValues[locale.languageCode]!['nextSentence']!;
  String get blackboard => _localizedValues[locale.languageCode]!['blackboard']!;
  String get keyWords => _localizedValues[locale.languageCode]!['keyWords']!;
  String get teacherExplanation => _localizedValues[locale.languageCode]!['teacherExplanation']!;
  String get yourUnderstanding => _localizedValues[locale.languageCode]!['yourUnderstanding']!;
  String get watchSummary => _localizedValues[locale.languageCode]!['watchSummary']!;
  String get knowledgeItem => _localizedValues[locale.languageCode]!['knowledgeItem']!;
  String get sayYourUnderstanding => _localizedValues[locale.languageCode]!['sayYourUnderstanding']!;
  String get tryMore => _localizedValues[locale.languageCode]!['tryMore']!;
  String get inputByVoice => _localizedValues[locale.languageCode]!['inputByVoice']!;
  String get stop => _localizedValues[locale.languageCode]!['stop']!;
  String get adjustPlaybackSpeed => _localizedValues[locale.languageCode]!['adjustPlaybackSpeed']!;
  String get hideText => _localizedValues[locale.languageCode]!['hideText']!;
  String get showText => _localizedValues[locale.languageCode]!['showText']!;
  String get regenerateAudio => _localizedValues[locale.languageCode]!['regenerateAudio']!;
  String get continuePlay => _localizedValues[locale.languageCode]!['continuePlay']!;
  String get ebbinghausHintTitle => _localizedValues[locale.languageCode]!['ebbinghausHintTitle']!;
  String get ebbinghausHintContent => _localizedValues[locale.languageCode]!['ebbinghausHintContent']!;
  String get gotIt => _localizedValues[locale.languageCode]!['gotIt']!;
  String get parentNote => _localizedValues[locale.languageCode]!['parentNote']!;
  String get childNotes => _localizedValues[locale.languageCode]!['childNotes']!;
  String get noChildNotes => _localizedValues[locale.languageCode]!['noChildNotes']!;
  String get noteHierarchy => _localizedValues[locale.languageCode]!['noteHierarchy']!;
  String get today => _localizedValues[locale.languageCode]!['today']!;
  String get yesterday => _localizedValues[locale.languageCode]!['yesterday']!;
  String get faqAlreadyExists => _localizedValues[locale.languageCode]!['faqAlreadyExists']!;

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => ['en', 'zh'].contains(locale.languageCode);

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(AppLocalizations(locale));
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
