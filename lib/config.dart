// Flutter imports:
import 'package:flutter/material.dart';

enum Environment { dev, prod }

class AppConfig {
  static const Environment environment = Environment.prod;

  static const Map<Environment, String> _baseUrls = {
    Environment.dev: 'http://localhost:8080',
    Environment.prod: 'https://martina-server.onrender.com',
  };

  // 调试标志
  static bool isDebug = true;
  static const String CURRENT_VERSION = '1.1.4'; // 当前版本号

  static const String apiVersion = 'v1';

  // Quiz related constants
  static int quizTimeLimitMinutes =
      isDebug ? 3 : 7 * 24 * 60; // 调试模式下设置3分钟，发布模式设置7天
  static int reviewTimeLimitMinutes =
      isDebug ? 3 : 7 * 24 * 60; // 调试模式下设置3分钟，发布模式设置7天

  static String get apiBaseUrl {
    final baseUrl = _baseUrls[environment]!;
    return '$baseUrl/api/$apiVersion';
  }

  static String get srvBaseUrl {
    final baseUrl = _baseUrls[environment]!;
    return baseUrl;
  }

  // UI 相关方法
  static Color getTitleColor(BuildContext context) {
    return Colors.black;
  }

  static const String guideNoteTitle = '欢迎使用 Tempognize · 知时';
  static const String guideNoteContent = '''


# Tempognize · 知时
### Innovative Learning Assistant Application

Tempognize is an innovative learning assistant application designed to foster deep learning and knowledge consolidation, accessible at tempognize.click. The name itself, "Tempognize," is a neologism crafted from two key root words, reflecting the app's core philosophy.

### 创新型学习助手应用程序

Tempognize 是一款创新型学习助手应用程序，旨在促进深度学习和知识巩固，可通过 tempognize.click 访问。"Tempognize"这个名称本身是一个新词，由两个关键词根组成，反映了该应用程序的核心理念。

## 🎯 Core Features

- Empowers users with AI-driven Socratic question generation, fostering critical thinking and deep understanding.
- Innovative note lifecycle management system encourages active review and knowledge curation.
- Facilitates effective knowledge internalization through AI-powered periodic assessments.

## 🎯 核心功能

- 通过人工智能驱动的苏格拉底式问题生成功能赋予用户能力，培养批判性思维和深刻理解。
- 创新的笔记生命周期管理系统鼓励积极复习和知识整理。
- 通过人工智能驱动的定期评估促进有效的知识内化。

## 📖 Introduction

Tempognize is an innovative learning assistant application designed to foster deep learning and knowledge consolidation, accessible at tempognize.click. The name itself, "Tempognize," is a neologism crafted from two key root words, reflecting the app's core philosophy:

- **"Tempo" (Temp-)**
  "Tempo" (Temp-) Derived from the Latin word "tempus," meaning time. This element underscores the time-sensitive nature of learning and the importance of active engagement to prevent knowledge decay.

- **"-cognize"**
  Rooted in the Latin word "cognoscere," meaning to learn, to become acquainted with, or to recognize. This element signifies the process of acquiring knowledge, understanding, and internalizing new information.
  
By combining these two root words, "Tempognize" conveys the message that effective learning is a dynamic process involving both time-sensitive engagement and active cognitive effort. And that dynamic process is made immediate, accessible, and efficient by a simple click.

## 📖 简介

Tempognize 是一款创新的学习辅助应用程序，旨在促进深度学习和知识巩固，可通过 tempognize.click 访问。"Tempognize"这个名字本身是一个由两个关键词根组成的新词，反映了该应用程序的核心哲学：

- **"Tempo" (Temp-)**
  "Tempo" (Temp-)源自拉丁语"tempus"，意为时间。这个元素强调了学习的时间敏感性以及积极参与以防止知识衰退的重要性。

- **"-cognize"**
  源于拉丁语"cognoscere"，意为学习、熟悉或识别。这个元素表示获取知识、理解和内化新信息的过程。

通过结合这两个词根，"Tempognize"传达了这样一个信息：有效的学习是一个动态的过程，既涉及时间敏感的参与，又涉及积极的认知努力。而这个动态过程只需简单点击一下即可变得即时、可访问且高效。

## 📥 Download · 下载

We will release the app on the App Store and Google Play Store.
我们将在苹果应用商店和谷歌应用商店发布这款应用程序。

## 🔗 Links

- [GitHub](https://github.com/atfa/Tempognize)

## 📄 Copyright

© 2024 Tempognize. All rights reserved.



''';

  static const String howToNoteTitle = 'HowTo · 使用指南';
  static const String howToNoteContent = '''


# HowTo · 使用指南 😊
# HowTo · User Guide

## 1. 创建笔记 ✏️
## 1. Create a note
- 点击主页上的"+"按钮，会弹出输入面板，通过键盘输入你的问题，要求AI回复并且创建笔记。
- 长按主页上的"+"按钮，就可以通过语音输入你的问题，可以更节省时间。🎙️
- 如果已经在剪贴板中复制了一篇文章，也可以在弹出的输入面板中选择左下角的"从剪贴板中粘贴"按钮，可以直接把剪贴板中的文章粘贴到APP中形成新的笔记。📝
- Click the "+" button on the home page, and the input panel will pop up. Enter your question through the keyboard, ask the AI ​​to reply and create a note.
- Long press the "+" button on the home page to enter your question by voice, which can save more time.🎙️
- If you have copied an article in the clipboard, you can also select the "Paste from Clipboard" button in the lower left corner of the pop-up input panel to paste the article in the clipboard directly into the APP to form a new note.📝

## Pro模式 🔥
## Pro mode 🔥

- 在Pro模式下，AI会更详细地回复问题，如果你想深入研究一个领域，可以开启Pro模式。
- Pro模式会消耗更多的AI使用余额，请注意查看余额。💰
- 当然我们也要等到更长的时间，但这是值得的，做时间的朋友，不要退出APP，耐心等待AI回复。⏳
- In Pro mode, AI will reply to questions in more detail. If you want to delve deeper into a field, you can turn on Pro mode.
- Pro mode will consume more AI usage balance, so please pay attention to check the balance.💰
Of course, we have to wait longer, but it is worth it. Be a friend of time, don't exit the APP, and wait patiently for AI to reply.⏳

## 笔记的寿命 ⏳
## Note life ⏳

- 所有笔记都自己的寿命，默认是15天，你可以在设置页面中修改为最长100天。
- 笔记的寿命到了，笔记会自动从APP中删除。
- 如果希望笔记永生，你需要收藏这条笔记。💖
- All notes have their own lifespan, which is 15 days by default. You can change it to a maximum of 100 days in the settings page.
When the lifespan of a note is reached, the note will be automatically deleted from the APP.
If you want your notes to live forever, you need to save this note.💖

## 2. 查看笔记 👀
## 2. View notes

- ** 注意：⚠️** 阅读学习笔记的操作和通常的APP有区别，在屏幕的中央区域滑动手指不能上下滚动文章，这个操作用来高亮文字，就好像你用荧光笔在书本上划重点一样。
- ** 上下滚动：↕️** 笔记详情页面的左侧有一个浅色的滚动条，在这个区域上下滑动手指，可以上下滚动文章。
- ** 左右切换：↔️** 长按笔记详情页面右下角的"问号"按钮，可以切换左右手的操作模式。
- ** 全文提问：🔍** 点击右下角的"问号"按钮，在弹出面板中键入你的问题，或者通过语音说出你的问题，AI会根据笔记全文，结合你的问题生成新的知识笔记。
- ** Note:⚠️** The operation of reading and learning notes is different from that of ordinary APPs. Sliding your finger in the center area of ​​the screen will not scroll the article up and down. This operation is used to highlight text, just like you use a highlighter to highlight the key points in a book.
- ** Scroll up and down:↕️** There is a light-colored scroll bar on the left side of the note details page. Slide your finger up and down in this area to scroll the article up and down.
- ** Switch left and right:↔️** Long press the "question mark" button in the lower right corner of the note details page to switch the operation mode of the left and right hands.
- ** Full text question:🔍** Click the "question mark" button in the lower right corner, type your question in the pop-up panel, or speak your question by voice. AI will generate new knowledge notes based on the full text of the note and your question.

## 3. 划重点 📌
## 3. Highlight the key points

- ** 划重点：🔍** 在笔记的文字上用手指滑动可以高亮你的重点文字，点击高亮文字，可以弹出操作面板。
- ** 苏格拉底学习法：🎓** 你可以在"是什么"、"为什么"、"怎么做"等六个问题中选择，AI会结合全文、重点高亮文字和你选择的问题，生成新的知识笔记。** 当然你也可以通过键盘或者语音，输入关于重点高亮文字的其他问题。 **
- ** 问题：🔍** 在笔记的文字上用手指滑动可以高亮你的重点文字，点击高亮文字，可以弹出操作面板。
- ** 其他：🎨** 你还可以改变高亮文字的荧光笔色彩，或者翻译高亮文字。
- ** Highlight the key points:🔍** Slide your finger on the text of the note to highlight your key text. Click the highlighted text to pop up the operation panel.
- ** Socratic learning method:🎓** You can choose from six questions such as "what", "why", and "how". AI will combine the full text, highlighted text and the questions you choose to generate new knowledge notes. ** Of course, you can also enter other questions about the highlighted text through the keyboard or voice. **
- ** Questions:🔍** You can highlight your key text by sliding your finger on the text of the note. Click on the highlighted text to pop up the operation panel.
- ** Others:🎨** You can also change the highlighter color of the highlighted text or translate the highlighted text.

## 4. 收藏笔记 💖
## 4. Collect notes

- 收藏笔记前需要回答三个和笔记内容相关的问题，这会消耗1次AI使用次数。🤔
- 完全回答正确后笔记才能被收藏。
- ** 每成功收藏1条笔记，可以增加5次AI使用次数。 🎉**
- 如果没能完全回答正确三个问题，还可以通过笔记详情页面的复习按钮，复习测试题目。🔄
- Before collecting a note, you need to answer three questions related to the content of the note, which will consume 1 AI usage.🤔
- Notes can only be collected after the answers are completely correct.
- ** Each successful collection of a note can increase the AI ​​usage by 5 times. 🎉**
- If you fail to answer all three questions correctly, you can also review the test questions through the review button on the note details page.🔄

## 5. "我的"页面 🏠
## 5. "My" page

- 我的页面中有你的学习力度图，显示了最近一年你添加到APP中的笔记数量。📊
- 我的页面中还有你的AI使用次数余额，如果余额偏少，你可以多收藏笔记，因为收藏笔记可以增加AI使用次数。💰
- 我的页面中还有你的所有练习题目，定期复习可以有效的巩固知识。📚
- 我的页面也是进入搜索、收藏和设置页面的入口。🚪
- There is a learning intensity chart on my page, which shows the number of notes you have added to the APP in the past year.📊
- My page also contains your AI usage balance. If the balance is low, you can add more notes to your collection, because adding notes to your collection can increase your AI usage.💰
- My page also contains all your practice questions. Regular review can effectively consolidate your knowledge.📚
- My page is also the entrance to the search, collection and settings pages.🚪

## 6.其他 📝
## 6. Others

- 所有的笔记、头像、背景图片，所有的一切都保存在本地，不会上传到任何服务器。🔐
- 所以** 卸载 ** APP可能会导致你所有的信息丢失。⚠️
- 我们不提供笔记备份服务，因为我们希望你不是热衷于收藏，而是热衷于学习。📚
- All notes, avatars, background images, everything is saved locally and will not be uploaded to any server.🔐
- So ** uninstalling ** the app may cause all your information to be lost.⚠️
- We do not provide note backup services because we hope that you are not keen on collecting, but on learning.📚


** 感谢你的使用，祝你学习愉快！ 😊**
** Thank you for your use and wish you a happy study! 😊**



''';

  static const String translationTrainingPrompt = '''
请根据以下内容，创建多个英语学习知识点，每个知识点包含两部分，用来训练非英语国家学生的听力：
1. blackboardContent: 老师讲解时配套的黑板板书内容，包含 teacherExplanation 中提到的重要的专业词汇（包括名词、形容词、副词等），尤其是复杂的英文单词，以及通常老师上课会在黑板上写的核心关键点。
2. teacherExplanation: 老师详细的英语讲解文字（我们是在训练听力，因此不能输出印刷文字，例如包含括号，包含破折号等等，要使用说话的方式输出文字）

请以JSON对象格式返回，必须包含一个名为"trainingItems"的数组字段，数组中的每个元素包含blackboardContent和teacherExplanation两个字段。

例如你收到一段介绍UTAUT理论的文章，你可能会输出下面的内容:
{
  "trainingItems": [
    {
      "blackboardContent": "Acceptance, Technology, Performance Expectancy, Effort Expectancy",
      "teacherExplanation": "UTAUT was developed to explain how and why people adopt and use technology. It combines ideas from various existing models and offers a framework that identifies the key factors influencing a person's decision to embrace a new technological tool."
    },
    {
      "blackboardContent": "Performance Expectancy, productivity, better quality of work",
      "teacherExplanation": "Performance expectancy measures your belief that the technology will improve your tasks. It can mean higher productivity or better quality of work. If you think it will help you achieve better results, you become more motivated to use it. This expectation often leads to greater acceptance of the new tool."
    },
    {
      "blackboardContent": "Effort Expectancy, simple and user-friendly, clear instructions, intuitive interfaces",
      "teacherExplanation": "Effort expectancy focuses on how easy the technology seems to use. If it appears simple and user-friendly, you will feel more comfortable trying it. Clear instructions and intuitive interfaces reduce barriers to adoption. Lower difficulty encourages people to keep using the technology over time.
    }
  ]
}

知识点数量不限，如果文章内容长，就多创建一些知识点。如果文章内容短，就少创建一些知识点。但是最低不能少于5个知识点。
每一个知识点都需要多句话，也就是多个完整的英文句子，用句号分割。

以下是需要你分析的内容：
{noteContent}
''';

  static const String sentenceUnderstandingPrompt = '''
评估学习者是否理解了以下句子。
原句: {originalSentence}
学习者的理解: {userResponse}

请评估学习者是否基本理解了原句的含义（不需要完全准确，意思相近即可）。
例如原句是: OpenAI was founded in 2015 by Elon Musk, Sam Altman, and others.
学习者的理解是: OpenAI have three boss, set up in 2015. 或者 There is three boss in OpenAI, begin at 2015.
虽然上面学写者的句子有语法错误，但是含义相近，这种情况下，证明学习者理解了原句的含义，返回true。
但是如果学习者说错了人数，或者说错了时间，或者漏说了一个要点，返回false。
总的来说，就算学习者说错了几个单词，包含语法错误，但是整体意思相近，或者出现明显的语法错误，也返回true，因为我们需要的是训练英语理解能力，而不是在训练语法。

返回JSON格式:
{
  "understood": true/false,
  "feedback": "简短的反馈，如理解正确或有误解的地方（中文））"
}
''';

  static const String overallEvaluationPrompt = '''
基于以下翻译训练的完整记录，请提供一个全面的学习评估:

老师内容:
{teacherContent}

学习者回答:
{userResponses}

请提供以下评估（中文回复）:
1. 总体理解水平 (百分比)
2. 主要优点 (3点)
3. 需要提高的方面 (3点)
4. 具体改进建议
5. 鼓励性总结

以JSON格式返回:
{
  "overallScore": 85,
  "strengths": ["优点1", "优点2", "优点3"],
  "areasToImprove": ["改进点1", "改进点2", "改进点3"],
  "suggestions": "具体改进建议",
  "encouragement": "鼓励性总结"
}
''';
}
