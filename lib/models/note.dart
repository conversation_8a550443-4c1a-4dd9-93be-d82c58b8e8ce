// Dart imports:
// ignore_for_file: constant_identifier_names

import 'dart:convert';
import 'dart:developer' as developer;

// Project imports:
import 'segment.dart';
import '../config.dart';
import '../utils/markdown_util.dart';

class Note {
  final int? id;
  final String title;
  final List<StyledTextSegment> content;
  final List<StyledTextSegment> originalContent;
  final String category;
  final String subCategory;
  final String subSubCategory;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isDeleted;
  final bool neverDelete;
  final int? parentNoteId;
  final String prompt;
  final int status; // 状态:0:正常,-1:解析json失败,-2:网络请求失败,-3:网络异常,-4:用户取消,-5:尚未开始获取数据
  final int retryCount;
  final String errorMessage;
  final String source; // 来源,1:剪贴板,2:输入问题,3:高亮文字AI生成
  final bool isProMode; // 新增：是否是pro模式生成
  final List<String> recommendations; // 新增：推荐的知识点列表
  final String reasoning; // 新增：模型的推理过程
  final DateTime? lastCorrectTime; // 新增：最近全对时间
  final String type; // 新增：笔记类型，用于标记FAQ笔记
  bool isUpdating = false;

  // status 和 isUpdating 的匹配关系:
  // isUpdating 为 true 时，不论 status 为多少，界面显示动画，note card 的标题栏显示"正在获取数据"
  // isUpdating 为 false 时，不显示动画

  // 状态码常量
  static const int STATUS_SUCCESS = 0; // 成功
  static const int STATUS_JSON_ERROR = -1; // JSON解析失败
  static const int STATUS_HTTP_ERROR = -2; // HTTP请求失败
  static const int STATUS_NETWORK_ERROR = -3; // 网络异常
  static const int STATUS_CANCELLED = -4; // 用户取消
  static const int STATUS_PENDING = -5; // 尚未开始
  static const int STATUS_NOT_LOGIN = -6; // 未登录
  static const int STATUS_QUOTA_EXCEEDED = -7; // 配额不足
  static const int STATUS_SERVICE_UNAVAILABLE = -8; // 服务不可用
  static const int STATUS_CONNECT_TIMEOUT = -9; // 连接超时
  static const int STATUS_RECEIVE_TIMEOUT = -10; // 接收超时
  static const int STATUS_UNSUPPORTED_QUESTION = -11; // 不支持的问题

  // 来源常量
  static const String SOURCE_CLIPBOARD = "1";
  static const String SOURCE_USER_ENTER = "2";
  static const String SOURCE_HIGHTED_TEXT = "3";

  // 笔记类型常量
  static const String TYPE_NORMAL = "normal";
  static const String TYPE_FAQ = "faq";

  Note({
    this.id,
    required this.title,
    required this.content,
    required this.originalContent,
    required this.createdAt,
    required this.updatedAt,
    required this.prompt,
    this.isDeleted = false,
    this.neverDelete = false,
    this.parentNoteId,
    this.category = '',
    this.subCategory = '',
    this.subSubCategory = '',
    this.status = 0,
    this.retryCount = 0,
    this.errorMessage = '',
    this.source = '',
    this.isProMode = false, // 新增：默认为false
    this.recommendations = const [], // 新增：默认为空列表
    this.reasoning = '', // 新增：默认为空字符串
    this.lastCorrectTime, // 新增：最近全对时间
    this.type = TYPE_NORMAL, // 新增：默认为普通笔记
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'content': jsonEncode(content.map((e) => e.toMap()).toList()),
      'original_content': jsonEncode(originalContent.map((e) => e.toMap()).toList()),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
      'never_delete': neverDelete ? 1 : 0,
      'parent_note_id': parentNoteId,
      'category': category,
      'sub_category': subCategory,
      'sub_sub_category': subSubCategory,
      'prompt': prompt,
      'status': status,
      'retry_count': retryCount,
      'error_message': errorMessage,
      'source': source,
      'is_pro_mode': isProMode ? 1 : 0, // 新增：转换为整数存储
      'recommendations': jsonEncode(recommendations), // 新增：将列表转换为JSON字符串
      'reasoning': reasoning, // 新增：存储推理过程
      'last_correct_time': lastCorrectTime?.toIso8601String(), // 新增：存储最近全对时间
      'type': type, // 新增：存储笔记类型
    };
  }

  factory Note.fromMap(Map<String, dynamic> map) {
    return Note(
      id: map['id'],
      title: map['title'],
      content: _parseContent(map['content']),
      originalContent: _parseContent(map['original_content']),
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
      isDeleted: map['is_deleted'] == 1,
      neverDelete: map['never_delete'] == 1,
      parentNoteId: map['parent_note_id'],
      category: map['category'],
      subCategory: map['sub_category'],
      subSubCategory: map['sub_sub_category'],
      prompt: map['prompt'] ?? '',
      status: map['status'] ?? 0,
      retryCount: map['retry_count'] ?? 0,
      errorMessage: map['error_message'] ?? '',
      source: map['source']?.toString() ?? '',
      isProMode: map['is_pro_mode'] == 1, // 新增：从整数转换回布尔值
      recommendations: _parseRecommendations(map['recommendations']), // 新增：解析推荐列表
      reasoning: map['reasoning'] ?? '', // 新增：从数据库读取推理过程
      lastCorrectTime:
          map['last_correct_time'] != null ? DateTime.parse(map['last_correct_time']) : null, // 新增：从数据库读取最近全对时间
      type: map['type'] ?? TYPE_NORMAL, // 新增：从数据库读取笔记类型，默认为普通笔记
    );
  }

  Note copyWith({
    int? id,
    String? title,
    List<StyledTextSegment>? content,
    List<StyledTextSegment>? originalContent,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
    bool? neverDelete,
    int? parentNoteId,
    String? category,
    String? subCategory,
    String? subSubCategory,
    String? prompt,
    int? status,
    int? retryCount,
    String? errorMessage,
    String? source,
    bool? isUpdating,
    bool? isProMode, // 新增
    List<String>? recommendations, // 新增
    String? reasoning, // 新增：推理过程
    DateTime? lastCorrectTime, // 新增：最近全对时间
    String? type, // 新增：笔记类型
  }) {
    return Note(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      originalContent: originalContent ?? this.originalContent,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      neverDelete: neverDelete ?? this.neverDelete,
      parentNoteId: parentNoteId ?? this.parentNoteId,
      category: category ?? this.category,
      subCategory: subCategory ?? this.subCategory,
      subSubCategory: subSubCategory ?? this.subSubCategory,
      prompt: prompt ?? this.prompt,
      status: status ?? this.status,
      retryCount: retryCount ?? this.retryCount,
      errorMessage: errorMessage ?? this.errorMessage,
      source: source ?? this.source,
      isProMode: isProMode ?? this.isProMode, // 新增
      recommendations: recommendations ?? this.recommendations, // 新增
      reasoning: reasoning ?? this.reasoning, // 新增：推理过程
      lastCorrectTime: lastCorrectTime ?? this.lastCorrectTime, // 新增：最近全对时间
      type: type ?? this.type, // 新增：笔记类型
    )..isUpdating = isUpdating ?? this.isUpdating;
  }

  static List<StyledTextSegment> _parseContent(String? content) {
    if (content == null || content.isEmpty) {
      return [];
    }
    try {
      List<dynamic> decodedList = json.decode(content);
      return decodedList.map((item) => StyledTextSegment.fromMap(item)).toList();
    } catch (e) {
      developer.log('Error parsing content: $e', error: e);
      return [StyledTextSegment(text: content)];
    }
  }

  static List<String> _parseRecommendations(String? recommendationsJson) {
    if (recommendationsJson == null || recommendationsJson.isEmpty) {
      return [];
    }
    try {
      List<dynamic> decodedList = json.decode(recommendationsJson);
      return decodedList.map((item) => item.toString()).toList();
    } catch (e) {
      developer.log('Error parsing recommendations: $e', error: e);
      return [];
    }
  }

  static Note createGuideNote() {
    final now = DateTime.now();

    // 在 Markdown 内容前添加一个空行，避免内容居中问题
    final content = '\n${AppConfig.guideNoteContent}';

    return Note(
      title: AppConfig.guideNoteTitle,
      content: MarkdownUtil.parseMarkdown(content),
      originalContent: MarkdownUtil.parseMarkdown(content),
      createdAt: now,
      updatedAt: now,
      source: Note.SOURCE_CLIPBOARD,
      prompt: 'Tempognize这个单词是什么意思？',
      category: 'Tempognize',
      subCategory: '使用指南',
      subSubCategory: '说明文档',
      neverDelete: true,
      reasoning: '', // 新增：默认为空字符串
    );
  }

  static Note createHowToNote() {
    final now = DateTime.now();

    // 在 Markdown 内容前添加一个空行，避免内容居中问题
    final content = '\n${AppConfig.howToNoteContent}';

    return Note(
      title: AppConfig.howToNoteTitle,
      content: MarkdownUtil.parseMarkdown(content),
      originalContent: MarkdownUtil.parseMarkdown(content),
      createdAt: now,
      updatedAt: now,
      source: Note.SOURCE_CLIPBOARD,
      prompt: '如何成为一个学习高手？',
      category: 'Tempognize',
      subCategory: '使用指南',
      subSubCategory: '说明文档',
      neverDelete: true,
      reasoning: '', // 新增：默认为空字符串
    );
  }
}
