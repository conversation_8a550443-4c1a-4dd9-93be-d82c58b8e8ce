// Package imports:
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:path/path.dart' as path;
import 'dart:developer' as developer;

// Project imports:
import '../l10n/localization.dart';
import '../models/note.dart';
import '../models/segment.dart';
import '../models/daily_note_stat.dart';
import '../services/event_bus.dart';
import '../models/quiz_question.dart';
import '../models/translation_training.dart';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._internal();
  Database? _database;

  factory DatabaseHelper() {
    return instance;
  }

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    if (kIsWeb) {
      // Web 平台初始化
      var factory = databaseFactoryFfiWeb;
      return await factory.openDatabase(
        'notes.db',
        options: OpenDatabaseOptions(
          version: 18,
          onCreate: _onCreate,
          onUpgrade: _onUpgrade,
          onOpen: _onOpen,
        ),
      );
    } else {
      // 移动端和桌面端初始化
      final databasesPath = await getDatabasesPath();
      final dbPath = path.join(databasesPath, 'notes.db');

      return await openDatabase(
        dbPath,
        version: 18,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onOpen: _onOpen,
      );
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE notes(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        content TEXT,
        original_content TEXT,
        created_at TEXT,
        updated_at TEXT,
        is_deleted INTEGER DEFAULT 0,
        never_delete INTEGER DEFAULT 0,
        parent_note_id INTEGER,
        category TEXT,
        sub_category TEXT,
        sub_sub_category TEXT,
        prompt TEXT DEFAULT '',
        status INTEGER DEFAULT 0,
        retry_count INTEGER DEFAULT 0,
        error_message TEXT DEFAULT '',
        source INTEGER DEFAULT 0,
        is_pro_mode INTEGER DEFAULT 0,
        recommendations TEXT DEFAULT '[]',
        reasoning TEXT DEFAULT '',
        last_correct_time TEXT,
        type TEXT DEFAULT 'normal'
      )
    ''');

    await db.execute('''
      CREATE TABLE daily_notes_stats(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        notes_count INTEGER DEFAULT 0
      )
    ''');

    // Add new table for favorited notes stats
    await db.execute('''
      CREATE TABLE daily_favorited_stats(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        favorited_count INTEGER DEFAULT 0
      )
    ''');

    await db.execute('''
      CREATE TABLE quiz_questions(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        note_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        questions_json TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (note_id) REFERENCES notes(id)
      )
    ''');

    // 添加翻译训练表 - 确保在新安装时也创建此表
    await db.execute('''
      CREATE TABLE translation_training(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        note_id INTEGER NOT NULL,
        training_data TEXT NOT NULL,
        user_responses TEXT NOT NULL DEFAULT '{}',
        ai_evaluations TEXT NOT NULL DEFAULT '{}',
        completed INTEGER DEFAULT 0,
        score REAL DEFAULT 0,
        current_item_index INTEGER DEFAULT 0,
        current_sentence_index INTEGER DEFAULT 0,
        statistics TEXT DEFAULT '{}',
        total_time INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (note_id) REFERENCES notes(id)
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 5) {
      await db.execute('ALTER TABLE notes ADD COLUMN parent_note_id INTEGER');
    }
    if (oldVersion < 6) {
      await db.execute('ALTER TABLE notes ADD COLUMN prompt TEXT DEFAULT \'\'');
    }
    if (oldVersion < 7) {
      // 添加新的状态相关列
      await db.execute('ALTER TABLE notes ADD COLUMN status INTEGER DEFAULT 0');
      await db.execute('ALTER TABLE notes ADD COLUMN retry_count INTEGER DEFAULT 0');
      await db.execute('ALTER TABLE notes ADD COLUMN error_message TEXT DEFAULT \'\'');
    }

    if (oldVersion < 8) {
      // 添加贡献统计表
      await db.execute('''
        CREATE TABLE daily_notes_stats(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          date TEXT NOT NULL,
          notes_count INTEGER DEFAULT 0
        )
      ''');
    }

    if (oldVersion < 9) {
      // 假设新版本号为9
      await db.execute('ALTER TABLE notes ADD COLUMN source TEXT DEFAULT \'\'');
    }

    if (oldVersion < 10) {
      await db.execute('''
        CREATE TABLE quiz_questions(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          note_id INTEGER NOT NULL,
          questions_json TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (note_id) REFERENCES notes(id)
        )
      ''');
    }

    if (oldVersion < 11) {
      // 为现有表添加 title 字段
      await db.execute('ALTER TABLE quiz_questions ADD COLUMN title TEXT DEFAULT ""');
    }

    if (oldVersion < 12) {
      // 添加 is_pro_mode 字段
      await db.execute('ALTER TABLE notes ADD COLUMN is_pro_mode INTEGER DEFAULT 0');
      // 添加 recommendations 字段
      await db.execute('ALTER TABLE notes ADD COLUMN recommendations TEXT DEFAULT \'[]\'');
    }

    if (oldVersion < 13) {
      // 添加 reasoning 字段
      await db.execute('ALTER TABLE notes ADD COLUMN reasoning TEXT DEFAULT \'\'');
    }

    if (oldVersion < 14) {
      // 添加翻译训练表
      await db.execute('''
        CREATE TABLE translation_training(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          note_id INTEGER NOT NULL,
          training_data TEXT NOT NULL,
          user_responses TEXT NOT NULL DEFAULT '{}',
          ai_evaluations TEXT NOT NULL DEFAULT '{}',
          completed INTEGER DEFAULT 0,
          score REAL DEFAULT 0,
          current_item_index INTEGER DEFAULT 0,
          current_sentence_index INTEGER DEFAULT 0,
          statistics TEXT DEFAULT '{}',
          total_time INTEGER DEFAULT 0,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          FOREIGN KEY (note_id) REFERENCES notes(id)
        )
      ''');
    }

    // 添加新的版本升级代码
    if (oldVersion < 15) {
      // 检查表是否存在
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='translation_training'",
      );

      if (tables.isNotEmpty) {
        // 检查列是否存在
        final columns = await db.rawQuery('PRAGMA table_info(translation_training)');
        final columnNames = columns.map((c) => c['name'] as String).toList();

        // 添加缺失的列
        if (!columnNames.contains('current_item_index')) {
          await db.execute(
            'ALTER TABLE translation_training ADD COLUMN current_item_index INTEGER DEFAULT 0',
          );
        }
        if (!columnNames.contains('current_sentence_index')) {
          await db.execute(
            'ALTER TABLE translation_training ADD COLUMN current_sentence_index INTEGER DEFAULT 0',
          );
        }
        if (!columnNames.contains('statistics')) {
          await db.execute(
            'ALTER TABLE translation_training ADD COLUMN statistics TEXT DEFAULT \'{}\'',
          );
        }
        if (!columnNames.contains('total_time')) {
          await db.execute(
            'ALTER TABLE translation_training ADD COLUMN total_time INTEGER DEFAULT 0',
          );
        }
      }
    }

    // Add new version for favorited stats table
    if (oldVersion < 16) {
      await db.execute('''
        CREATE TABLE IF NOT EXISTS daily_favorited_stats(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          date TEXT NOT NULL,
          favorited_count INTEGER DEFAULT 0
        )
      ''');

      // Initialize the table with existing favorited notes
      await _initializeFavoritedStats(db);
    }

    // Add last_correct_time column
    if (oldVersion < 17) {
      await db.execute('ALTER TABLE notes ADD COLUMN last_correct_time TEXT');
    }

    // Add type column for FAQ notes
    if (oldVersion < 18) {
      await db.execute('ALTER TABLE notes ADD COLUMN type TEXT DEFAULT \'normal\'');
    }
  }

  Future<void> _initializeFavoritedStats(Database db) async {
    // Get all favorited notes
    final favoritedNotes = await db.query(
      'notes',
      where: 'never_delete = ? AND is_deleted = ?',
      whereArgs: [1, 0], // 1 = true, 0 = false
    );

    // Count notes by date
    final Map<String, int> countsByDate = {};
    for (var note in favoritedNotes) {
      final createdAt = note['created_at'] as String;
      final dateStr = createdAt.split('T')[0];
      countsByDate[dateStr] = (countsByDate[dateStr] ?? 0) + 1;
    }

    // Insert into daily_favorited_stats
    for (var entry in countsByDate.entries) {
      await db.insert('daily_favorited_stats', {
        'date': entry.key,
        'favorited_count': entry.value,
      });
    }
  }

  Future<void> _onOpen(Database db) async {
    await verifyTables(db);
  }

  Future<void> verifyTables(Database db) async {
    try {
      // Check if translation_training table exists
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='translation_training'",
      );

      if (tables.isEmpty) {
        print('translation_training table not found! Creating it now...');

        // Create the missing table
        await db.execute('''
          CREATE TABLE IF NOT EXISTS translation_training(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            note_id INTEGER NOT NULL,
            training_data TEXT NOT NULL,
            user_responses TEXT NOT NULL DEFAULT '{}',
            ai_evaluations TEXT NOT NULL DEFAULT '{}',
            completed INTEGER DEFAULT 0,
            score REAL DEFAULT 0,
            current_item_index INTEGER DEFAULT 0,
            current_sentence_index INTEGER DEFAULT 0,
            statistics TEXT DEFAULT '{}',
            total_time INTEGER DEFAULT 0,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            FOREIGN KEY (note_id) REFERENCES notes(id)
          )
        ''');

        print('translation_training table created successfully!');
      }
    } catch (e) {
      print('Error verifying tables: $e');
    }
  }

  Future<int> insertNote(Note note) async {
    final db = await database;
    final noteMap = note.toMap();
    if (noteMap['original_content'] == null) {
      noteMap['original_content'] = noteMap['content'];
    }

    final id = await db.insert('notes', noteMap);
    // 插入笔记后更新当天的贡献统计
    await incrementDailyNoteCount(note.createdAt);
    return id;
  }

  Future<List<Note>> getNotes({int offset = 0, int limit = 20}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'is_deleted = 0',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );
    return List.generate(maps.length, (i) => Note.fromMap(maps[i]));
  }

  // 获取根笔记（没有父笔记的笔记）
  Future<List<Note>> getRootNotes({int offset = 0, int limit = 20}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'is_deleted = 0 AND (parent_note_id IS NULL OR parent_note_id = 0) AND (type IS NULL OR type != ?)',
      whereArgs: ['faq'],
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );
    return List.generate(maps.length, (i) => Note.fromMap(maps[i]));
  }

  // 获取根笔记总数
  Future<int> getRootNotesCount() async {
    final db = await database;
    final result = await db
        .rawQuery('SELECT COUNT(*) FROM notes WHERE is_deleted = 0 AND (parent_note_id IS NULL OR parent_note_id = 0) AND (type IS NULL OR type != ?)', ['faq']);
    return result.isNotEmpty ? result.first.values.first as int : 0;
  }

  // 根据父笔记ID获取子笔记
  Future<List<Note>> getChildNotes(int parentNoteId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'is_deleted = 0 AND parent_note_id = ? AND (type IS NULL OR type != ?)',
      whereArgs: [parentNoteId, 'faq'],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Note.fromMap(maps[i]));
  }

  // 检查笔记是否有子笔记
  Future<bool> hasChildNotes(int noteId) async {
    final db = await database;
    final result =
        await db.rawQuery('SELECT COUNT(*) FROM notes WHERE is_deleted = 0 AND parent_note_id = ?', [noteId]);
    final count = result.isNotEmpty ? result.first.values.first as int : 0;
    return count > 0;
  }

  // 获取指定父笔记的FAQ笔记
  Future<Note?> getFAQNote(int parentNoteId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'is_deleted = 0 AND parent_note_id = ? AND type = ?',
      whereArgs: [parentNoteId, 'faq'],
      limit: 1,
    );
    return maps.isNotEmpty ? Note.fromMap(maps.first) : null;
  }

  // 检查笔记是否已有FAQ
  Future<bool> hasFAQNote(int parentNoteId) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) FROM notes WHERE is_deleted = 0 AND parent_note_id = ? AND type = ?',
      [parentNoteId, 'faq'],
    );
    final count = result.isNotEmpty ? result.first.values.first as int : 0;
    return count > 0;
  }

  Future<int> deleteNote(int id) async {
    final db = await database;

    // 首先获取要删除的笔记
    final noteToDelete = await getNoteById(id);
    if (noteToDelete == null) {
      return 0;
    }

    // 更新贡献统计
    await decrementDailyNoteCount(noteToDelete.createdAt);

    // 如果有父笔记，更新父笔记中的链接
    if (noteToDelete.parentNoteId != null) {
      final parentNote = await getNoteById(noteToDelete.parentNoteId!);
      if (parentNote != null) {
        List<StyledTextSegment> updatedContent = [];
        for (var segment in parentNote.content) {
          if (segment.linkedNoteId == id) {
            // 移除链接，但保留高亮，以及颜色、字体大小等设置
            updatedContent.add(StyledTextSegment(
              text: segment.text,
              isHighlighted: segment.isHighlighted,
              color: segment.color,
              fontSizeAddLevel: segment.fontSizeAddLevel,
              bold: segment.bold,
              italic: segment.italic,
              strikethrough: segment.strikethrough,
              linkedNoteId: null,
            ));
          } else {
            updatedContent.add(segment);
          }
        }

        // 更新父笔记
        await updateNote(parentNote.copyWith(content: updatedContent));
      }
    }

    // 删除笔记（标记为已删除）
    final result = await db.update(
      'notes',
      {'is_deleted': 1},
      where: 'id = ?',
      whereArgs: [id],
    );

    NoteEvents.eventBus.fire(const NoteDeletedEvent());
    return result;
  }

  Future<int> getNotesCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) FROM notes WHERE is_deleted = 0');
    return result.isNotEmpty ? result.first.values.first as int : 0;
  }

  Future<void> deleteExpiredNotes(int defaultDeleteDays) async {
    final db = await database;
    final expirationDate = DateTime.now().subtract(Duration(days: defaultDeleteDays));
    await db.delete(
      'notes',
      where: 'created_at <= ? AND never_delete = 0',
      whereArgs: [expirationDate.toIso8601String()],
    );
  }

  Future<int> updateNote(Note note) async {
    final db = await database;

    // Check if the favorited status has changed
    final oldNote = await getNoteById(note.id!);
    if (oldNote != null && oldNote.neverDelete != note.neverDelete) {
      if (note.neverDelete) {
        // Note was favorited
        await incrementDailyFavoritedCount(note.createdAt);
      } else {
        // Note was unfavorited
        await decrementDailyFavoritedCount(note.createdAt);
      }
    }

    return await db.update(
      'notes',
      note.toMap(),
      where: 'id = ?',
      whereArgs: [note.id],
    );
  }

  Future<void> updateNoteContent(int noteId, String newContent) async {
    final db = await database;
    await db.update(
      'notes',
      {'content': newContent, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [noteId],
    );
  }

  Future<Note?> getNoteById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Note.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Note>> getSavedNotes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'never_delete = ? AND is_deleted = ?',
      whereArgs: [1, 0], // 1 表示 true，0 表示 false
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Note.fromMap(maps[i]);
    });
  }

  // 添加新的方法来处理daily_notes_stats表
  Future<void> incrementDailyNoteCount(DateTime date) async {
    final db = await database;
    final dateStr = date.toIso8601String().split('T')[0];

    await db.transaction((txn) async {
      final result = await txn.query(
        'daily_notes_stats',
        where: 'date = ?',
        whereArgs: [dateStr],
      );

      if (result.isEmpty) {
        await txn.insert('daily_notes_stats', {
          'date': dateStr,
          'notes_count': 1,
        });
      } else {
        final newCount = (result.first['notes_count'] as int? ?? 0) + 1;
        await txn.update(
          'daily_notes_stats',
          {'notes_count': newCount},
          where: 'date = ?',
          whereArgs: [dateStr],
        );
      }
    });
  }

  // 获取指定日期范围内的贡献数据
  Future<List<DailyNoteStat>> getDailyNotesStats(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final startDateStr = startDate.toIso8601String().split('T')[0];
    final endDateStr = endDate.toIso8601String().split('T')[0];

    final List<Map<String, dynamic>> maps = await db.query(
      'daily_notes_stats',
      where: 'date BETWEEN ? AND ?',
      whereArgs: [startDateStr, endDateStr],
      orderBy: 'date ASC',
    );

    return maps.map((map) => DailyNoteStat.fromMap(map)).toList();
  }

  Future<void> decrementDailyNoteCount(DateTime date) async {
    final db = await database;
    final dateStr = date.toIso8601String().split('T')[0];

    await db.transaction((txn) async {
      final result = await txn.query(
        'daily_notes_stats',
        where: 'date = ?',
        whereArgs: [dateStr],
      );

      if (result.isNotEmpty) {
        final currentCount = result.first['notes_count'] as int? ?? 0;
        if (currentCount > 0) {
          await txn.update(
            'daily_notes_stats',
            {'notes_count': currentCount - 1},
            where: 'date = ?',
            whereArgs: [dateStr],
          );
          developer.log('Decreased note count for $dateStr to ${currentCount - 1}');
        }
      }
    });
  }

  Future<List<Note>> getNotesByCategory(String category) async {
    final db = await database;

    // 在三个分类字段中搜索匹配的笔记，并且只返回未删除的笔记
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: '(category = ? OR sub_category = ? OR sub_sub_category = ?) AND is_deleted = 0',
      whereArgs: [category, category, category],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Note.fromMap(maps[i]);
    });
  }

  Future<List<Note>> searchNotes(String keyword) async {
    final db = await database;

    // 使用 LIKE 进行模糊搜索，同时搜索标题和内容
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where:
          '(title LIKE ? OR content LIKE ? OR category = ? OR sub_category = ? OR sub_sub_category = ?) AND is_deleted = 0',
      whereArgs: ['%$keyword%', '%$keyword%', '%$keyword%', '%$keyword%', '%$keyword%'],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Note.fromMap(maps[i]);
    });
  }

  Future<Note?> getNoteByPrompt(String prompt) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'prompt = ? AND is_deleted = 0',
      whereArgs: [prompt],
      orderBy: 'created_at DESC',
      limit: 1,
    );

    if (maps.isEmpty) {
      return null;
    }

    return Note.fromMap(maps.first);
  }

  // 插入新的题目
  Future<int> insertQuizQuestion(QuizQuestion question) async {
    final db = await database;

    // 获取笔记标题
    final note = await getNoteById(question.noteId);
    if (note == null) {
      throw Exception('Note not found');
    }

    // 获取当前笔记的测试数量
    final count = await db.rawQuery(
      'SELECT COUNT(*) as count FROM quiz_questions WHERE note_id = ?',
      [question.noteId],
    );
    final quizNumber = (count.isNotEmpty ? count.first.values.first as int : 0) + 1;

    // 生成标题
    final title = '${note.title} - ${AppLocalizations.instance.review} $quizNumber';

    // 创建包含标题的新 QuizQuestion
    final questionWithTitle = QuizQuestion(
      noteId: question.noteId,
      title: title,
      questionsJson: question.questionsJson,
      createdAt: question.createdAt,
      updatedAt: question.updatedAt,
    );

    return await db.insert('quiz_questions', questionWithTitle.toMap());
  }

  // 根据笔记ID获取题目
  Future<QuizQuestion?> getQuizQuestionByNoteId(int noteId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'quiz_questions',
      where: 'note_id = ?',
      whereArgs: [noteId],
    );

    if (maps.isEmpty) {
      return null;
    }
    return QuizQuestion.fromMap(maps.first);
  }

  // 更新题目
  Future<int> updateQuizQuestion(QuizQuestion question) async {
    final db = await database;
    return await db.update(
      'quiz_questions',
      question.toMap(),
      where: 'id = ?',
      whereArgs: [question.id],
    );
  }

  // 删除题目
  Future<int> deleteQuizQuestion(int id) async {
    final db = await database;
    return await db.delete(
      'quiz_questions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<QuizQuestion>> getQuizzes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'quiz_questions',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return QuizQuestion.fromMap(maps[i]);
    });
  }

  // 分页获取测试列表
  Future<List<QuizQuestion>> getQuizzesWithPagination({
    int offset = 0,
    int limit = 20,
  }) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'quiz_questions',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return QuizQuestion.fromMap(maps[i]);
    });
  }

  // 获取测试总数
  Future<int> getQuizzesCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) FROM quiz_questions');
    return result.isNotEmpty ? result.first.values.first as int : 0;
  }

  Future<List<QuizQuestion>> getQuizzesByNoteId(int noteId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'quiz_questions',
      where: 'note_id = ?',
      whereArgs: [noteId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return QuizQuestion.fromMap(maps[i]);
    });
  }

  Future<int> saveTranslationTraining(TranslationTraining training) async {
    final db = await database;

    if (training.id != null) {
      return await db.update(
        'translation_training',
        training.toMap(),
        where: 'id = ?',
        whereArgs: [training.id],
      );
    } else {
      return await db.insert('translation_training', training.toMap());
    }
  }

  Future<TranslationTraining?> getTranslationTrainingByNoteId(int noteId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'translation_training',
      where: 'note_id = ?',
      whereArgs: [noteId],
    );

    if (maps.isEmpty) {
      return null;
    }

    return TranslationTraining.fromMap(maps.first);
  }

  Future<List<TranslationTraining>> getAllTranslationTrainings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('translation_training');

    return List.generate(maps.length, (i) => TranslationTraining.fromMap(maps[i]));
  }

  Future<int> deleteTranslationTraining(int id) async {
    final db = await database;
    return await db.delete(
      'translation_training',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Add methods to update favorited notes count
  Future<void> incrementDailyFavoritedCount(DateTime date) async {
    final db = await database;
    final dateStr = date.toIso8601String().split('T')[0];

    await db.transaction((txn) async {
      final result = await txn.query(
        'daily_favorited_stats',
        where: 'date = ?',
        whereArgs: [dateStr],
      );

      if (result.isEmpty) {
        await txn.insert('daily_favorited_stats', {
          'date': dateStr,
          'favorited_count': 1,
        });
      } else {
        final newCount = (result.first['favorited_count'] as int? ?? 0) + 1;
        await txn.update(
          'daily_favorited_stats',
          {'favorited_count': newCount},
          where: 'date = ?',
          whereArgs: [dateStr],
        );
      }
    });

    // Fire an event to notify that favorited stats have changed
    NoteEvents.eventBus.fire(const FavoritedStatsChangedEvent());
  }

  Future<void> decrementDailyFavoritedCount(DateTime date) async {
    final db = await database;
    final dateStr = date.toIso8601String().split('T')[0];

    await db.transaction((txn) async {
      final result = await txn.query(
        'daily_favorited_stats',
        where: 'date = ?',
        whereArgs: [dateStr],
      );

      if (result.isNotEmpty) {
        final currentCount = result.first['favorited_count'] as int? ?? 0;
        if (currentCount > 0) {
          await txn.update(
            'daily_favorited_stats',
            {'favorited_count': currentCount - 1},
            where: 'date = ?',
            whereArgs: [dateStr],
          );
          developer.log('Decreased favorited count for $dateStr to ${currentCount - 1}');
        }
      }
    });

    // Fire an event to notify that favorited stats have changed
    NoteEvents.eventBus.fire(const FavoritedStatsChangedEvent());
  }

  // Get favorited stats for a date range
  Future<List<DailyNoteStat>> getDailyFavoritedStats(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final startDateStr = startDate.toIso8601String().split('T')[0];
    final endDateStr = endDate.toIso8601String().split('T')[0];

    final List<Map<String, dynamic>> maps = await db.query(
      'daily_favorited_stats',
      where: 'date BETWEEN ? AND ?',
      whereArgs: [startDateStr, endDateStr],
      orderBy: 'date ASC',
    );

    return maps.map((map) {
      // Convert to DailyNoteStat - we'll reuse this model but populate it with favorited counts
      return DailyNoteStat(
        id: map['id'] as int?,
        date: DateTime.parse(map['date']),
        notesCount: map['favorited_count'] as int? ?? 0,
      );
    }).toList();
  }
}
