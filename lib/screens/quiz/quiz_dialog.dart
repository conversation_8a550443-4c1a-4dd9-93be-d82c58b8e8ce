import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../helpers/database_helper.dart';
import '../../models/note.dart';
import '../../l10n/localization.dart';
import '../../services/enhanced_ai_service.dart';
import 'dart:developer' as developer;
import '../../models/quiz_question.dart';
import '../../services/auth_service.dart';
import 'package:get/get.dart';
import 'quiz_screen.dart';
import '../../services/event_bus.dart';

class QuizDialog extends StatefulWidget {
  final Note note;
  final Function(Note) onToggleNeverDelete;
  final bool withAward;

  const QuizDialog({
    super.key,
    required this.note,
    required this.onToggleNeverDelete,
    required this.withAward,
  });

  @override
  State<QuizDialog> createState() => _QuizDialogState();
}

class _QuizDialogState extends State<QuizDialog> {
  bool _isLoading = false;

  Future<void> _getQuizQuestions() async {
    // 添加登录检查
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.pleaseLoginFirst);
      Get.toNamed('/login');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 获取笔记的全文内容
      final noteContent = widget.note.content.map((segment) => segment.text).join();

      // 直接调用生成题目的方法
      final response = await EnhancedAIService.instance.generateQuizQuestions(noteContent);

      // 处理配额信息
      if (response.containsKey('remaining_quota')) {
        final event = QuotaUpdatedEvent(response['remaining_quota']);
        await event.saveToLocal();
        NoteEvents.eventBus.fire(event);
      }

      // 创建新的 QuizQuestion 对象
      final quizQuestion = QuizQuestion(
        noteId: widget.note.id!,
        title: '',
        questionsJson: response['questions_json'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 保存到数据库
      final id = await DatabaseHelper.instance.insertQuizQuestion(quizQuestion);

      developer.log('Quiz questions saved to database with id: $id');

      // 发送 QuizUpdatedEvent
      NoteEvents.eventBus.fire(QuizUpdatedEvent());

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // 关闭当前对话框并打开答题页面
      final result = await Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => QuizScreen(
            quizQuestion: quizQuestion,
            withAward: widget.withAward,
          ),
        ),
      );

      // 如果答题全对，更新笔记状态
      if (result == true) {
        // 更新笔记的 neverDelete 状态和 lastCorrectTime
        final updatedNote = widget.note.copyWith(
          neverDelete: true,
          lastCorrectTime: DateTime.now(),
        );
        await DatabaseHelper.instance.updateNote(updatedNote);

        // 发送笔记收藏状态变更事件
        NoteEvents.eventBus.fire(NoteFavoriteChangedEvent(updatedNote));

        // 调用回调函数更新父组件状态
        widget.onToggleNeverDelete(updatedNote);
      }
    } catch (e) {
      if (e is DioException && e.response?.statusCode == 403) {
        Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.quotaExceeded);
      } else {
        Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.generateQuizFailed);
      }

      developer.log('Error getting quiz questions:', error: e);
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(AppLocalizations.instance.neverDelete),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      insetPadding: EdgeInsets.only(
        bottom: MediaQuery.of(context).size.height * 0.4, // 从底部抬高
        left: 16,
        right: 16,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(AppLocalizations.instance.generateQuizDescription),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                shadowColor: Colors.transparent, // 移除阴影
              ),
              onPressed: _isLoading ? null : _getQuizQuestions,
              child: _isLoading
                  ? Transform.scale(
                      scale: 0.6,
                      child: const CircularProgressIndicator(
                        strokeWidth: 4.0,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(AppLocalizations.instance.answerQuiz),
            ),
          ),
          const SizedBox(height: 16),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.instance.cancel),
          ),
          const SizedBox(height: 6),
        ],
      ),
    );
  }
}
