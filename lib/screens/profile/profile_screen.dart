// Flutter imports:
import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tempognize/screens/profile/settings_page.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../helpers/database_helper.dart';
// Project imports:
import '../../l10n/localization.dart';
import '../../models/daily_note_stat.dart';
import '../../services/auth_service.dart';
import '../../services/event_bus.dart';
import '../../widgets/contribution_graph.dart';
import '../timeline/saved_notes_page.dart';
import '../../utils/image_util.dart';
import '../../routes/app_routes.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final ScrollController _scrollController = ScrollController();
  double _avatarOpacity = 0.0;
  final double _expandedHeight = 360.0;
  final double _avatarShowThreshold = 0.9; // FlexibleSpaceBar 变为半透明时的阈值
  List<DailyNoteStat> _contributionStats = [];
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  String? _userEmail; // 添加用户邮箱状态
  int _remainingQuota = 0;
  StreamSubscription? _loginStatusSubscription;
  int _quizzesCount = 0;
  String? _customAvatarPath;
  String? _customBackgroundPath;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadContributionStats();
    _checkLoginStatus();

    // 修改事件监听的方式
    _loginStatusSubscription = NoteEvents.eventBus.on<LoginStatusChangedEvent>().listen((event) {
      _checkLoginStatus();
    });

    // 监听笔记创建事件
    NoteEvents.eventBus.on<NoteCreatedEvent>().listen((_) {
      _loadContributionStats();
    });

    // 监听笔记删除事件
    NoteEvents.eventBus.on<NoteDeletedEvent>().listen((_) {
      _loadContributionStats();
    });

    // 监听收藏统计变更事件
    NoteEvents.eventBus.on<FavoritedStatsChangedEvent>().listen((_) {
      _loadContributionStats();
    });

    // 初始加载配额
    _loadQuota();

    // 监听配额更新事件
    NoteEvents.eventBus.on<QuotaUpdatedEvent>().listen((event) {
      if (mounted) {
        setState(() {
          _remainingQuota = event.remainingQuota;
        });
      }
    });

    // 监听笔记更新事件
    NoteEvents.eventBus.on<NoteUpdatedEvent>().listen((event) {
      if (mounted && event.showConfettiAnimation) {
        _loadQuota(); // 重新加载配额
      }
    });

    // 监听 QuizUpdatedEvent
    NoteEvents.eventBus.on<QuizUpdatedEvent>().listen((_) {
      _loadQuizzes();
    });

    _loadQuizzes();
    _loadCustomImages();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    // 在组件销毁时取消事件监听
    _loginStatusSubscription?.cancel();
    super.dispose();
  }

  void _onScroll() {
    final double offset = _scrollController.offset;
    final double appBarShrinkPercentage = (offset / (_expandedHeight - kToolbarHeight)).clamp(0.0, 1.0);

    setState(() {
      if (appBarShrinkPercentage > _avatarShowThreshold) {
        // 当 FlexibleSpaceBar 收缩超过阈值时，开始显示头像
        _avatarOpacity = ((appBarShrinkPercentage - _avatarShowThreshold) / (1 - _avatarShowThreshold)).clamp(0.0, 1.0);
      } else {
        _avatarOpacity = 0.0;
      }
    });
  }

  Future<void> _loadContributionStats() async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(const Duration(days: 365));

    final stats = await _databaseHelper.getDailyFavoritedStats(startDate, endDate);

    if (mounted) {
      setState(() {
        _contributionStats = stats;
      });
    }
  }

  Future<void> _checkLoginStatus() async {
    final email = await AuthService().getUserEmail();
    setState(() {
      _userEmail = email;
    });
    if (email != null) {
      _loadQuota(); // 如果登录了，就加载配额
    }
  }

  Future<void> _handleAvatarTap() async {
    final token = await AuthService().getToken();
    if (token == null) {
      if (!mounted) return;
      final result = await Get.toNamed(Routes.login);
      if (result != null) {
        _checkLoginStatus();
      }
      return;
    }

    if (!mounted) return;
    final String fileName = 'avatar_${DateTime.now().millisecondsSinceEpoch}.jpg';
    final String? newAvatarPath = await ImageUtil.pickAndProcessImage(
      context: context,
      targetPath: fileName,
      maxWidth: 300,
      maxHeight: 300,
      isAvatar: true,
    );

    if (newAvatarPath != null) {
      final prefs = await SharedPreferences.getInstance();
      if (_customAvatarPath != null) {
        final oldFile = File(_customAvatarPath!);
        try {
          if (await oldFile.exists()) {
            await oldFile.delete();
          }
        } catch (e) {
          debugPrint('删除旧头像失败: $e');
        }
      }
      await prefs.setString('custom_avatar_path', newAvatarPath);
      setState(() {
        _customAvatarPath = newAvatarPath;
      });
    }
  }

  // 处理背景图点击
  Future<void> _handleBackgroundTap() async {
    if (!mounted) return;
    final String fileName = 'background_${DateTime.now().millisecondsSinceEpoch}.jpg';
    final String? newBackgroundPath = await ImageUtil.pickAndProcessImage(
      context: context,
      targetPath: fileName,
      maxWidth: 1920,
      maxHeight: 1080,
      isAvatar: false,
    );

    if (newBackgroundPath != null) {
      final prefs = await SharedPreferences.getInstance();
      if (_customBackgroundPath != null) {
        final oldFile = File(_customBackgroundPath!);
        try {
          if (await oldFile.exists()) {
            await oldFile.delete();
          }
        } catch (e) {
          debugPrint('删除旧背景失败: $e');
        }
      }
      await prefs.setString('custom_background_path', newBackgroundPath);
      setState(() {
        _customBackgroundPath = newBackgroundPath;
      });
    }
  }

  // 加载本地保存的配额
  Future<void> _loadQuota() async {
    final prefs = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        _remainingQuota = prefs.getInt('remaining_quota') ?? 0;
      });
    }
  }

  // 加载用户的 Quiz 数量
  Future<void> _loadQuizzes() async {
    final count = await _databaseHelper.getQuizzesCount();
    if (mounted) {
      setState(() {
        _quizzesCount = count;
      });
    }
  }

  // 加载自定义图片
  Future<void> _loadCustomImages() async {
    final prefs = await SharedPreferences.getInstance();
    String? savedAvatarPath = prefs.getString('custom_avatar_path');
    String? savedBackgroundPath = prefs.getString('custom_background_path');

    if (savedAvatarPath != null && !await File(savedAvatarPath).exists()) {
      await prefs.remove('custom_avatar_path');
      savedAvatarPath = null;
    }

    if (savedBackgroundPath != null && !await File(savedBackgroundPath).exists()) {
      await prefs.remove('custom_background_path');
      savedBackgroundPath = null;
    }

    setState(() {
      _customAvatarPath = savedAvatarPath;
      _customBackgroundPath = savedBackgroundPath;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          SliverAppBar(
            expandedHeight: _expandedHeight,
            floating: false,
            pinned: true,
            leading: IconButton(
              icon: FaIcon(FontAwesomeIcons.gear),
              color: Colors.white,
              onPressed: () {
                Get.toNamed(SettingsPage.routeName);
              },
              iconSize: 24,
              padding: EdgeInsets.all(8),
            ),
            actions: [
              IconButton(
                icon: FaIcon(FontAwesomeIcons.bookmark),
                color: Colors.white,
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SavedNotesPage(),
                    ),
                  );
                },
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              centerTitle: true,
              titlePadding: EdgeInsets.zero,
              title: Padding(
                padding: EdgeInsets.only(bottom: 10),
                child: AnimatedOpacity(
                  opacity: _avatarOpacity,
                  duration: Duration(milliseconds: 300),
                  child: CircleAvatar(
                    radius: 16,
                    backgroundImage: _customAvatarPath != null
                        ? FileImage(File(_customAvatarPath!))
                        : AssetImage('assets/icon/icon.png') as ImageProvider,
                  ),
                ),
              ),
              background: GestureDetector(
                onTap: _handleBackgroundTap,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    _customBackgroundPath != null
                        ? Image.file(
                            File(_customBackgroundPath!),
                            fit: BoxFit.cover,
                          )
                        : Image.asset(
                            'assets/images/bg.png',
                            fit: BoxFit.cover,
                          ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5), // 增加遮罩深度
                      ),
                    ),
                    Positioned(
                      left: 16,
                      right: 16,
                      bottom: 16,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              GestureDetector(
                                onTap: _handleAvatarTap,
                                child: CircleAvatar(
                                  radius: 40,
                                  backgroundImage: _customAvatarPath != null
                                      ? FileImage(File(_customAvatarPath!))
                                      : AssetImage('assets/icon/icon.png') as ImageProvider,
                                ),
                              ),
                              if (_userEmail != null) // 只在登录状态显示配额
                                Text(
                                  '${AppLocalizations.instance.remainingQuota} $_remainingQuota ${AppLocalizations.instance.times}',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                ),
                            ],
                          ),
                          SizedBox(height: 16),
                          Text(
                            _userEmail ?? AppLocalizations.instance.notLogin,
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Colors.white),
                          ),
                          Text(
                            _userEmail != null
                                ? '@${_userEmail!.split('@')[0]}'
                                : AppLocalizations.instance.clickAvatarToLogin,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Colors.white70),
                          ),
                          SizedBox(height: 8),
                          Text(
                            AppLocalizations.instance.socratesWords,
                            style: TextStyle(color: Colors.white70),
                          ),
                          SizedBox(
                            width: double.infinity, // 确保容器占据全宽
                            child: Text(
                              AppLocalizations.instance.socrates,
                              style: TextStyle(color: Colors.white70),
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            backgroundColor: Theme.of(context).primaryColor,
            systemOverlayStyle: SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.dark,
              // 确保导航栏样式保持一致
              systemNavigationBarColor: Colors.transparent,
              systemNavigationBarDividerColor: Colors.transparent,
              systemNavigationBarIconBrightness: Brightness.dark,
            ),
          ),
          SliverToBoxAdapter(
            child: ContributionGraph(
              stats: _contributionStats,
              startDate: DateTime.now().subtract(const Duration(days: 363)),
              endDate: DateTime.now(),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey[200]!, width: 0.8),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: InkWell(
                  onTap: () {
                    Get.toNamed(Routes.quizList);
                  },
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.quiz,
                            color: Theme.of(context).primaryColor,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '查看所有测试',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _quizzesCount > 0 ? '共 $_quizzesCount 个测试可供复习' : '暂无测试，开始创建笔记并生成测试吧',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          Icons.chevron_right,
                          color: Colors.grey[400],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(height: 24),
          ),
        ],
      ),
    );
  }
}

class SlidePageRoute extends PageRouteBuilder {
  final Widget child;
  final AxisDirection direction;

  SlidePageRoute({
    required this.child,
    this.direction = AxisDirection.right,
  }) : super(
          transitionDuration: const Duration(milliseconds: 200),
          reverseTransitionDuration: const Duration(milliseconds: 200),
          pageBuilder: (context, animation, secondaryAnimation) => child,
        );

  @override
  Widget buildTransitions(
      BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation, Widget child) {
    Offset offset = Offset(-1.0, 0.0);

    switch (direction) {
      case AxisDirection.right:
        offset = Offset(-1.0, 0.0);
        break;
      case AxisDirection.left:
        offset = Offset(1.0, 0.0);
        break;
      case AxisDirection.up:
        offset = Offset(0.0, 1.0);
        break;
      case AxisDirection.down:
        offset = Offset(0.0, -1.0);
        break;
    }

    return SlideTransition(
      position: Tween<Offset>(
        begin: offset,
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.easeInOut,
      )),
      child: child,
    );
  }
}
