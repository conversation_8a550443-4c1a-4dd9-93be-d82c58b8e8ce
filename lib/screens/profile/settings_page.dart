// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:tempognize/config.dart';

// Project imports:
import '../../l10n/localization.dart';
import '../../services/app_settings.dart';
import '../../services/auth_service.dart';
import '../../services/event_bus.dart';
import 'package:get/get.dart';
import '../../widgets/feature_hint.dart';
import '../../services/title_image_service.dart';
import '../../helpers/database_helper.dart';
import '../../models/note.dart';

class SettingsPage extends StatefulWidget {
  static const routeName = '/settings';

  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  String? _userEmail;

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    final email = await AuthService().getUserEmail();
    if (mounted) {
      setState(() {
        _userEmail = email;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final settings = Get.find<AppSettings>();
    final localizations = AppLocalizations.instance;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.settings),
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.inbox),
            onPressed: () {
              Get.toNamed('/notifications');
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader(theme, localizations.coreSettings),
            Card(
              elevation: 0,
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                  width: 1,
                ),
              ),
              color: Colors.white,
              child: Column(
                children: [
                  if (AppConfig.isDebug) ...[
                    ListTile(
                      leading: const FaIcon(FontAwesomeIcons.clock),
                      title: Text(localizations.noteLifespan),
                      subtitle: Text('${settings.defaultDeleteDays} ${localizations.days}'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        _showDeleteDaysDialog(context, settings, localizations);
                      },
                    ),
                    const Divider(height: 1),
                  ],
                  ListTile(
                    leading: const FaIcon(FontAwesomeIcons.lightbulb),
                    title: Text(localizations.resetFeatureHints),
                    subtitle: Text(localizations.resetFeatureHintsDescription),
                    onTap: () {
                      _showResetHintsDialog(context, localizations);
                    },
                  ),
                ],
              ),
            ),
            _buildSectionHeader(theme, localizations.lookAndFeel),
            Card(
              elevation: 0,
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                  width: 1,
                ),
              ),
              color: Colors.white,
              child: Column(
                children: [
                  ListTile(
                    leading: const FaIcon(FontAwesomeIcons.image),
                    title: Text(localizations.clearTitleImageCache),
                    subtitle: Text(localizations.clearTitleImageCacheDescription),
                    onTap: () => _showClearTitleImageCacheDialog(context),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const FaIcon(FontAwesomeIcons.palette),
                    title: Text(localizations.colorScheme),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const SizedBox(width: 24),
                    title: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: FlexColor.schemes[FlexScheme.amber]?.light.primary,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text('Amber Gold'),
                      ],
                    ),
                    trailing: settings.colorScheme == FlexScheme.amber
                        ? FaIcon(FontAwesomeIcons.check, color: theme.colorScheme.primary)
                        : null,
                    onTap: () => settings.setColorScheme(FlexScheme.amber),
                  ),
                  ListTile(
                    leading: const SizedBox(width: 24),
                    title: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: FlexColor.schemes[FlexScheme.material]?.light.primary,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text('Material You'),
                      ],
                    ),
                    trailing: settings.colorScheme == FlexScheme.material
                        ? FaIcon(FontAwesomeIcons.check, color: theme.colorScheme.primary)
                        : null,
                    onTap: () => settings.setColorScheme(FlexScheme.material),
                  ),
                  ListTile(
                    leading: const SizedBox(width: 24),
                    title: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: FlexColor.schemes[FlexScheme.blueM3]?.light.primary,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text('Ocean Blue'),
                      ],
                    ),
                    trailing: settings.colorScheme == FlexScheme.blueM3
                        ? FaIcon(FontAwesomeIcons.check, color: theme.colorScheme.primary)
                        : null,
                    onTap: () => settings.setColorScheme(FlexScheme.blueM3),
                  ),
                  ListTile(
                    leading: const SizedBox(width: 24),
                    title: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: FlexColor.schemes[FlexScheme.greenM3]?.light.primary,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text('Fresh Green'),
                      ],
                    ),
                    trailing: settings.colorScheme == FlexScheme.greenM3
                        ? FaIcon(FontAwesomeIcons.check, color: theme.colorScheme.primary)
                        : null,
                    onTap: () => settings.setColorScheme(FlexScheme.greenM3),
                  ),
                  ListTile(
                    leading: const SizedBox(width: 24),
                    title: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: FlexColor.schemes[FlexScheme.purpleM3]?.light.primary,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text('Royal Purple'),
                      ],
                    ),
                    trailing: settings.colorScheme == FlexScheme.purpleM3
                        ? FaIcon(FontAwesomeIcons.check, color: theme.colorScheme.primary)
                        : null,
                    onTap: () => settings.setColorScheme(FlexScheme.purpleM3),
                  ),
                ],
              ),
            ),
            _buildSectionHeader(theme, localizations.language),
            Card(
              elevation: 0,
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                  width: 1,
                ),
              ),
              color: Colors.white,
              child: Column(
                children: [
                  ListTile(
                    leading: const FaIcon(FontAwesomeIcons.language),
                    title: Text(localizations.language),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const SizedBox(width: 24),
                    title: Text(localizations.chinese),
                    trailing: settings.locale == const Locale('zh', '')
                        ? Icon(Icons.check, color: theme.colorScheme.primary)
                        : null,
                    onTap: () => settings.setLocale(const Locale('zh', '')),
                  ),
                  ListTile(
                    leading: const SizedBox(width: 24),
                    title: Text(localizations.english),
                    trailing: settings.locale == const Locale('en', '')
                        ? Icon(Icons.check, color: theme.colorScheme.primary)
                        : null,
                    onTap: () => settings.setLocale(const Locale('en', '')),
                  ),
                ],
              ),
            ),
            Card(
              elevation: 0,
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                  width: 1,
                ),
              ),
              color: Colors.white,
              child: Column(
                children: [
                  ListTile(
                    leading: const FaIcon(FontAwesomeIcons.noteSticky),
                    title: Text('初始化默认笔记'),
                    subtitle: Text('创建产品介绍和使用指南笔记'),
                    onTap: () => _showInitializeDefaultNotesDialog(context),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.info_outline),
                    title: Text(localizations.aboutTempognize),
                    onTap: () => Get.toNamed('/about'),
                  ),
                ],
              ),
            ),
            if (_userEmail != null) ...[
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      try {
                        await AuthService().logout();
                        NoteEvents.eventBus.fire(LoginStatusChangedEvent(false));
                        Get.back();
                        setState(() {
                          _userEmail = null;
                        });
                      } catch (e) {
                        if (!mounted) return;
                        Get.snackbar(AppLocalizations.instance.message, 'Logout failed: $e');
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      localizations.logout,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _showDeleteAccountDialog(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      localizations.deleteAccount,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ),
            ],
            const SizedBox(height: 120),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(ThemeData theme, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
      child: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          color: theme.colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showDeleteDaysDialog(BuildContext context, AppSettings settings, AppLocalizations localizations) {
    int days = settings.defaultDeleteDays;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.setNoteLifespan),
          content: TextField(
            keyboardType: TextInputType.number,
            onChanged: (value) {
              days = int.tryParse(value) ?? days;
            },
            decoration: InputDecoration(
              hintText: localizations.enterNumberBetween,
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(localizations.cancel),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(localizations.confirm),
              onPressed: () {
                if (days >= 8 && days <= 30) {
                  settings.setDefaultDeleteDays(days);
                  Navigator.of(context).pop();
                } else {
                  Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.enterNumberBetween);
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _showResetHintsDialog(BuildContext context, AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.resetFeatureHints),
          content: Text(localizations.resetFeatureHintsConfirm),
          actions: <Widget>[
            TextButton(
              child: Text(localizations.cancel),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(localizations.confirm),
              onPressed: () async {
                await FeatureHintManager().resetAllHints();
                if (context.mounted) {
                  Navigator.of(context).pop();
                  Get.snackbar(AppLocalizations.instance.message, localizations.resetFeatureHintsSuccess);
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    final localizations = AppLocalizations.instance;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.deleteAccountConfirm),
          content: Text(localizations.deleteAccountDescription),
          actions: <Widget>[
            TextButton(
              child: Text(localizations.cancel),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: Text(localizations.confirm),
              onPressed: () async {
                try {
                  await AuthService().deleteAccount();
                  await AuthService().clearStoredEmail();
                  NoteEvents.eventBus.fire(LoginStatusChangedEvent(false));

                  if (context.mounted) {
                    Navigator.of(context).pop(); // 关闭对话框
                    Get.back(); // 返回上一页
                    Get.snackbar(AppLocalizations.instance.message, localizations.deleteAccountSuccess);
                  }
                } catch (e) {
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    Get.snackbar(AppLocalizations.instance.message, '${localizations.deleteAccountFailed}: $e');
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _showClearTitleImageCacheDialog(BuildContext context) {
    final localizations = AppLocalizations.instance;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.clearTitleImageCache),
        content: Text(localizations.clearTitleImageCacheConfirm),
        actions: [
          TextButton(
            child: Text(localizations.cancel),
            onPressed: () => Navigator.pop(context),
          ),
          TextButton(
            child: Text(localizations.confirm),
            onPressed: () async {
              await TitleImageService().clearCache();
              if (context.mounted) {
                Navigator.pop(context);
                Get.snackbar(AppLocalizations.instance.message, localizations.clearTitleImageCacheSuccess);
              }
            },
          ),
        ],
      ),
    );
  }

  void _showInitializeDefaultNotesDialog(BuildContext context) {
    final localizations = AppLocalizations.instance;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.initializeDefaultNotes),
          content: Text(localizations.initializeDefaultNotesDescription),
          actions: <Widget>[
            TextButton(
              child: Text(localizations.cancel),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(localizations.confirm),
              onPressed: () async {
                try {
                  // 创建并保存引导笔记
                  final guideNote = Note.createGuideNote();
                  final db = DatabaseHelper();
                  await db.insertNote(guideNote);

                  // 创建并保存使用指南笔记
                  final howToNote = Note.createHowToNote();
                  await db.insertNote(howToNote);

                  // 发送刷新列表的事件
                  NoteEvents.eventBus.fire(const NotesRefreshEvent());

                  if (context.mounted) {
                    Navigator.of(context).pop();
                    Get.snackbar(
                      AppLocalizations.instance.message,
                      localizations.initializeDefaultNotesSuccess,
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    Get.snackbar(
                      AppLocalizations.instance.message,
                      'Error: $e',
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }
}
