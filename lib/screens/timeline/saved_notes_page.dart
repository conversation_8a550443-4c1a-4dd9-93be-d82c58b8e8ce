// Flutter imports:
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Project imports:
import '../../constants/layout_config.dart';
import '../../helpers/database_helper.dart';
import '../../l10n/localization.dart';
import '../../models/note.dart';
import '../../widgets/enhanced_note_card.dart';
import '../../services/app_settings.dart';
import '../note_detail/note_detail_page.dart';
import '../../widgets/countdown_progress_bar.dart';
import '../../services/event_bus.dart';

class SavedNotesPage extends StatefulWidget {
  const SavedNotesPage({super.key});

  @override
  State<SavedNotesPage> createState() => _SavedNotesPageState();
}

class _SavedNotesPageState extends State<SavedNotesPage> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  List<Note> _savedNotes = [];
  bool _isLoading = false;
  final Map<int, bool> _dismissingNotes = {};
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  late StreamSubscription _refreshSubscription;

  @override
  void initState() {
    super.initState();
    _loadSavedNotes();

    _refreshSubscription = NoteEvents.eventBus.on<RefreshProfileEvent>().listen((_) {
      if (mounted) {
        _loadSavedNotes();
      }
    });
  }

  @override
  void dispose() {
    _refreshSubscription.cancel();
    super.dispose();
  }

  Future<void> _loadSavedNotes() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
    });

    try {
      final notes = await _databaseHelper.getSavedNotes();
      if (!mounted) return;
      setState(() {
        _savedNotes = notes;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _openNoteDetail(Note note) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoteDetailPage(
          note: note,
          onNoteCreated: () async {
            await _loadSavedNotes();
          },
        ),
      ),
    );
  }

  Future<void> _toggleNeverDelete(Note note) async {
    final index = _savedNotes.indexWhere((n) => n.id == note.id);
    if (index == -1) return;

    final updatedNote = note.copyWith(
      neverDelete: !note.neverDelete,
      updatedAt: DateTime.now(),
    );
    await _databaseHelper.updateNote(updatedNote);

    if (_savedNotes.length == 1) {
      if (mounted) {
        setState(() {
          _savedNotes = [];
        });
      }
    } else {
      if (mounted && _listKey.currentState != null) {
        final removedNote = _savedNotes[index];
        setState(() {
          _savedNotes.removeAt(index);
        });

        _listKey.currentState!.removeItem(
          index,
          (context, animation) => SizeTransition(
            sizeFactor: animation,
            child: FadeTransition(
              opacity: animation,
              child: NoteCard(
                note: removedNote,
                isSelected: false,
                onTap: () {},
                onLongPress: () {},
                onToggleNeverDelete: (_) {},
                onRetryAI: (_) {},
                onCancelAI: () {},
                layout: TimelineLayout.single,
                child: CountdownProgressBar(
                  note: removedNote,
                  defaultDeleteDays: Get.find<AppSettings>().defaultDeleteDays,
                ),
              ),
            ),
          ),
          duration: const Duration(milliseconds: 300),
        );
      }
    }

    NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.instance;

    final appSettings = Get.find<AppSettings>();

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.favoritedNotes),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _savedNotes.isEmpty
              ? Center(
                  child: Text(
                    localizations.noFavoritedNotes,
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                )
              : AnimatedList(
                  key: _listKey,
                  initialItemCount: _savedNotes.length,
                  itemBuilder: (context, index, animation) {
                    final note = _savedNotes[index];
                    return SizeTransition(
                      sizeFactor: animation,
                      child: AnimatedSlide(
                        duration: Duration(milliseconds: 300),
                        offset: _dismissingNotes[note.id] == true ? Offset(1.0, 0.0) : Offset.zero,
                        child: AnimatedOpacity(
                          duration: Duration(milliseconds: 300),
                          opacity: _dismissingNotes[note.id] == true ? 0.0 : 1.0,
                          child: NoteCard(
                            note: note,
                            isSelected: false,
                            onTap: () => _openNoteDetail(note),
                            onLongPress: () {},
                            onToggleNeverDelete: _toggleNeverDelete,
                            onRetryAI: (_) {},
                            onCancelAI: () {},
                            layout: TimelineLayout.single,
                            child: CountdownProgressBar(
                              note: note,
                              defaultDeleteDays: appSettings.defaultDeleteDays,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}
