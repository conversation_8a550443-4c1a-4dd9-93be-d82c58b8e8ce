// Flutter imports:
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:confetti/confetti.dart';

// Project imports:
import '../../constants/layout_config.dart';
import '../../helpers/database_helper.dart';
import '../../l10n/localization.dart';
import '../../models/note.dart';
import '../../services/ai_service.dart';
import '../../services/app_settings.dart';
import '../../services/auth_service.dart';
import '../../services/event_bus.dart';
import '../../widgets/countdown_progress_bar.dart';
import '../../widgets/feature_hint.dart';
import '../../widgets/note_card.dart';
import '../../widgets/game_style_quota_popup.dart';
import '../auth/login_page.dart';
import '../note_detail/note_detail_page.dart';
import '../note_hierarchy/note_hierarchy_page.dart';
import '../search/search_page.dart';

class TimelinePage extends StatefulWidget {
  const TimelinePage({
    super.key,
    required this.onEnterMultiSelectMode,
    required this.onExitMultiSelectMode,
    required this.onSelectedCountChanged,
  });

  final Function(int) onEnterMultiSelectMode;
  final Function() onExitMultiSelectMode;
  final Function(int) onSelectedCountChanged;

  @override
  TimelinePageState createState() => TimelinePageState();
}

class TimelinePageState extends State<TimelinePage> {
  static const int _pageSize = 20;

  int _currentPage = 0;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  bool _hasMore = true;
  bool _isLoading = false;
  bool _isMultiSelectMode = false;
  List<Note> _notes = [];
  final ScrollController _scrollController = ScrollController();
  final Set<int> _selectedNotes = {};
  late StreamSubscription _noteUpdatedSubscription;
  late StreamSubscription _noteCreatedSubscription;
  final AIService _aiService = AIService();

  static const String _addButtonHintId = 'add_button_hint';
  late StreamSubscription _noteFavoriteChangedSubscription;
  late StreamSubscription _notesRefreshSubscription;

  // 添加新的状态变量
  final ConfettiController _confettiController = ConfettiController(duration: const Duration(seconds: 2));
  int _remainingQuota = 0;
  bool _showQuotaPopup = false;
  late StreamSubscription _quotaUpdatedSubscription;

  // 添加通知队列，记录需要显示通知的笔记ID
  final List<int> _pendingSuccessNotes = [];
  // 防抖变量，避免频繁显示通知
  bool _isShowingPopup = false;

  // 添加布局状态
  TimelineLayout _currentLayout = TimelineLayout.double;

  // 添加 AppSettings 控制器
  late final AppSettings _appSettings;

  @override
  void dispose() {
    _noteUpdatedSubscription.cancel();
    _noteCreatedSubscription.cancel();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _aiService.dispose();
    _noteFavoriteChangedSubscription.cancel();
    _notesRefreshSubscription.cancel();
    _quotaUpdatedSubscription.cancel(); // 取消配额更新订阅
    _confettiController.dispose(); // 释放纸屑控制器
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // 获取 AppSettings 实例
    _appSettings = Get.find<AppSettings>();
    // 使用 _appSettings 替代 Provider
    _currentLayout = _appSettings.timelineLayout;

    _loadNotes();
    _scrollController.addListener(_onScroll);
    _deleteExpiredNotes();

    // 获取当前剩余AI余额
    _loadRemainingQuota();

    // 监听配额更新事件
    _quotaUpdatedSubscription = NoteEvents.eventBus.on<QuotaUpdatedEvent>().listen((event) {
      setState(() {
        _remainingQuota = event.remainingQuota;
      });
    });

    // Tutorial dialog is now handled by DialogQueueManager during app startup
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _showAddButtonHint();
    // });

    // 监听笔记列表刷新事件
    _notesRefreshSubscription = NoteEvents.eventBus.on<NotesRefreshEvent>().listen((_) {
      _refreshNotes();
    });

    _noteCreatedSubscription = NoteEvents.eventBus.on<NoteCreatedEvent>().listen((event) {
      // 只有根笔记（没有父笔记的笔记）才应该显示在时间线上
      if (event.note.parentNoteId == null || event.note.parentNoteId == 0) {
        // 原来响应NoteCreatedEvent事件的方法是，刷新数据库，现在采用事件自带的note数据直接添加到列表最顶端，由于这个note的isUpdating=true，所以会显示更新动画
        setState(() {
          // 输出新创建的笔记ID
          _notes.insert(0, event.note);
        });
        // 下面这段代码的作用是：在刷新完笔记列表后，将列表滚动到顶部
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
      // 如果是子笔记，不在时间线显示，但不做任何操作（让层级页面处理）
    });

    _noteUpdatedSubscription = NoteEvents.eventBus.on<NoteUpdatedEvent>().listen((event) {
      // 只有在成功状态时才显示成功提示
      if (event.note.status == Note.STATUS_SUCCESS) {
        Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.aiUpdateSuccess);

        // 成功时刷新AI余额信息并处理通知显示
        _loadRemainingQuota().then((_) {
          if (event.showConfettiAnimation) {
            // 不管当前是否显示弹窗，也不管队列中是否有其他通知，
            // 都清空当前状态，只显示最新的一个通知
            setState(() {
              // 停止任何正在播放的动画
              if (_confettiController.state == ConfettiControllerState.playing) {
                _confettiController.stop();
              }

              // 清空当前队列
              _pendingSuccessNotes.clear();

              // 添加新的通知ID
              _pendingSuccessNotes.add(event.note.id ?? 0);

              // 重置显示状态（确保我们下面的显示代码会被执行）
              _isShowingPopup = false;
              _showQuotaPopup = false;
            });

            // 短暂延迟确保状态更新完成
            Future.delayed(Duration(milliseconds: 50), () {
              // 显示新通知
              setState(() {
                _isShowingPopup = true;
                _showQuotaPopup = true;
                _confettiController.play();
              });
            });
          }
        });
      } else if (event.note.errorMessage.isNotEmpty) {
        Get.snackbar(AppLocalizations.instance.message, event.note.errorMessage);
      }

      setState(() {
        final index = _notes.indexWhere((note) => note.id == event.note.id);
        if (index != -1) {
          _notes[index] = event.note;
        } else {
          // 只有根笔记才添加到时间线列表中
          if (event.note.parentNoteId == null || event.note.parentNoteId == 0) {
            _notes.insert(0, event.note);
          }
          // 如果是子笔记，不添加到时间线列表中
        }
      });
    });

    // 监听笔记收藏状态变更事件
    _noteFavoriteChangedSubscription = NoteEvents.eventBus.on<NoteFavoriteChangedEvent>().listen((event) {
      setState(() {
        final index = _notes.indexWhere((note) => note.id == event.note.id);
        if (index != -1) {
          _notes[index] = event.note;
        }
      });
    });

    // 应用启动时重置卡住的更新状态
    _resetStuckUpdatingNotes();
  }

  void exitMultiSelectMode() {
    if (mounted) {
      setState(() {
        _isMultiSelectMode = false;
        _selectedNotes.clear();
      });
      widget.onExitMultiSelectMode();
    }
  }

  Future<void> deleteSelectedNotes() async {
    try {
      List<int> notesToDelete = List.from(_selectedNotes);
      for (int noteId in notesToDelete) {
        await _databaseHelper.deleteNote(noteId);
        _notes.removeWhere((note) => note.id == noteId);
      }
      setState(() {
        _selectedNotes.clear();
        _isMultiSelectMode = false;
      });
      widget.onExitMultiSelectMode();
      await _loadNotes(); // 重新加载笔记列表
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.selectedNoteDeleted);
    } catch (e) {
      Get.snackbar(AppLocalizations.instance.message, '${AppLocalizations.instance.deleteFailed}: ${e.toString()}');
    }
  }

  Future<void> _onScroll() async {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      await _loadNotes();
    }
  }

  Future<void> _loadNotes() async {
    if (!_isLoading && _hasMore) {
      setState(() {
        _isLoading = true;
      });

      // 只加载根笔记（没有父笔记的笔记）
      final notes = await _databaseHelper.getRootNotes(offset: _currentPage * _pageSize, limit: _pageSize);
      final totalCount = await _databaseHelper.getRootNotesCount();

      // 检查并修复可能有问题的笔记状态
      final fixedNotes = <Note>[];
      for (final note in notes) {
        Note fixedNote = note;

        // 检查是否有显示"正在等待AI响应"但实际应该显示重试按钮的笔记
        if (note.status != 0 && !note.isUpdating) {
          // 状态不是成功但也不在更新中，检查标题是否还显示为"正在等待"
          final statusText = _getStatusTextForNote(note);
          if (statusText.contains('正在获取') || statusText.contains('gettingData')) {
            print('修复有问题的笔记状态: ${note.title} (ID: ${note.id})');
            fixedNote = note.copyWith(
              status: Note.STATUS_NETWORK_ERROR,
              errorMessage: '连接已断开，请重试',
              isUpdating: false,
            );
            // 更新数据库
            await _databaseHelper.updateNote(fixedNote);
          }
        }

        fixedNotes.add(fixedNote);
      }

      setState(() {
        if (_currentPage == 0) {
          _notes = fixedNotes;
        } else {
          _notes.addAll(fixedNotes);
        }
        _currentPage++;
        _isLoading = false;
        _hasMore = _notes.length < totalCount;
      });
    }
  }

  // 获取笔记状态文本的辅助方法
  String _getStatusTextForNote(Note note) {
    if (note.isUpdating) {
      return AppLocalizations.instance.gettingData;
    }

    switch (note.status) {
      case 0:
        return note.title;
      default:
        return AppLocalizations.instance.gettingData; // 这里可能是问题所在
    }
  }

  Future<void> _refreshNotes() async {
    if (_hasProcessingNotes()) {
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.waitingForAIResponse);
      return;
    }
    setState(() {
      _currentPage = 0;
      _hasMore = true;
    });
    await _loadNotes();
  }

  void _toggleNoteSelection(int noteId) {
    // 找到对应的笔记
    final note = _notes.firstWhere((note) => note.id == noteId);

    // 如果笔记被标记为永不删除，则不允许选中
    if (note.neverDelete) {
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.canDeleteFavNote);
      return;
    }

    setState(() {
      if (_selectedNotes.contains(noteId)) {
        _selectedNotes.remove(noteId);
        if (_selectedNotes.isEmpty) {
          _isMultiSelectMode = false;
          widget.onExitMultiSelectMode();
        } else {
          widget.onSelectedCountChanged(_selectedNotes.length);
        }
      } else {
        _selectedNotes.add(noteId);
        if (!_isMultiSelectMode) {
          _isMultiSelectMode = true;
          widget.onEnterMultiSelectMode(_selectedNotes.length);
        } else {
          widget.onSelectedCountChanged(_selectedNotes.length);
        }
      }
    });
  }

  Future<void> _deleteExpiredNotes() async {
    await _databaseHelper.deleteExpiredNotes(_appSettings.defaultDeleteDays);
  }

  Future<void> _toggleNeverDelete(Note note) async {
    // 先更新数据库
    final updatedNote = note.copyWith(
      neverDelete: !note.neverDelete,
      updatedAt: DateTime.now(),
    );
    await _databaseHelper.updateNote(updatedNote);

    // 先刷新当前页面
    await _refreshNotes();

    // 通知其他页面更新 - 不需要显式触发ContributionGraph刷新，
    // 因为我们现在在数据库的updateNote方法中已经触发了FavoritedStatsChangedEvent
    NoteEvents.eventBus.fire(NoteFavoriteChangedEvent(updatedNote));
    NoteEvents.eventBus.fire(const RefreshProfileEvent());
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: EdgeInsets.all(16.0),
      alignment: Alignment.center,
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildEndOfListIndicator() {
    return Container(
      padding: EdgeInsets.all(16.0),
      alignment: Alignment.center,
      child: Column(children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(child: Divider()),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(AppLocalizations.instance.timelineTitle, style: TextStyle(color: Colors.grey)),
            ),
            Expanded(child: Divider()),
          ],
        ),
        Padding(
          padding: EdgeInsets.fromLTRB(0, 16, 0, 0),
          child: Text(
            "${AppLocalizations.instance.clickPlusButtonToInputText}\n${AppLocalizations.instance.longPressPlusButtonToPasteClipboardText}",
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Padding(
          padding: EdgeInsets.fromLTRB(0, 6, 0, 48),
          child: Icon(
            Icons.arrow_downward,
            color: Theme.of(context).colorScheme.primary,
            size: 36,
          ),
        ),
      ]),
    );
  }

  Future<void> _openNoteDetail(Note note) async {
    dynamic result;

    result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoteDetailPage(
          note: note,
          onNoteCreated: () async {
            setState(() {
              _currentPage = 0;
              _hasMore = true;
              _notes.clear();
            });
            await _loadNotes();

            if (_scrollController.hasClients) {
              _scrollController.animateTo(
                0,
                duration: Duration(milliseconds: 300),
                curve: Curves.easeOut,
              );
            }
          },
        ),
      ),
    );

    // 从笔记详情页返回后，检查笔记是否现在有子笔记
    // 如果有子笔记，应该导航到层级页面而不是停留在时间线
    final hasChildrenNow = await _databaseHelper.hasChildNotes(note.id!);
    if (hasChildrenNow) {
      // 如果笔记现在有子笔记，导航到层级页面
      await _openNoteHierarchy(note);
      return; // 不执行下面的代码
    }

    // 从笔记详情页返回后，检查并处理通知队列
    // 如果队列中有多个笔记，只保留最新的一个
    if (_pendingSuccessNotes.length > 1) {
      final latestNoteId = _pendingSuccessNotes.last;
      setState(() {
        _pendingSuccessNotes.clear();
        _pendingSuccessNotes.add(latestNoteId);
      });
    }

    // 如果当前没有显示通知但队列不为空，显示通知
    if (!_isShowingPopup && _pendingSuccessNotes.isNotEmpty) {
      _showNextSuccessPopup();
    }

    if (result is Map<String, dynamic>) {
      switch (result['action']) {
        case 'retry':
          final noteStillExists = await _databaseHelper.getNoteById(note.id!);
          if (noteStillExists != null) {
            _retryAIRequest(note);
          }
          break;
        case 'delete':
          setState(() {
            _notes.removeWhere((n) => n.id == note.id);
          });
          break;
      }
    }
  }

  Future<void> _openNoteHierarchy(Note note) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoteHierarchyPage(parentNote: note),
      ),
    );

    // 如果有变化，刷新列表
    if (result == true) {
      setState(() {
        _currentPage = 0;
        _hasMore = true;
        _notes.clear();
      });
      await _loadNotes();
    }
  }

  Future<void> _handleNoteTap(Note note) async {
    // 检查笔记是否有子笔记
    final hasChildren = await _databaseHelper.hasChildNotes(note.id!);

    if (hasChildren) {
      // 如果有子笔记，导航到层级页面
      await _openNoteHierarchy(note);
    } else {
      // 如果没有子笔记，直接打开笔记详情
      await _openNoteDetail(note);
    }
  }

  void _retryAIRequest(Note note) async {
    // 防止重复点击
    if (note.isUpdating) {
      Get.snackbar(AppLocalizations.instance.message, '正在处理中，请稍候...');
      return;
    }

    // 先检查登录状态
    final token = await AuthService().getToken();
    if (token == null) {
      Get.toNamed(LoginPage.routeName);
      return;
    }

    try {
      setState(() {
        final updatedNote = note.copyWith(
          isUpdating: true,
          status: 1,
          errorMessage: '',
          retryCount: note.retryCount + 1,
          updatedAt: DateTime.now(),
        );
        final index = _notes.indexWhere((n) => n.id == note.id);
        if (index != -1) {
          _notes[index] = updatedNote;
        }
      });

      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.retryingAIResponse);

      await _aiService.askAI(
        note,
        noUpdateContent: note.source == "1",
        proMode: note.isProMode,
      );
    } catch (e) {
      // 如果重试失败，恢复错误状态
      setState(() {
        final errorNote = note.copyWith(
          isUpdating: false,
          status: Note.STATUS_NETWORK_ERROR,
          errorMessage: '重试失败: ${e.toString()}',
          updatedAt: DateTime.now(),
        );
        final index = _notes.indexWhere((n) => n.id == note.id);
        if (index != -1) {
          _notes[index] = errorNote;
        }
      });

      Get.snackbar(AppLocalizations.instance.message, '重试失败，请检查网络连接');
    }
  }

  Widget _buildMultiSelectBar() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      height: _isMultiSelectMode ? 56.0 : 0,
      child: _isMultiSelectMode
          ? Container(
              color: Theme.of(context).primaryColor.withOpacity(0.8),
              child: SafeArea(
                child: Row(
                  children: [
                    IconButton(
                      icon: Icon(FontAwesomeIcons.xmark, color: Colors.white),
                      onPressed: exitMultiSelectMode,
                    ),
                    Text(
                      '已选择 ${_selectedNotes.length} 项',
                      style: TextStyle(color: Colors.white),
                    ),
                    Spacer(),
                    IconButton(
                      icon: Icon(FontAwesomeIcons.trash, color: Colors.white),
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: Text(AppLocalizations.instance.deleteConfirm),
                            content: Text(AppLocalizations.instance.confirmDeleteSelectedNotes(_selectedNotes.length)),
                            actions: [
                              TextButton(
                                child: Text(AppLocalizations.instance.cancel),
                                onPressed: () => Navigator.pop(context),
                              ),
                              TextButton(
                                child: Text(AppLocalizations.instance.delete),
                                onPressed: () {
                                  Navigator.pop(context);
                                  deleteSelectedNotes();
                                },
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  // 修改构建笔记卡片的方法，移除纸屑动画
  Widget _buildNoteCard(Note note) {
    return NoteCard(
      note: note,
      isSelected: _selectedNotes.contains(note.id),
      layout: _currentLayout,
      onTap: () {
        if (_isMultiSelectMode) {
          _toggleNoteSelection(note.id!);
        } else if (note.status == 0) {
          _handleNoteTap(note);
        } else {
          Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.waitingForAIResponse);
        }
      },
      onLongPress: !note.isUpdating
          ? () => _toggleNoteSelection(note.id!)
          : () => Get.snackbar(AppLocalizations.instance.message, '处理中的笔记不能被选择'),
      onToggleNeverDelete: _toggleNeverDelete,
      onRetryAI: _retryAIRequest,
      onStateReset: _resetNoteState, // 新增：状态重置回调
      onCancelAI: () {
        if (note.id != null) {
          _aiService.cancelRequest(note.id!);
          setState(() {
            final index = _notes.indexWhere((n) => n.id == note.id);
            if (index != -1) {
              final updatedNote = note.copyWith(
                isUpdating: false,
                status: -4,
                errorMessage: '用户取消了请求',
                updatedAt: DateTime.now(),
              );
              _notes[index] = updatedNote;
              NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));
            }
          });
          Get.snackbar(AppLocalizations.instance.message, '已停止处理');
        }
      },
      child: CountdownProgressBar(
        note: note,
        defaultDeleteDays: _appSettings.defaultDeleteDays,
      ),
    );
  }

  // 重置笔记状态
  void _resetNoteState(Note resetNote) async {
    setState(() {
      final index = _notes.indexWhere((n) => n.id == resetNote.id);
      if (index != -1) {
        _notes[index] = resetNote;
      }
    });

    // 更新数据库
    await _databaseHelper.updateNote(resetNote);

    // 发送事件通知
    NoteEvents.eventBus.fire(NoteUpdatedEvent(resetNote, false));
  }

  // 重置卡住的更新状态
  Future<void> _resetStuckUpdatingNotes() async {
    try {
      print('开始检查卡住的更新状态...');

      // 获取所有可能卡住的笔记（更新时间超过5分钟的 isUpdating 笔记）
      final stuckNotes = await _databaseHelper.getStuckUpdatingNotes();

      print('找到 ${stuckNotes.length} 个可能卡住的笔记');

      if (stuckNotes.isNotEmpty) {
        // 批量重置状态
        for (final note in stuckNotes) {
          print('重置笔记: ${note.title} (ID: ${note.id})');

          final resetNote = note.copyWith(
            isUpdating: false,
            status: Note.STATUS_NETWORK_ERROR,
            errorMessage: '连接已断开，请重试',
            updatedAt: DateTime.now(),
          );

          await _databaseHelper.updateNote(resetNote);
        }

        print('重置完成，重新加载笔记列表...');
        // 重新加载笔记列表
        _refreshNotes();
      }
    } catch (e) {
      // 如果重置失败，记录错误但不影响应用启动
      print('重置卡住的更新状态时出错: $e');
    }
  }

  void scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  bool _hasProcessingNotes() {
    return _notes.any((note) => note.isUpdating);
  }

  void _showAddButtonHint() {
    FeatureHint.show(
      context,
      HintConfig(
        id: _addButtonHintId,
        title: AppLocalizations.instance.addButtonHintTitle,
        content: AppLocalizations.instance.addButtonHintContent,
        icon: Icons.add_circle_outline,
        duration: const Duration(seconds: 8),
        showDoNotRemind: true,
      ),
    );
  }

  // 添加布局切换方法
  void _toggleLayout() {
    final newLayout = _currentLayout == TimelineLayout.single ? TimelineLayout.double : TimelineLayout.single;

    setState(() {
      _currentLayout = newLayout;
    });

    // 使用 _appSettings 替代 Provider
    _appSettings.setTimelineLayout(newLayout);
  }

  // 获取当前剩余AI余额
  Future<void> _loadRemainingQuota() async {
    final authService = AuthService();
    final quota = await authService.getRemainingQuota();
    setState(() {
      _remainingQuota = quota;
    });
  }

  // 添加一个新方法来显示下一个通知
  void _showNextSuccessPopup() {
    if (_pendingSuccessNotes.isEmpty) {
      setState(() {
        _isShowingPopup = false;
        _showQuotaPopup = false;
        _confettiController.stop();
      });
      return;
    }

    setState(() {
      _isShowingPopup = true;
      _showQuotaPopup = true;

      // 播放纸屑动画
      _confettiController.play();
    });
  }

  // 修改build方法，将纸屑动画和游戏风格提示放在页面顶层
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        title: Text(
          AppLocalizations.instance.timelineAppBarTitle,
          style: TextStyle(
            color: Theme.of(context).textTheme.titleLarge?.color,
          ),
        ),
        leading: IconButton(
          icon: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return RotationTransition(
                turns: animation,
                child: ScaleTransition(
                  scale: animation,
                  child: child,
                ),
              );
            },
            child: Icon(
              _currentLayout == TimelineLayout.single ? Icons.grid_view : Icons.view_agenda,
              key: ValueKey(_currentLayout),
              color: Theme.of(context).textTheme.titleLarge?.color,
            ),
          ),
          onPressed: _toggleLayout,
          tooltip: _currentLayout == TimelineLayout.single
              ? AppLocalizations.instance.switchToGridView
              : AppLocalizations.instance.switchToListView,
        ),
        actions: [
          IconButton(
            icon: FaIcon(FontAwesomeIcons.magnifyingGlass),
            color: Theme.of(context).textTheme.titleLarge?.color,
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SearchPage(),
                ),
              );
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: Stack(
                  children: [
                    RefreshIndicator(
                      onRefresh: _refreshNotes,
                      child: Container(
                        color: Colors.grey[50],
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return SingleChildScrollView(
                              physics: AlwaysScrollableScrollPhysics(),
                              controller: _scrollController,
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  minHeight: constraints.maxHeight + 1,
                                ),
                                child: CustomScrollView(
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  slivers: [
                                    _currentLayout == TimelineLayout.single
                                        ? SliverList(
                                            delegate: SliverChildBuilderDelegate(
                                              (context, index) {
                                                if (index < _notes.length) {
                                                  return _buildNoteCard(_notes[index]);
                                                } else if (_hasMore) {
                                                  return _buildLoadingIndicator();
                                                }
                                                return null; // 返回 null，不再在这里构建底部提示
                                              },
                                              childCount: _notes.length + (_hasMore ? 1 : 0), // 调整 childCount
                                            ),
                                          )
                                        : SliverToBoxAdapter(
                                            child: MasonryGridView.count(
                                              crossAxisCount: 2,
                                              mainAxisSpacing: 1,
                                              crossAxisSpacing: 1,
                                              shrinkWrap: true,
                                              physics: NeverScrollableScrollPhysics(),
                                              itemBuilder: (context, index) {
                                                if (index < _notes.length) {
                                                  return _buildNoteCard(_notes[index]);
                                                } else if (_hasMore) {
                                                  return _buildLoadingIndicator();
                                                }
                                                return Container(); // 不再在这里构建底部提示
                                              },
                                              itemCount: _notes.length + (_hasMore ? 1 : 0), // 调整 itemCount
                                            ),
                                          ),
                                    // 添加一个新的 SliverToBoxAdapter 来显示底部提示
                                    if (!_hasMore) // 只在没有更多数据时显示底部提示
                                      SliverToBoxAdapter(
                                        child: _buildEndOfListIndicator(),
                                      ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: _buildMultiSelectBar(),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // 在顶层添加纸屑效果
          Align(
            alignment: Alignment.center,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirectionality: BlastDirectionality.explosive,
              maxBlastForce: 10,
              minBlastForce: 5,
              emissionFrequency: 0.05,
              numberOfParticles: 50,
              gravity: 0.2,
              shouldLoop: false,
              colors: const [
                Colors.blue,
                Colors.pink,
                Colors.yellow,
                Colors.purple,
                Colors.green,
                Colors.orange,
              ],
            ),
          ),

          // 修改游戏风格提示的显示和关闭逻辑
          if (_showQuotaPopup)
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.3), // 添加半透明遮罩
                child: Center(
                  child: GameStyleQuotaPopup(
                    remainingQuota: _remainingQuota,
                    defaultDeleteDays: _appSettings.defaultDeleteDays,
                    onDismiss: () {
                      setState(() {
                        // 清空整个队列
                        _pendingSuccessNotes.clear();

                        // 直接关闭弹窗和动画
                        _isShowingPopup = false;
                        _showQuotaPopup = false;
                        _confettiController.stop();
                      });
                    },
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
