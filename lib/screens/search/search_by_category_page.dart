// Flutter imports:
import 'package:flutter/material.dart';

// Project imports:
import '../../constants/layout_config.dart';
import '../../helpers/database_helper.dart';
import '../../l10n/localization.dart';
import '../../models/note.dart';
import '../../models/segment.dart';
import '../../widgets/enhanced_note_card.dart';
import '../../services/app_settings.dart';
import '../../widgets/countdown_progress_bar.dart';
import '../note_detail/note_detail_page.dart';
import '../../services/ai_service.dart';
import 'package:get/get.dart';
import '../../services/event_bus.dart';

class SearchByCategoryPage extends StatefulWidget {
  final String searchCategory; // 新增：搜索分类

  const SearchByCategoryPage({
    super.key,
    required this.searchCategory,
  });

  @override
  State<SearchByCategoryPage> createState() => _SearchByCategoryPageState();
}

class _SearchByCategoryPageState extends State<SearchByCategoryPage> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Note> _searchResults = [];
  bool _isLoading = false;
  final AIService _aiService = AIService();

  @override
  void initState() {
    super.initState();
    _searchNotesByCategory();
  }

  // 根据分类搜索笔记
  Future<void> _searchNotesByCategory() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
    });

    try {
      // 从数据库中查询包含该分类的所有笔记
      final notes = await _databaseHelper.getNotesByCategory(widget.searchCategory);
      if (!mounted) return;
      setState(() {
        _searchResults = notes;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 打开笔记详情
  Future<void> _openNoteDetail(Note note) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoteDetailPage(
          note: note,
          onNoteCreated: () async {
            await _searchNotesByCategory();
          },
        ),
      ),
    );
  }

  Future<void> _generateNewQuestion() async {
    if (_searchResults.isEmpty) {
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.noRelatedNotesFound);
      return;
    }

    final localizations = AppLocalizations.instance;

    // 获取前10条笔记的标题
    final titles = _searchResults.take(10).map((note) => note.title).where((title) => title.isNotEmpty).toList();
    String titleList = titles.asMap().entries.map((e) => "${e.key + 1}. ${e.value}").join('.\n');
    final question = localizations.getInterestingKnowledge(widget.searchCategory, titleList);

    try {
      // 创建一个临时笔记
      List<StyledTextSegment> content = [StyledTextSegment(text: question)];

      Note note = Note(
        title: localizations.waitingForAIResponse,
        content: content,
        originalContent: content,
        prompt: AppLocalizations.instance.askPrompt(question),
        category: '',
        subCategory: '',
        subSubCategory: '',
        status: Note.STATUS_PENDING,
        retryCount: 0,
        errorMessage: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        source: Note.SOURCE_USER_ENTER,
      )..isUpdating = true;

      // 保存到数据库
      final noteId = await _databaseHelper.insertNote(note);
      note = note.copyWith(id: noteId);

      // 在后台请求AI响应
      _aiService.askAI(note, noUpdateContent: false);

      // 显示提示
      Get.snackbar(AppLocalizations.instance.message, localizations.createdNewNoteAndWaitingForAIResponse);

      // 发送创建事件
      NoteEvents.eventBus.fire(NoteCreatedEvent(note));
    } catch (e) {
      Get.snackbar(AppLocalizations.instance.message, '${localizations.createNoteFailed}: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    final appSettings = Get.find<AppSettings>();

    return Scaffold(
      appBar: AppBar(
        title: Text('${AppLocalizations.instance.search}: ${widget.searchCategory}'),
      ),
      body: Column(
        children: [
          // 添加"来点不一样的"按钮
          if (!_isLoading && _searchResults.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: FilledButton.icon(
                onPressed: _generateNewQuestion,
                icon: const Icon(Icons.auto_awesome),
                label: Text(AppLocalizations.instance.getDifferentKnowledge),
                style: FilledButton.styleFrom(
                  minimumSize: const Size.fromHeight(45),
                ),
              ),
            ),
          // 搜索结果列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _searchResults.isEmpty
                    ? Center(
                        child: Text(
                          AppLocalizations.instance.noRelatedNotesFound,
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                        ),
                      )
                    : ListView.builder(
                        itemCount: _searchResults.length,
                        itemBuilder: (context, index) {
                          final note = _searchResults[index];
                          return EnhancedNoteCard(
                            note: note,
                            isSelected: false,
                            onTap: () => _openNoteDetail(note),
                            onLongPress: () {},
                            onToggleNeverDelete: (_) {},
                            onRetryAI: (_) {},
                            onCancelAI: () {},
                            layout: TimelineLayout.single,
                            child: CountdownProgressBar(
                              note: note,
                              defaultDeleteDays: appSettings.defaultDeleteDays,
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }
}
