// Flutter imports:
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'dart:async';

// Project imports:
import '../../constants/layout_config.dart';
import '../../helpers/database_helper.dart';
import '../../l10n/localization.dart';
import '../../models/note.dart';
import '../../widgets/enhanced_note_card.dart';
import '../../services/app_settings.dart';
import '../../widgets/countdown_progress_bar.dart';
import '../note_detail/note_detail_page.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<String> _searchHistory = [];
  List<Note> _searchResults = [];
  bool _isSearching = false;
  Timer? _debounceTimer;
  static const String _searchHistoryKey = 'search_history';
  static const int _minSearchLength = 2; // 最小搜索长度

  bool _isTyping = false;
  Timer? _saveHistoryTimer;
  static const _saveHistoryDelay = Duration(seconds: 2); // 用户停止输入2秒后才保存

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
    _searchController.addListener(_onSearchChanged);
  }

  // 搜索防抖处理
  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();
    if (_saveHistoryTimer?.isActive ?? false) _saveHistoryTimer!.cancel();

    _isTyping = true;

    // 实时搜索的防抖
    _debounceTimer = Timer(const Duration(milliseconds: 100), () {
      _performSearch(_searchController.text);
    });

    // 保存搜索历史的延迟
    _saveHistoryTimer = Timer(_saveHistoryDelay, () {
      _isTyping = false;
      _maybeSaveSearchHistory(_searchController.text);
    });
  }

  // 加载搜索历史
  Future<void> _loadSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _searchHistory = prefs.getStringList(_searchHistoryKey) ?? [];
    });
  }

  // 保存搜索历史
  Future<void> _saveSearchHistory(String keyword) async {
    if (keyword.trim().isEmpty) return;

    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _searchHistory.remove(keyword);
      _searchHistory.insert(0, keyword);
      if (_searchHistory.length > 10) {
        _searchHistory = _searchHistory.sublist(0, 10);
      }
    });
    await prefs.setStringList(_searchHistoryKey, _searchHistory);
  }

  // 执行搜索
  Future<void> _performSearch(String keyword) async {
    if (keyword.trim().length < _minSearchLength) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final notes = await _databaseHelper.searchNotes(keyword);
      if (!mounted) return;

      setState(() {
        _searchResults = notes;
        _isSearching = false;
      });

      // 只在搜索词达到最小长度且有结果时保存
      if (notes.isNotEmpty && keyword.trim().length >= _minSearchLength) {
        await _saveSearchHistory(keyword);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isSearching = false;
      });
    }
  }

  Future<void> _openNoteDetail(Note note) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoteDetailPage(
          note: note,
          onNoteCreated: () async {
            await _performSearch(_searchController.text);
          },
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchController.text.isEmpty) {
      return _buildSearchHistory();
    }

    if (_isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.magnifyingGlass,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              '没有找到相关笔记',
              style: TextStyle(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
      );
    }

    final appSettings = Get.find<AppSettings>();
    return ListView.builder(
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final note = _searchResults[index];
        return EnhancedNoteCard(
          note: note,
          isSelected: false,
          onTap: () => _openNoteDetail(note),
          onLongPress: () {},
          onToggleNeverDelete: (_) {},
          onRetryAI: (_) {},
          onCancelAI: () {},
          layout: TimelineLayout.single,
          child: CountdownProgressBar(
            note: note,
            defaultDeleteDays: appSettings.defaultDeleteDays,
          ),
        );
      },
    );
  }

  Widget _buildSearchHistory() {
    if (_searchHistory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.magnifyingGlass,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无搜索记录',
              style: TextStyle(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            itemCount: _searchHistory.length,
            itemBuilder: (context, index) {
              return ListTile(
                leading: Icon(
                  FontAwesomeIcons.clockRotateLeft,
                  size: 20,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                title: Text(
                  _searchHistory[index],
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                onTap: () {
                  _searchController.text = _searchHistory[index];
                  _performSearch(_searchHistory[index]);
                },
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              );
            },
          ),
        ),
        // 添加清除历史记录按钮
        if (_searchHistory.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 0.5,
                ),
              ),
            ),
            child: TextButton.icon(
              onPressed: () async {
                // 显示确认对话框
                final bool? result = await showDialog<bool>(
                  context: context,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      title: Text(AppLocalizations.instance.clearConfirm),
                      content: Text(AppLocalizations.instance.clearSearchHistoryConfirm),
                      actions: <Widget>[
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: Text(AppLocalizations.instance.cancel),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          child: Text(AppLocalizations.instance.clearConfirm),
                        ),
                      ],
                    );
                  },
                );

                if (result == true) {
                  // 清除搜索历史
                  final prefs = await SharedPreferences.getInstance();
                  await prefs.remove(_searchHistoryKey);
                  setState(() {
                    _searchHistory.clear();
                  });
                }
              },
              icon: Icon(
                Icons.delete_outline,
                color: Theme.of(context).colorScheme.error,
              ),
              label: Text(
                AppLocalizations.instance.clearSearchHistory,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _maybeSaveSearchHistory(String keyword) async {
    // 如果用户还在输入，或关键词太短，或没有搜索结果，则不保存
    if (_isTyping || keyword.trim().length < 2 || _searchResults.isEmpty) {
      return;
    }

    await _saveSearchHistory(keyword);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        title: Column(
          children: [
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(right: 12),
                  child:
                      FaIcon(FontAwesomeIcons.magnifyingGlass, size: 20, color: Theme.of(context).colorScheme.primary),
                ),
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    autofocus: true,
                    style: Theme.of(context).textTheme.bodyLarge, // 设置输入文本的样式
                    decoration: InputDecoration(
                      hintText: AppLocalizations.instance.searchPlaceholder,
                      hintStyle: Theme.of(context).textTheme.bodyLarge, // 设置提示文本的样式
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 12), // 添加垂直内边距
                      fillColor: Colors.white,
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(Icons.clear, color: Theme.of(context).colorScheme.primary),
                              onPressed: () {
                                _searchController.clear();
                              },
                            )
                          : null,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(AppLocalizations.instance.cancel),
                ),
              ],
            ),
            Container(
              height: 8,
              color: Colors.grey.withAlpha(40),
            ),
          ],
        ),
      ),
      body: _buildSearchResults(),
    );
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _debounceTimer?.cancel();
    _saveHistoryTimer?.cancel();
    super.dispose();
  }
}
