import 'package:flutter/material.dart';

import '../../../l10n/localization.dart';

class ParentNotePanel extends StatelessWidget {
  final String noteTitle;
  final String? parentNoteTitle;
  final Animation<double> animation;
  final VoidCallback? onParentNoteTap;
  final DateTime createdAt;
  final bool isPro;
  final VoidCallback? onClose;

  const ParentNotePanel({
    super.key,
    required this.noteTitle,
    this.parentNoteTitle,
    required this.animation,
    this.onParentNoteTap,
    required this.createdAt,
    this.isPro = false,
    this.onClose,
  });

  String _getTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return AppLocalizations.instance.justNow;
    } else if (difference.inMinutes < 60) {
      return AppLocalizations.instance.minutesAgo(difference.inMinutes);
    } else if (difference.inHours < 24) {
      return AppLocalizations.instance.hoursAgo(difference.inHours);
    } else if (difference.inDays < 30) {
      return AppLocalizations.instance.daysAgo(difference.inDays);
    } else {
      return '${createdAt.year}-${createdAt.month}-${createdAt.day}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: FadeTransition(
        opacity: animation,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Theme.of(context).scaffoldBackgroundColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // 标题
                  Expanded(
                    child: Text(
                      noteTitle,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: 28,
                          ),
                      maxLines: 3,
                      softWrap: true,
                    ),
                  ),
                  SizedBox(width: 20),
                  // 右侧的关闭按钮、Pro 标签和时间戳
                  Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 关闭按钮
                      GestureDetector(
                        onTap: onClose,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            Icons.close,
                            size: 20,
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ),
                      // Pro 标签
                      Column(
                        children: [
                          if (isPro) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'Pro',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            SizedBox(height: 4),
                          ],
                          // 时间戳
                          Text(
                            _getTimeAgo(),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 10),
              if (parentNoteTitle != null && onParentNoteTap != null) ...[
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  height: 0.5,
                  color: Colors.grey.withOpacity(0.3),
                ),
                GestureDetector(
                  onTap: onParentNoteTap,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.link,
                          size: 14,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          parentNoteTitle!,
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: 14,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
