import 'package:flutter/material.dart';
import 'package:tempognize/l10n/localization.dart';

class NoteCategoryPanel extends StatelessWidget {
  const NoteCategoryPanel({
    super.key,
    required this.categories,
    required this.onCategoryTap,
    required this.onClose,
    this.recommendations = const [],
    this.onRecommendationTap,
  });

  final List<String> categories;
  final Function(String) onCategoryTap;
  final VoidCallback onClose;
  final List<String> recommendations;
  final Future<dynamic> Function(String)? onRecommendationTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: SafeArea(
          top: false,
          child: Stack(
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (recommendations.isNotEmpty) ...[
                    _buildRecommendationsSection(context),
                    const Divider(height: 24),
                  ],
                  Text(
                    AppLocalizations.instance.noteCategory,
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 10),
                  ...List.generate(
                    categories.length,
                    (index) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: _buildCategoryItem(
                        context,
                        categories[index],
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
              Positioned(
                top: -14,
                right: 0,
                child: IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: onClose,
                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecommendationsSection(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Text(
            AppLocalizations.instance.recommendKnowledge,
            style: TextStyle(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        ...List.generate(
          recommendations.length,
          (index) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildRecommendationItem(
              context,
              recommendations[index],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendationItem(BuildContext context, String recommendation) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return GestureDetector(
      onTap: () => onRecommendationTap?.call(recommendation),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: primaryColor.withOpacity(0.08),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: primaryColor.withOpacity(0.15),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                recommendation,
                style: TextStyle(
                  color: theme.colorScheme.onSurface,
                  fontSize: 14,
                ),
              ),
            ),
            Icon(
              Icons.chevron_right,
              size: 18,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(BuildContext context, String category) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    // 计算缩进距离
    final leftPadding = 0.0;

    // 使用与推荐面板一致的样式
    final backgroundColor = primaryColor.withOpacity(0.08);
    final textColor = theme.colorScheme.onSurface;

    return Padding(
      padding: EdgeInsets.only(left: leftPadding),
      child: GestureDetector(
        onTap: () => onCategoryTap(category),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: primaryColor.withOpacity(0.15),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  category,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Icon(
                Icons.chevron_right,
                size: 18,
                color: textColor.withOpacity(0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
