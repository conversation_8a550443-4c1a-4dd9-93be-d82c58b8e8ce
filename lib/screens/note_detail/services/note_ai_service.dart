// Flutter imports:
import 'package:flutter/material.dart';

// Project imports:
import '../../../helpers/database_helper.dart';
import '../../../l10n/localization.dart';
import '../../../models/note.dart';
import '../../../models/segment.dart';
import '../../../services/enhanced_ai_service.dart';
import '../../../services/auth_service.dart';
import '../../../services/event_bus.dart';

import '../../../screens/auth/login_page.dart';
import 'package:get/get.dart';

/// 笔记 AI 服务类
/// 处理笔记详情页面中所有与 AI 相关的操作
class NoteAIService {
  final EnhancedAIService _aiService = EnhancedAIService.instance;
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;

  /// 检查登录状态并请求 AI
  /// 如果未登录，会跳转到登录页面
  Future<void> checkLoginAndAskAI(
    String prompt,
    String fullHighlightedText,
    StyledTextSegment segment,
    BuildContext context, {
    required Function(int newNoteId, String groupId) onNoteCreated,
    required Note parentNote,
  }) async {
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      Get.toNamed(LoginPage.routeName);
      return;
    }

    await askAIAndCreateNote(
      fullHighlightedText,
      prompt,
      segment,
      parentNote,
      onNoteCreated: onNoteCreated,
    );
  }

  /// 创建新笔记并在后台请求 AI 响应
  Future<int> askAIAndCreateNote(
    String highlightedText,
    String prompt,
    StyledTextSegment triggerSegment,
    Note parentNote, {
    required Function(int newNoteId, String groupId) onNoteCreated,
  }) async {
    final localizations = AppLocalizations.instance;

    // 获取 highlightGroupId
    final String? groupId = triggerSegment.highlightGroupId;

    // 获取所有具有相同 highlightGroupId 的文本
    String fullHighlightedText = parentNote.content
        .where((segment) => segment.highlightGroupId == groupId)
        .map((segment) => segment.text)
        .join('');

    // 如果没有收集到文本，就使用原始文本
    if (fullHighlightedText.isEmpty) {
      fullHighlightedText = highlightedText;
    }

    // 创建新笔记
    final newNote = Note(
      title: localizations.waitingForAIResponse,
      content: [StyledTextSegment(text: fullHighlightedText)],
      originalContent: [StyledTextSegment(text: fullHighlightedText)],
      category: parentNote.category,
      subCategory: parentNote.subCategory,
      subSubCategory: parentNote.subSubCategory,
      prompt: prompt,
      parentNoteId: parentNote.id,
      status: Note.STATUS_PENDING,
      retryCount: 0,
      errorMessage: '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      source: Note.SOURCE_HIGHTED_TEXT,
      isProMode: false,
      recommendations: [],
    )..isUpdating = true;

    // 保存新笔记到数据库
    final newNoteId = await _databaseHelper.insertNote(newNote);

    if (newNoteId != 0) {
      // 通知 UI 更新高亮文字的链接
      onNoteCreated(newNoteId, groupId ?? '');

      // 在后台请求 AI 响应
      try {
        await _aiService.askAI(newNote.copyWith(id: newNoteId), noUpdateContent: false);
        // Success is handled automatically by the enhanced service
      } catch (error) {
        // Error is automatically handled by ErrorHandlerService
        // The enhanced service will show appropriate error messages to the user
      }

      // 通知 UI 更新
      Get.snackbar(AppLocalizations.instance.message, localizations.waitingForAIResponse);
      NoteEvents.eventBus.fire(NoteCreatedEvent(newNote.copyWith(id: newNoteId)));
    }

    return newNoteId;
  }

  /// 基于高亮文字客户自己输入问题创建新笔记并在后台请求 AI 响应
  Future<int> askAIAndCreateNoteFromHighlightedText({
    required String highlightedText,
    required String thought,
    required StyledTextSegment triggerSegment,
    required Note parentNote,
    required Function(int newNoteId, String groupId) onNoteCreated,
  }) async {
    final localizations = AppLocalizations.instance;
    String prompt = AppLocalizations.instance.askAboutHighlightedTextPrompt(
      parentNote.originalContent.map((seg) => seg.text).join(''),
      highlightedText,
      thought,
    );

    // 获取 highlightGroupId
    final String? groupId = triggerSegment.highlightGroupId;

    // 获取所有具有相同 highlightGroupId 的文本
    String fullHighlightedText = parentNote.content
        .where((segment) => segment.highlightGroupId == groupId)
        .map((segment) => segment.text)
        .join('');

    // 如果没有收集到文本，就使用原始文本
    if (fullHighlightedText.isEmpty) {
      fullHighlightedText = highlightedText;
    }

    // 创建新笔记
    final newNote = Note(
      title: localizations.waitingForAIResponse,
      content: [StyledTextSegment(text: fullHighlightedText)],
      originalContent: [StyledTextSegment(text: fullHighlightedText)],
      category: parentNote.category,
      subCategory: parentNote.subCategory,
      subSubCategory: parentNote.subSubCategory,
      prompt: prompt,
      parentNoteId: parentNote.id,
      status: Note.STATUS_PENDING,
      retryCount: 0,
      errorMessage: '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      source: Note.SOURCE_HIGHTED_TEXT,
      isProMode: false,
      recommendations: [],
    )..isUpdating = true;

    // 保存新笔记到数据库
    final newNoteId = await _databaseHelper.insertNote(newNote);

    if (newNoteId != 0) {
      // 通知 UI 更新高亮文字的链接
      onNoteCreated(newNoteId, groupId ?? '');

      // 在后台请求 AI 响应
      try {
        await _aiService.askAI(newNote.copyWith(id: newNoteId), noUpdateContent: false);
        // Success is handled automatically by the enhanced service
      } catch (error) {
        // Error is automatically handled by ErrorHandlerService
        // The enhanced service will show appropriate error messages to the user
      }

      // 通知 UI 更新
      Get.snackbar(AppLocalizations.instance.message, localizations.waitingForAIResponse);
      NoteEvents.eventBus.fire(NoteCreatedEvent(newNote.copyWith(id: newNoteId)));
    }

    return newNoteId;
  }

  /// 基于全文提问题创建新笔记并在后台请求 AI 响应
  Future<int> askAIAndCreateNoteFromFullNote({
    required String thought,
    required Note parentNote,
  }) async {
    final localizations = AppLocalizations.instance;
    String prompt = AppLocalizations.instance.askAboutFullNotePrompt(
      parentNote.originalContent.map((seg) => seg.text).join(''),
      thought,
    );

    // 创建新笔记
    final newNote = Note(
      title: localizations.waitingForAIResponse,
      content: [StyledTextSegment(text: thought)],
      originalContent: [StyledTextSegment(text: thought)],
      category: parentNote.category,
      subCategory: parentNote.subCategory,
      subSubCategory: parentNote.subSubCategory,
      prompt: prompt,
      parentNoteId: parentNote.id,
      status: Note.STATUS_PENDING,
      retryCount: 0,
      errorMessage: '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      source: Note.SOURCE_USER_ENTER,
      isProMode: false,
      recommendations: [],
    )..isUpdating = true;

    // 保存新笔记到数据库
    final newNoteId = await _databaseHelper.insertNote(newNote);

    if (newNoteId != 0) {
      // 在后台请求 AI 响应
      try {
        await _aiService.askAI(newNote.copyWith(id: newNoteId), noUpdateContent: false);
        // Success is handled automatically by the enhanced service
      } catch (error) {
        // Error is automatically handled by ErrorHandlerService
        // The enhanced service will show appropriate error messages to the user
      }

      // 通知 UI 更新
      Get.snackbar(AppLocalizations.instance.message, localizations.waitingForAIResponse);
      NoteEvents.eventBus.fire(NoteCreatedEvent(newNote.copyWith(id: newNoteId)));
    }

    return newNoteId;
  }
}
