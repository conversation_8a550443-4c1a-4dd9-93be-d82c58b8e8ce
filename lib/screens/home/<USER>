// Flutter imports:
import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_sound_record/flutter_sound_record.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

// Project imports:
import '../../config.dart';
import '../../helpers/database_helper.dart';
import '../../l10n/localization.dart';
import '../../models/note.dart';

import '../../models/segment.dart';
import '../../screens/profile/profile_screen.dart';
import '../../services/ai_service.dart';
import '../../services/auth_service.dart';
import '../../services/event_bus.dart';

import '../../utils/markdown_util.dart';
import '../timeline/timeline_page.dart';
import '../../services/app_settings.dart';
import '../../core/services/dialog_queue_manager.dart';

class MainScreen extends StatefulWidget {
  static const routeName = '/';
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => MainScreenState();
}

class MainScreenState extends State<MainScreen> with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  final TextEditingController textController = TextEditingController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  Timer? _ampTimer;
  Amplitude? _amplitude;
  bool _isRecording = false;
  final AIService _aiService = AIService();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  DateTime? _lastTapTime;
  final GlobalKey<TimelinePageState> _timelineKey = GlobalKey<TimelinePageState>();
  final _recorder = FlutterSoundRecord();
  String? _recordingPath;
  DateTime? _recordStartTime;
  // 用户启动了Pro答案模式
  bool isProEnabled = false;

  // 添加一个变量来跟踪转写状态
  bool _isTranscribing = false;
  // 添加一个变量来存储当前的请求
  CancelToken? _cancelToken;

  late AnimationController _animationController;

  // 移到类级别
  DateTime? _pressStartTime;
  bool _isLongPress = false;

  // 添加新的状态变量
  bool _isOutsideFAB = false; // 跟踪手指是否在FAB区域外
  Offset? _fabCenter; // FAB中心点位置
  final double _fabRadius = 40.0; // FAB半径

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  // 检查录音权限（仅在需要时调用）
  Future<bool> _checkRecordingPermissionWhenNeeded() async {
    try {
      // 检查是否有其他对话框正在显示
      if (Get.isRegistered<DialogQueueManager>()) {
        final dialogManager = DialogQueueManager.instance;
        if (dialogManager.isDialogShowing) {
          // 如果有其他对话框正在显示，延迟权限请求
          Get.snackbar(AppLocalizations.instance.message, '请等待其他对话框关闭后再试');
          return false;
        }
      }

      final hasPermission = await _recorder.hasPermission();
      if (!hasPermission) {
        // 只有在用户实际需要录音时才请求权限
        final granted = await _recorder.hasPermission();
        if (!granted) {
          Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.noRecordingPermission);
          return false;
        }
      }
      return true;
    } catch (e) {
      developer.log('检查录音权限失败: $e');
      return false;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    textController.dispose();
    _recorder.dispose();
    _ampTimer?.cancel();
    super.dispose();
  }

  void _onItemTapped(int index) {
    if (index != 1) {
      if (index == 0) {
        // 检查是否是双击首页按钮
        final now = DateTime.now();
        if (_lastTapTime != null && now.difference(_lastTapTime!) <= const Duration(milliseconds: 500)) {
          // 双击，滚动到顶部
          _timelineKey.currentState?.scrollToTop();
          _lastTapTime = null;
        } else {
          // 单击，更新最后点击时间
          _lastTapTime = now;
        }
      }

      setState(() {
        _selectedIndex = index;
      });
      HapticFeedback.lightImpact();
    }
  }

  Future<int?> _createNoteFromClipboard() async {
    final localizations = AppLocalizations.instance;

    // 添加登录检查
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.pleaseLoginFirst);
      Get.toNamed('/login');
      return null;
    }

    // 添加轻微震动反馈
    HapticFeedback.lightImpact();
    // HapticFeedback.lightImpact();

    // 获取剪贴板数据
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData?.text?.isNotEmpty ?? false) {
      try {
        // 1. 创建一个临时笔记
        List<StyledTextSegment> content = MarkdownUtil.parseMarkdown('\n\n\n${clipboardData!.text}\n\n\n');

        Note note = Note(
          title: localizations.waitingForAITitle,
          content: content,
          originalContent: content,
          prompt: "${AppLocalizations.instance.generateTitleAndCategoriesPrompt}\n${clipboardData.text}",
          category: '',
          subCategory: '',
          subSubCategory: '',
          status: Note.STATUS_PENDING,
          retryCount: 0,
          errorMessage: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          source: Note.SOURCE_CLIPBOARD,
        )..isUpdating = true; // 设置isUpdating为true

        // 2. 保存到数据库
        final noteId = await _databaseHelper.insertNote(note);
        note = note.copyWith(id: noteId);

        // 3. 在后台请求AI响应
        _aiService.askAI(note, noUpdateContent: true);

        // 4. 显示提示
        Get.snackbar(AppLocalizations.instance.message, localizations.waitingForAITitle);

        // 5. 发送创建事件
        NoteEvents.eventBus.fire(NoteCreatedEvent(note));

        return noteId;
      } catch (e) {
        Get.snackbar(AppLocalizations.instance.message, '${localizations.createNoteFailed}: ${e.toString()}');
        return null;
      }
    } else {
      Get.snackbar(AppLocalizations.instance.message, localizations.clipboardIsEmpty);
      return null;
    }
  }

  Future<void> _createNoteFromQuestion(String question, {bool proMode = false}) async {
    final localizations = AppLocalizations.instance;

    // 添加登录检查
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.pleaseLoginFirst);
      Get.toNamed('/login');
      return;
    }

    try {
      // 1. 创建一个临时笔记
      List<StyledTextSegment> content = [StyledTextSegment(text: question)];

      Note note = Note(
        title: localizations.waitingForAIResponse,
        content: content,
        originalContent: content,
        prompt: proMode ? question : AppLocalizations.instance.askPrompt(question),
        category: '',
        subCategory: '',
        subSubCategory: '',
        status: Note.STATUS_PENDING,
        retryCount: 0,
        errorMessage: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        source: Note.SOURCE_USER_ENTER,
        isProMode: proMode,
        recommendations: [],
      )..isUpdating = true;

      // 2. 保存到数据库
      final noteId = await _databaseHelper.insertNote(note);
      note = note.copyWith(id: noteId);

      // 3. 在后台请求AI响应
      _aiService.askAI(note, noUpdateContent: false, proMode: proMode);

      // 4. 显示提示
      Get.snackbar(AppLocalizations.instance.message, localizations.createdNewNoteAndWaitingForAIResponse);

      // 5. 发送创建事件
      NoteEvents.eventBus.fire(NoteCreatedEvent(note));
    } catch (e) {
      developer.log('${localizations.createNoteFailed}: ${e.toString()}');
    }
  }

  Future<void> _startRecording(StateSetter setDialogState) async {
    // 添加登录检查
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.pleaseLoginFirst);
      Get.toNamed('/login');
      return;
    }

    // 检查录音权限（仅在需要时）
    if (!await _checkRecordingPermissionWhenNeeded()) {
      return;
    }

    try {
      // 确保录音器处于停止状态
      await _recorder.stop();

      final tempDir = await getTemporaryDirectory();
      _recordingPath = '${tempDir.path}/temp_audio.m4a';

      await _recorder.start(
        path: _recordingPath!,
        encoder: AudioEncoder.AAC,
        bitRate: 128000,
        samplingRate: 44100,
      );

      HapticFeedback.heavyImpact();
      _recordStartTime = DateTime.now();

      if (mounted) {
        _startAmplitudeTimer(setDialogState);
      }
    } catch (e) {
      _recordStartTime = null;
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.recordingFailed);
    }
  }

  void _startAmplitudeTimer(StateSetter setDialogState) {
    _ampTimer?.cancel();
    _ampTimer = Timer.periodic(const Duration(milliseconds: 100), (Timer t) async {
      try {
        if (!_isRecording) {
          _ampTimer?.cancel();
          return;
        }

        final amp = await _recorder.getAmplitude();

        if (_isRecording && mounted) {
          setState(() {
            _amplitude = amp;
          });
          setDialogState(() {
            _amplitude = amp;
          });
        }
      } catch (e) {
        _ampTimer?.cancel();
      }
    });
  }

  Future<void> _stopRecordingAndTranscribe(StateSetter setDialogState) async {
    developer.log('进入 _stopRecordingAndTranscribe - _isRecording: $_isRecording, _isTranscribing: $_isTranscribing');

    try {
      // 设置转写状态
      setState(() {
        _isTranscribing = true;
      });

      final file = File(_recordingPath!);
      if (!await file.exists()) {
        Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.recordingNotFound);
        return;
      }

      // 创建取消令牌
      _cancelToken = CancelToken();

      // 创建multipart请求
      final uri = Uri.parse('${AppConfig.apiBaseUrl}/audio/transcribe');
      var request = http.MultipartRequest('POST', uri);

      // 添加认证头
      final token = await AuthService().getToken();
      request.headers['Authorization'] = 'Bearer $token';
      final settings = Get.find<AppSettings>();
      final language = settings.locale.languageCode;
      request.fields['language'] = language;

      // 添加文件
      request.files.add(
        await http.MultipartFile.fromPath(
          'file',
          _recordingPath!,
          filename: 'audio.m4a',
        ),
      );

      // 发送请求
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.transcribing);
      final response = await request.send();
      final responseStr = await response.stream.bytesToString();

      if (_cancelToken?.isCancelled ?? false) {
        return;
      }

      if (response.statusCode == 200) {
        try {
          final Map<String, dynamic> jsonResponse = jsonDecode(responseStr);
          final String transcribedText = jsonResponse['text'] as String;

          if (mounted) {
            setState(() {
              textController.text = transcribedText;
              _isTranscribing = false; // 转写成功后重置状态
            });
            developer.log('转写成功，状态已重置 - _isTranscribing: $_isTranscribing');
          }
        } catch (e) {
          developer.log('JSON 解析失败: $e');
          Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.transcribeFailed);
        }
      } else {
        Get.snackbar(
            AppLocalizations.instance.message, '${AppLocalizations.instance.transcribeFailed}: ${response.statusCode}');
        developer.log('转写失败: ${response.statusCode}');
        developer.log('转写失败响应: $responseStr');
      }
    } catch (e) {
      developer.log('转写出错: $e');
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.transcribeFailed);
    } finally {
      // 确保在任何情况下都重置转写状态
      if (mounted) {
        setState(() {
          _isTranscribing = false;
        });
        developer.log('转写状态已重置 - _isTranscribing: $_isTranscribing');
      }
      _cancelToken = null;
    }
  }

  void _showInputPanel({bool autoStartRecording = false}) async {
    // 添加登录检查
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.pleaseLoginFirst);
      Get.toNamed('/login');
      return;
    }

    // 检查 widget 是否仍然 mounted
    if (!mounted) return;

    isProEnabled = false;

    final transcribingNotifier = ValueNotifier<bool>(_isTranscribing);
    final proModeNotifier = ValueNotifier<bool>(isProEnabled);
    final subscription = Stream.periodic(const Duration(milliseconds: 100)).listen((_) {
      if (transcribingNotifier.value != _isTranscribing) {
        transcribingNotifier.value = _isTranscribing;
      }
    });

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return ValueListenableBuilder<bool>(
          valueListenable: proModeNotifier,
          builder: (context, isProMode, child) {
            return ValueListenableBuilder<bool>(
              valueListenable: transcribingNotifier,
              builder: (context, isTranscribing, child) {
                return Stack(
                  children: [
                    Positioned(
                      top: MediaQuery.of(context).padding.top + 24,
                      child: AlertDialog(
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        surfaceTintColor: Colors.transparent,
                        contentPadding: EdgeInsets.zero,
                        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        content: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                          width: MediaQuery.of(context).size.width - 32,
                          height: MediaQuery.of(context).size.height * 0.4 + (isProMode ? 40.0 : 0.0),
                          child: Column(
                            children: [
                              // 标题栏
                              Padding(
                                padding: const EdgeInsets.all(16),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      AppLocalizations.instance.ask,
                                      style: Theme.of(context).textTheme.titleLarge,
                                    ),
                                    // 添加加载动画
                                    AnimatedOpacity(
                                      opacity: isTranscribing ? 1.0 : 0.0,
                                      duration: const Duration(milliseconds: 200),
                                      child: SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        Clipboard.getData(Clipboard.kTextPlain).then((value) {
                                          if (value?.text?.isNotEmpty ?? false) {
                                            textController.text = value!.text!;
                                          } else {
                                            Get.snackbar(AppLocalizations.instance.message,
                                                AppLocalizations.instance.clipboardIsEmpty);
                                          }
                                        });
                                      },
                                      icon: const Icon(FontAwesomeIcons.paste),
                                    ),
                                  ],
                                ),
                              ),

                              const Divider(height: 1),

                              Expanded(
                                child: Column(
                                  children: [
                                    // 文本输入区域
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: TextFormField(
                                          controller: textController,
                                          decoration: InputDecoration(
                                            hintText: isTranscribing
                                                ? AppLocalizations.instance.transcribing
                                                : AppLocalizations.instance.askPlaceholder,
                                            border: OutlineInputBorder(
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                            isCollapsed: true,
                                          ),
                                          maxLines: null,
                                          expands: true,
                                          textAlignVertical: TextAlignVertical.top,
                                          autofocus: false,
                                          showCursor: true,
                                          readOnly: isTranscribing,
                                          enabled: !isTranscribing,
                                        ),
                                      ),
                                    ),

                                    // 底部操作栏
                                    Container(
                                      padding: const EdgeInsets.fromLTRB(8, 8, 16, 8),
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(
                                            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                                          ),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          // 左侧按钮组
                                          Row(
                                            children: [
                                              IconButton(
                                                onPressed: () async {
                                                  final confirm = await showDialog<bool>(
                                                    context: context,
                                                    builder: (BuildContext dialogContext) {
                                                      return AlertDialog(
                                                        title: Text(AppLocalizations.instance.pasteFromClipboard),
                                                        content:
                                                            Text(AppLocalizations.instance.pasteFromClipboardQuestion),
                                                        actions: <Widget>[
                                                          TextButton(
                                                            child: Text(AppLocalizations.instance.cancel),
                                                            onPressed: () {
                                                              Navigator.of(dialogContext).pop(false);
                                                            },
                                                          ),
                                                          TextButton(
                                                            child: Text(AppLocalizations.instance.confirm),
                                                            onPressed: () {
                                                              Navigator.of(dialogContext).pop(true);
                                                            },
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                  );
                                                  if (confirm ?? false) {
                                                    _createNoteFromClipboard();
                                                    Get.back();
                                                  }
                                                },
                                                icon: Row(children: [
                                                  const Icon(FontAwesomeIcons.clipboard),
                                                  const Icon(FontAwesomeIcons.arrowRight, size: 12),
                                                  const Icon(FontAwesomeIcons.fileLines),
                                                ]),
                                              ),
                                              // "Pro"开关组件
                                              StatefulBuilder(
                                                builder: (BuildContext context, StateSetter setDialogState) {
                                                  return Row(
                                                    children: [
                                                      Switch(
                                                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                                        activeTrackColor: Theme.of(context).colorScheme.primary,
                                                        value: isProMode,
                                                        onChanged: (value) {
                                                          setDialogState(() {
                                                            isProEnabled = value;
                                                          });
                                                          setState(() {
                                                            isProEnabled = value;
                                                          });
                                                          proModeNotifier.value = value;
                                                        },
                                                      ),
                                                      Icon(
                                                        Icons.star,
                                                        size: 12,
                                                        color: isProMode
                                                            ? Theme.of(context).colorScheme.primary
                                                            : Theme.of(context)
                                                                .colorScheme
                                                                .onSurfaceVariant
                                                                .withOpacity(0.6),
                                                      ),
                                                      Text(
                                                        'Pro',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontWeight: FontWeight.bold,
                                                          color: isProMode
                                                              ? Theme.of(context).colorScheme.primary
                                                              : Theme.of(context)
                                                                  .colorScheme
                                                                  .onSurfaceVariant
                                                                  .withOpacity(0.6),
                                                        ),
                                                      ),
                                                    ],
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                          // 右侧按钮组
                                          FilledButton(
                                            onPressed: () async {
                                              if (isTranscribing) {
                                                _cancelToken?.cancel();
                                                Get.snackbar(AppLocalizations.instance.message,
                                                    AppLocalizations.instance.transcribeCancelled);
                                                if (mounted) {
                                                  setState(() {
                                                    _isTranscribing = false;
                                                  });
                                                }
                                                transcribingNotifier.value = false;
                                              } else if (textController.text.trim().isNotEmpty) {
                                                Navigator.pop(context);
                                                await _createNoteFromQuestion(textController.text.trim(),
                                                    proMode: isProMode);
                                              }
                                            },
                                            child: Text(isTranscribing
                                                ? AppLocalizations.instance.cancel
                                                : AppLocalizations.instance.confirm),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Pro模式抽屉
                              AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                                height: isProMode ? 56.0 : 0.0,
                                child: AnimatedOpacity(
                                  duration: const Duration(milliseconds: 200),
                                  opacity: isProMode ? 1.0 : 0.0,
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                                      borderRadius: const BorderRadius.only(
                                        bottomLeft: Radius.circular(20),
                                        bottomRight: Radius.circular(20),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          FontAwesomeIcons.wandMagicSparkles,
                                          size: 14,
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            AppLocalizations.instance.proModeDescription,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Theme.of(context).colorScheme.primary,
                                            ),
                                            softWrap: true,
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    ).then((_) {
      subscription.cancel();
      proModeNotifier.dispose();
      textController.clear();
    });
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
  }) {
    final theme = Theme.of(context);
    final isSelected = _selectedIndex == index;

    return InkWell(
      onTap: () => _onItemTapped(index),
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 28,
              color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaveform() {
    // 计算容器宽度
    final double maxWidth = MediaQuery.of(context).size.width - 64;
    final double minWidth = maxWidth * 0.2;

    // 音量值映射优化
    final double minDb = -160.0;
    final double maxDb = 0.0;
    final double currentDb = (_amplitude?.current ?? minDb).clamp(minDb, maxDb);
    final double normalizedAmp = (currentDb - minDb) / (maxDb - minDb);
    final double normalizedWidth = minWidth + (maxWidth - minWidth) * normalizedAmp;

    // 根据是否在FAB区域外决定使用的颜色
    final Color primaryColor =
        _isOutsideFAB ? Theme.of(context).colorScheme.error : Theme.of(context).colorScheme.secondary;
    final Color containerColor =
        _isOutsideFAB ? Theme.of(context).colorScheme.errorContainer : Theme.of(context).colorScheme.secondaryContainer;

    return Container(
      height: 180,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const SizedBox(height: 8),
            Icon(
              FontAwesomeIcons.microphone,
              color: primaryColor,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.instance.recording,
              style: TextStyle(
                color: primaryColor,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 24,
              constraints: BoxConstraints(
                maxWidth: maxWidth,
                minWidth: minWidth,
              ),
              child: Center(
                child: Container(
                  height: 24,
                  width: normalizedWidth.clamp(minWidth, maxWidth),
                  decoration: BoxDecoration(
                    color: containerColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Container(
                      height: 2,
                      width: (normalizedWidth * 0.8).clamp(minWidth * 0.8, maxWidth * 0.8),
                      decoration: BoxDecoration(
                        color: primaryColor,
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${_amplitude?.current.toStringAsFixed(1) ?? "0.0"} dB',
              style: TextStyle(
                color: primaryColor,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _isOutsideFAB ? AppLocalizations.instance.releaseToCancel : AppLocalizations.instance.releaseToStop,
              style: TextStyle(
                color: primaryColor,
                fontSize: 9,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 添加判断点是否在FAB区域内的方法
  bool _isPointInsideFAB(Offset point) {
    if (_fabCenter == null) return true;
    return (point - _fabCenter!).distance <= _fabRadius;
  }

  Widget _buildFAB() {
    return SizedBox(
      height: 80,
      width: 80,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Listener(
            onPointerDown: (PointerDownEvent event) async {
              // 获取FAB中心点位置（在异步操作之前）
              final RenderBox renderBox = context.findRenderObject() as RenderBox;
              _fabCenter = renderBox.localToGlobal(
                Offset(constraints.maxWidth / 2, constraints.maxHeight / 2),
              );

              // 检查登录状态
              final isLoggedIn = await AuthService().isLoggedIn();
              if (!isLoggedIn) {
                Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.pleaseLoginFirst);
                Get.toNamed('/login');
                return;
              }

              // 检查 widget 是否仍然 mounted
              if (!mounted) return;

              _pressStartTime = DateTime.now();
              _isOutsideFAB = false;

              // 延迟判断长按
              await Future.delayed(const Duration(milliseconds: 200));
              if (_pressStartTime != null && mounted) {
                _isLongPress = true;
                await _startRecording(setState);
                if (mounted) {
                  setState(() {
                    _isRecording = true;
                  });
                }
              }
            },
            onPointerMove: (PointerMoveEvent event) {
              if (_isRecording) {
                final bool wasOutside = _isOutsideFAB;
                _isOutsideFAB = !_isPointInsideFAB(event.position);

                // 只有当状态发生变化时才更新UI
                if (wasOutside != _isOutsideFAB) {
                  setState(() {});
                }
              }
            },
            onPointerUp: (PointerUpEvent event) async {
              if (_pressStartTime == null) return;

              final pressDuration = DateTime.now().difference(_pressStartTime!);
              _pressStartTime = null;

              if (_isLongPress) {
                _isLongPress = false;
                if (_isRecording) {
                  await _recorder.stop();
                  setState(() {
                    _isRecording = false;
                    _isOutsideFAB = false;
                  });

                  // 检查是否在FAB区域外松开
                  if (!_isPointInsideFAB(event.position)) {
                    // 删除临时录音文件
                    if (_recordingPath != null) {
                      try {
                        final file = File(_recordingPath!);
                        if (await file.exists()) {
                          await file.delete();
                        }
                      } catch (e) {
                        developer.log('删除临时录音文件失败: $e');
                      }
                    }
                    Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.recordingCancelled);
                    return;
                  }

                  // 正常流程：检查录音时长
                  if (_recordStartTime != null) {
                    final duration = DateTime.now().difference(_recordStartTime!);
                    if (duration.inSeconds < 2) {
                      Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.recordingTooShort);
                      // 录音时间太短也需要删除临时文件
                      if (_recordingPath != null) {
                        try {
                          final file = File(_recordingPath!);
                          if (await file.exists()) {
                            await file.delete();
                          }
                        } catch (e) {
                          developer.log('删除临时录音文件失败: $e');
                        }
                      }
                      return;
                    }
                  }

                  _showInputPanel(autoStartRecording: false);
                  await _stopRecordingAndTranscribe(setState);
                }
              } else if (pressDuration.inMilliseconds < 200) {
                _showInputPanel(autoStartRecording: false);
              }
            },
            onPointerCancel: (PointerCancelEvent event) async {
              _pressStartTime = null;
              _isLongPress = false;
              _isOutsideFAB = false;
              if (_isRecording) {
                await _recorder.stop();
                setState(() {
                  _isRecording = false;
                });

                // 删除临时录音文件
                if (_recordingPath != null) {
                  try {
                    final file = File(_recordingPath!);
                    if (await file.exists()) {
                      await file.delete();
                    }
                  } catch (e) {
                    developer.log('删除临时录音文件失败: $e');
                  }
                }
                Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.recordingCancelled);
              }
            },
            child: FloatingActionButton(
              onPressed: null,
              elevation: 4.0,
              backgroundColor: _isOutsideFAB ? Colors.white : Theme.of(context).colorScheme.primary,
              shape: const CircleBorder(),
              child: Icon(
                _isOutsideFAB ? Icons.close : FontAwesomeIcons.plus,
                color: _isOutsideFAB ? Theme.of(context).colorScheme.error : Colors.white,
                size: 32,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRecordingOverlay() {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 200),
      // 计算屏幕中间位置
      top: _isRecording ? MediaQuery.of(context).size.height * 0.4 : MediaQuery.of(context).size.height,
      left: 20,
      right: 20,
      child: AnimatedOpacity(
        opacity: _isRecording ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: Card(
          elevation: 8.0, // 增加一点阴影让面板更突出
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: _buildWaveform(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
      ),
      child: Scaffold(
        key: _scaffoldKey,
        resizeToAvoidBottomInset: false,
        body: Stack(
          children: [
            IndexedStack(
              index: _selectedIndex,
              children: [
                SafeArea(
                  bottom: false,
                  child: TimelinePage(
                    key: _timelineKey,
                    onEnterMultiSelectMode: (count) {},
                    onExitMultiSelectMode: () {},
                    onSelectedCountChanged: (count) {},
                  ),
                ),
                const SizedBox(),
                const ProfileScreen(),
              ],
            ),
            if (_selectedIndex == 0)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: MediaQuery.of(context).padding.top + 20,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withOpacity(0.3),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
            if (_isRecording) _buildRecordingOverlay(),
          ],
        ),
        floatingActionButton: _buildFAB(),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: BottomAppBar(
            color: Colors.transparent,
            height: 75,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            shape: const CircularNotchedRectangle(),
            notchMargin: 8,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: <Widget>[
                _buildNavItem(
                  icon: FontAwesomeIcons.house,
                  label: AppLocalizations.instance.home,
                  index: 0,
                ),
                const SizedBox(width: 80),
                _buildNavItem(
                  icon: FontAwesomeIcons.user,
                  label: AppLocalizations.instance.profile,
                  index: 2,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
