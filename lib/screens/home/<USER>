// Dart imports:
import 'dart:async';
import 'dart:math';

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Project imports:
import '../../services/startup_service.dart';
import '../../routes/app_routes.dart';

class SplashScreen extends StatefulWidget {
  static const routeName = '/splash';
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => SplashScreenState();
}

class SplashScreenState extends State<SplashScreen> with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _logoController;
  late AnimationController _textController;
  late StartupService _startupService;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _textOpacityAnimation;
  late Animation<Offset> _textSlideAnimation;

  @override
  void initState() {
    super.initState();

    // 进度条动画控制器
    _progressController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    // Logo动画控制器
    _logoController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // 文字动画控制器
    _textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    // 设置Logo动画
    _logoScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _logoOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeIn,
    ));

    // 设置文字动画
    _textOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeIn,
    ));

    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    _startupService = StartupService.instance;
    _startAnimations();
    _initializeApp();
  }

  void _startAnimations() {
    // 启动Logo动画
    _logoController.forward();

    // 延迟启动文字动画
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _textController.forward();
      }
    });
  }

  Future<void> _initializeApp() async {
    // 如果应用已经初始化且不应该显示启动动画，直接跳转
    if (_startupService.isAppInitialized && !_startupService.shouldShowSplash) {
      _navigateToMain();
      return;
    }

    // 开始应用初始化
    await _startupService.initializeApp();

    // 等待初始化完成后跳转
    if (_startupService.isAppInitialized && mounted) {
      _navigateToMain();
    }
  }

  Future<void> _navigateToMain() async {
    // 确保进度条完成
    await _progressController.animateTo(
      1.0,
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeIn,
    );

    if (mounted) {
      Get.offAllNamed(Routes.main);
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用Logo - 添加动画效果
            AnimatedBuilder(
              animation: _logoController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _logoScaleAnimation.value,
                  child: Opacity(
                    opacity: _logoOpacityAnimation.value,
                    child: ClipOval(
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          SizedBox(
                            width: 180,
                            height: 180,
                            child: Image.asset(
                              'assets/icon/icon.png',
                              fit: BoxFit.cover,
                            ),
                          ),
                          SizedBox(
                            width: 180,
                            height: 180,
                            child: AnimatedBuilder(
                              animation: _progressController,
                              builder: (context, child) {
                                return CustomPaint(
                                  painter: CircularProgressPainter(
                                    progress: _progressController.value,
                                    color: Theme.of(context).primaryColor.withOpacity(0.3),
                                    strokeWidth: 4,
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 50),
            // 应用标题 - 添加滑入动画
            AnimatedBuilder(
              animation: _textController,
              builder: (context, child) {
                return SlideTransition(
                  position: _textSlideAnimation,
                  child: Opacity(
                    opacity: _textOpacityAnimation.value,
                    child: const Text(
                      'Tempognize · 知时',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 50),
            // 进度条和状态文本 - 添加淡入动画
            AnimatedBuilder(
              animation: _textController,
              builder: (context, child) {
                return Opacity(
                  opacity: _textOpacityAnimation.value,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 50),
                    child: GetBuilder<StartupService>(
                      builder: (startupService) {
                        // 同步进度条到当前初始化进度
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (mounted) {
                            _progressController.animateTo(
                              startupService.initializationProgress,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeOut,
                            );
                          }
                        });

                        return Column(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(2),
                              child: LinearProgressIndicator(
                                value: startupService.initializationProgress,
                                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Theme.of(context).primaryColor,
                                ),
                                minHeight: 4,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${(startupService.initializationProgress * 100).toInt()}%',
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            const SizedBox(height: 16),
                            // 显示当前初始化步骤
                            AnimatedSwitcher(
                              duration: const Duration(milliseconds: 300),
                              child: Text(
                                startupService.currentInitStep,
                                key: ValueKey(startupService.currentInitStep),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme.of(context).primaryColor.withOpacity(0.7),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  CircularProgressPainter({
    required this.progress,
    required this.color,
    this.strokeWidth = 2.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 创建圆形路径
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2;

    // 背景圆环
    final backgroundPaint = Paint()
      ..color = color.withOpacity(0.1)
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    canvas.drawCircle(center, radius - strokeWidth / 2, backgroundPaint);

    // 进度圆弧
    final progressPaint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // 添加渐变效果
    final gradient = SweepGradient(
      startAngle: -pi / 2,
      endAngle: -pi / 2 + 2 * pi * progress,
      colors: [
        color.withOpacity(0.3),
        color,
      ],
    );

    progressPaint.shader = gradient.createShader(
      Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
    );

    // 计算圆弧的起始和结束角度
    const startAngle = -pi / 2; // 从12点钟方向开始
    final sweepAngle = 2 * pi * progress;

    // 绘制进度圆弧
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
      startAngle,
      sweepAngle,
      false,
      progressPaint,
    );

    // 在进度条末端添加一个小圆点
    if (progress > 0) {
      final endAngle = startAngle + sweepAngle;
      final dotX = center.dx + (radius - strokeWidth / 2) * cos(endAngle);
      final dotY = center.dy + (radius - strokeWidth / 2) * sin(endAngle);

      final dotPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(dotX, dotY), strokeWidth / 2, dotPaint);
    }
  }

  @override
  bool shouldRepaint(CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.color != color || oldDelegate.strokeWidth != strokeWidth;
  }
}
