import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/note.dart';
import '../../helpers/database_helper.dart';
import '../../widgets/note_card.dart';
import '../note_detail/note_detail_page.dart';
import '../../l10n/localization.dart';
import '../../constants/layout_config.dart';
import '../../services/ai_service.dart';
import '../../models/segment.dart';

class NoteHierarchyPage extends StatefulWidget {
  final Note parentNote;

  const NoteHierarchyPage({
    super.key,
    required this.parentNote,
  });

  @override
  State<NoteHierarchyPage> createState() => _NoteHierarchyPageState();
}

class _NoteHierarchyPageState extends State<NoteHierarchyPage> {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final AIService _aiService = AIService();
  List<Note> _childNotes = [];
  bool _isLoading = true;
  Note? _faqNote;

  @override
  void initState() {
    super.initState();
    _loadChildNotes();
    _loadFAQNote();
  }

  Future<void> _loadChildNotes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final childNotes = await _databaseHelper.getChildNotes(widget.parentNote.id!);
      setState(() {
        _childNotes = childNotes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Error loading child notes: $e');
    }
  }

  Future<void> _loadFAQNote() async {
    try {
      final faqNote = await _databaseHelper.getFAQNote(widget.parentNote.id!);
      setState(() {
        _faqNote = faqNote;
      });
    } catch (e) {
      print('Error loading FAQ note: $e');
    }
  }

  Future<void> _openNoteDetail(Note note) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoteDetailPage(
          note: note,
          onNoteCreated: () async {
            // 刷新子笔记列表
            await _loadChildNotes();
            await _loadFAQNote();
          },
        ),
      ),
    );

    // 从笔记详情页返回后，只刷新当前列表
    // 移除自动导航到子层级页面的逻辑，避免重复导航
    await _loadChildNotes();
    await _loadFAQNote();
  }

  Future<void> _generateFAQ() async {
    // 检查是否已有FAQ
    final hasFAQ = await _databaseHelper.hasFAQNote(widget.parentNote.id!);
    if (hasFAQ) {
      if (mounted) {
        Get.snackbar(
          AppLocalizations.instance.message,
          AppLocalizations.instance.faqAlreadyExists,
        );
      }
      return;
    }

    // 整合父笔记和子笔记内容
    final combinedContent = _combineNoteContents();

    if (combinedContent.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('没有内容可生成FAQ')),
        );
      }
      return;
    }

    try {
      // 创建FAQ笔记（先创建一个pending状态的笔记）
      final now = DateTime.now();
      final faqNote = Note(
        title: '${widget.parentNote.title} - FAQ总结',
        content: [StyledTextSegment(text: '正在生成FAQ总结...')],
        originalContent: [StyledTextSegment(text: '正在生成FAQ总结...')],
        createdAt: now,
        updatedAt: now,
        parentNoteId: widget.parentNote.id,
        prompt: _createFAQPrompt(combinedContent),
        status: Note.STATUS_PENDING,
        type: Note.TYPE_FAQ,
      );

      // 保存到数据库
      final faqNoteId = await _databaseHelper.insertNote(faqNote);
      final savedFaqNote = await _databaseHelper.getNoteById(faqNoteId);

      if (savedFaqNote != null) {
        setState(() {
          _faqNote = savedFaqNote;
        });

        // 发送AI请求
        await _askAIForFAQ(savedFaqNote);
      }
    } catch (e) {
      print('Error creating FAQ: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成FAQ失败: $e')),
        );
      }
    }
  }

  String _combineNoteContents() {
    final StringBuffer buffer = StringBuffer();

    // 添加父笔记内容
    buffer.writeln('### 主要内容');
    for (final segment in widget.parentNote.content) {
      buffer.writeln(segment.text);
    }

    // 添加子笔记内容
    if (_childNotes.isNotEmpty) {
      buffer.writeln('\n### 子内容');
      for (final childNote in _childNotes) {
        buffer.writeln('\n#### ${childNote.title}');
        for (final segment in childNote.content) {
          buffer.writeln(segment.text);
        }
      }
    }

    return buffer.toString();
  }

  String _createFAQPrompt(String content) {
    return '''请基于以下学习材料，生成一个FAQ（常见问题解答）列表，帮助学习者进行复习和总结：

$content

要求：
1. 精心设计问题，确保覆盖核心知识点
2. 问题应该有助于学习者理解和记忆
3. 答案要简洁明了，重点突出
4. 按重要性排序，最重要的问题在前
5. 包含"是什么"、"为什么"、"怎么做"等不同类型的问题
6. 格式：### 问题\\n答案\\n\\n

请生成5-8个高质量的FAQ问题。''';
  }

  Future<void> _askAIForFAQ(Note faqNote) async {
    try {
      // 更新笔记状态为进行中
      final updatedNote = faqNote.copyWith(
        status: Note.STATUS_PENDING,
        isUpdating: true,
      );
      await _databaseHelper.updateNote(updatedNote);

      setState(() {
        _faqNote = updatedNote;
      });

      // 调用AI服务
      final response = await _aiService.askAI(faqNote, noUpdateContent: false);

      // AI服务返回的是更新后的笔记，直接使用
      setState(() {
        _faqNote = response;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('FAQ总结生成完成')),
        );
      }
    } catch (e) {
      print('Error asking AI for FAQ: $e');

      // 更新错误状态
      final errorNote = faqNote.copyWith(
        status: Note.STATUS_HTTP_ERROR,
        errorMessage: e.toString(),
        isUpdating: false,
      );
      await _databaseHelper.updateNote(errorNote);

      setState(() {
        _faqNote = errorNote;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成FAQ失败: $e')),
        );
      }
    }
  }

  Future<void> _openNoteHierarchy(Note note) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoteHierarchyPage(parentNote: note),
      ),
    );

    // 如果有变化，刷新列表
    if (result == true) {
      await _loadChildNotes();
      await _loadFAQNote();
    }
  }

  Future<void> _handleNoteTap(Note note) async {
    // 检查笔记是否有子笔记
    final hasChildren = await _databaseHelper.hasChildNotes(note.id!);

    if (hasChildren) {
      // 如果有子笔记，导航到层级页面
      await _openNoteHierarchy(note);
    } else {
      // 如果没有子笔记，直接打开笔记详情
      await _openNoteDetail(note);
    }
  }

  Widget _buildParentNoteCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Parent Note Label
          Row(
            children: [
              Icon(
                Icons.folder_open,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.instance.parentNote,
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '${_childNotes.length} ${AppLocalizations.instance.childNotes}',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Use the existing NoteCard component for consistent display
          NoteCard(
            note: widget.parentNote,
            isSelected: false,
            layout: TimelineLayout.single,
            onTap: () => _openNoteDetail(widget.parentNote),
          ),
        ],
      ),
    );
  }

  Widget _buildFAQNoteCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // FAQ Label
          Row(
            children: [
              Icon(
                Icons.quiz,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'FAQ总结',
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (_faqNote!.isUpdating)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
          const SizedBox(height: 8),
          // Use the existing NoteCard component for consistent display
          NoteCard(
            note: _faqNote!,
            isSelected: false,
            layout: TimelineLayout.single,
            onTap: () => _handleNoteTap(_faqNote!),
          ),
        ],
      ),
    );
  }

  Widget _buildChildNotesList() {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_childNotes.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.note_add_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.instance.noChildNotes,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _childNotes.length,
      itemBuilder: (context, index) {
        final note = _childNotes[index];
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: NoteCard(
            note: note,
            isSelected: false,
            layout: TimelineLayout.single,
            onTap: () => _handleNoteTap(note),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.instance.noteHierarchy),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.auto_awesome),
            color: Theme.of(context).primaryColor,
            onPressed: _generateFAQ,
            tooltip: '生成FAQ总结',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await _loadChildNotes();
          await _loadFAQNote();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildParentNoteCard(),
              if (_faqNote != null) _buildFAQNoteCard(),
              if (_childNotes.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    AppLocalizations.instance.childNotes,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
              ],
              _buildChildNotesList(),
              const SizedBox(height: 32), // 底部间距
            ],
          ),
        ),
      ),
    );
  }
}
