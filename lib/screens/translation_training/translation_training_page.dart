// ignore_for_file: use_build_context_synchronously

import 'dart:async'; // Add this import at the top

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../helpers/database_helper.dart';
import '../../l10n/localization.dart';
import '../../models/translation_training.dart';
import '../../services/ai_service.dart';
import '../../services/tts_service.dart';
import '../../services/voice_recognition_service.dart';
import '../../services/translate_service.dart';
import '../../services/event_bus.dart';

class TranslationTrainingPage extends StatefulWidget {
  final String noteContent;
  final int noteId;

  const TranslationTrainingPage({super.key, required this.noteContent, required this.noteId});

  @override
  State<TranslationTrainingPage> createState() => _TranslationTrainingPageState();
}

class _TranslationTrainingPageState extends State<TranslationTrainingPage> with WidgetsBindingObserver {
  final TTSService _ttsService = TTSService();
  final AIService _aiService = AIService();
  final VoiceRecognitionService _voiceService = VoiceRecognitionService();
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;

  // 训练数据
  List<TranslationTrainingItem> _trainingItems = [];
  int _currentItemIndex = 0;

  // 用户响应
  Map<int, Map<int, String>> _userResponses = {};
  Map<int, Map<String, dynamic>> _evaluations = {};

  // 数据库记录ID
  int? _trainingId;

  // 状态控制
  bool _isGenerating = false;
  bool _isRecording = false;
  bool _showTranslation = false;
  bool _showShowTranslationButton = false;
  bool _isDownloading = false;
  bool _isLongWordsExpanded = true;

  // 当前句子索引
  int _currentSentenceIndex = 0;

  // 当前句子播放次数
  int _currentSentencePlayCount = 0;

  // 文本输入控制器
  final TextEditingController _responseController = TextEditingController();

  // 播放状态管理
  PlayerState _playerState = PlayerState.stopped;
  StreamSubscription? _playerStateChangeSubscription;

  // 播放速度
  double _playbackRate = 1.0; // 默认播放速度

  // 添加播放完成监听
  StreamSubscription? _playerCompleteSubscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initStreams();
    _loadOrGenerateTrainingData();
  }

  void _initStreams() {
    print('初始化播放器状态流');

    // 取消已有的订阅
    _playerStateChangeSubscription?.cancel();
    _playerCompleteSubscription?.cancel();

    // 播放状态监听
    _playerStateChangeSubscription = _ttsService.playerStateStream.listen(
      (state) {
        print('收到播放器状态变更: $state');
        if (mounted) {
          setState(() => _playerState = state);
        }
      },
      onError: (error) {
        print('播放器状态流错误: $error');
        if (mounted) {
          setState(() => _playerState = PlayerState.stopped);
        }
      },
      cancelOnError: false,
    );

    // 添加播放完成监听
    _playerCompleteSubscription = _ttsService.onPlayerComplete.listen(
      (event) {
        print('收到播放完成事件');
        if (mounted) {
          setState(() {
            _playerState = PlayerState.stopped;
          });
        }
      },
      onError: (error) {
        print('播放完成事件流错误: $error');
      },
      cancelOnError: false,
    );

    // 初始化后立即检查当前状态
    Future.delayed(Duration(milliseconds: 300), () {
      if (mounted) {
        _syncPlayerState();
      }
    });
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);

    // 确保先取消所有订阅
    _playerStateChangeSubscription?.cancel();
    _playerCompleteSubscription?.cancel();

    // 然后再处理其他资源
    _ttsService.dispose();
    _voiceService.dispose();
    _responseController.dispose();

    super.dispose();
  }

  // 处理应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print('应用生命周期状态变化: $state');

    if (state == AppLifecycleState.paused) {
      // 应用进入后台，暂停播放
      if (_playerState == PlayerState.playing) {
        _ttsService.pause();
        print('应用进入后台，暂停播放');
      }
    } else if (state == AppLifecycleState.resumed) {
      // 应用回到前台，重新同步播放状态
      print('应用回到前台，同步播放状态');
      _syncPlayerState();

      // 确保重新检查并发送通知
      Future.delayed(Duration(milliseconds: 300), () {
        if (mounted) {
          _syncPlayerState();
        }
      });
    }
  }

  // 同步播放器状态
  Future<void> _syncPlayerState() async {
    try {
      // 获取当前真实的播放器状态
      final actualPlayerState = await _ttsService.getCurrentPlayerState();

      if (mounted && _playerState != actualPlayerState) {
        print('检测到播放器状态不同步: UI状态=$_playerState, 实际状态=$actualPlayerState');

        setState(() {
          _playerState = actualPlayerState;
        });

        // 如果UI显示正在播放但实际已停止，尝试恢复播放
        if (_playerState == PlayerState.playing && actualPlayerState == PlayerState.stopped) {
          _recoverPlayback();
        }
      }
    } catch (e) {
      print('同步播放器状态错误: $e');
    }
  }

  // 恢复播放（用于应用从后台返回前台时）
  Future<void> _recoverPlayback() async {
    if (_trainingItems.isEmpty || _currentItemIndex >= _trainingItems.length) {
      return;
    }

    try {
      await _ttsService.stop();
      await _ttsService.resetPlayer();

      setState(() {
        _isDownloading = true;
      });

      await _preloadAudio();
      await _playCurrentSentence();
    } catch (e) {
      setState(() {
        _isDownloading = false;
        _playerState = PlayerState.stopped;
      });
    }
  }

  // 加载或生成训练数据
  Future<void> _loadOrGenerateTrainingData() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      print('\n===== 开始加载训练数据 =====');
      print('笔记ID: ${widget.noteId}');

      // 尝试从数据库加载训练数据
      TranslationTraining? training;
      try {
        training = await _databaseHelper.getTranslationTrainingByNoteId(widget.noteId);
      } catch (dbError) {
        // 捕获数据库错误，包括表不存在等问题
        print('数据库加载训练数据错误: $dbError');
        print('将自动切换到生成新的训练内容');
        training = null;

        // 显示提示
        if (mounted) {
          Get.snackbar(
            AppLocalizations.instance.notifications,
            AppLocalizations.instance.databaseLoadFailedGenerateNew,
            duration: Duration(seconds: 2),
          );
        }
      }

      if (training != null) {
        print('成功从数据库加载训练数据');
        print('训练ID: ${training.id}');
        print('加载的项目索引: ${training.currentItemIndex}');
        print('加载的句子索引: ${training.currentSentenceIndex}');
        print('训练项目总数: ${training.items.length}');
        print('用户响应数: ${training.userResponses.length}');
        print('评估结果数: ${training.evaluations.length}');

        // 设置基本数据
        setState(() {
          // 使用 non-nullable 局部变量来避免多次null断言
          final nonNullTraining = training!;
          _trainingItems = nonNullTraining.items;
          _userResponses = nonNullTraining.userResponses;
          _evaluations = nonNullTraining.evaluations;
          _trainingId = nonNullTraining.id;
          _isGenerating = false;
          _showShowTranslationButton = false;
        });

        // 验证并设置当前项目和句子索引
        int nextItemIndex = training.currentItemIndex;
        int nextSentenceIndex = training.currentSentenceIndex;

        // 确保索引在有效范围内
        if (nextItemIndex >= _trainingItems.length) {
          print('警告：项目索引超出范围，重置为最后一个项目');
          print('原项目索引: $nextItemIndex, 最大有效索引: ${_trainingItems.length - 1}');
          nextItemIndex = _trainingItems.length - 1;
          nextSentenceIndex = 0;
        }

        if (nextItemIndex >= 0 && nextItemIndex < _trainingItems.length) {
          final currentItem = _trainingItems[nextItemIndex];
          if (nextSentenceIndex >= currentItem.sentences.length) {
            print('警告：句子索引超出范围，重置为0');
            print('原句子索引: $nextSentenceIndex, 最大有效索引: ${currentItem.sentences.length - 1}');
            nextSentenceIndex = 0;
          }
        }

        setState(() {
          _currentItemIndex = nextItemIndex;
          _currentSentenceIndex = nextSentenceIndex;
        });

        print('最终恢复到的位置：');
        print('项目索引: $_currentItemIndex');
        print('句子索引: $_currentSentenceIndex');

        // 显示加载成功提示
        Get.snackbar(
          AppLocalizations.instance.notifications,
          AppLocalizations.instance.previousTrainingProgressLoaded,
          duration: Duration(seconds: 2),
        );

        // 确保在加载完数据后预加载当前项目的音频
        if (_trainingItems.isNotEmpty && _currentItemIndex < _trainingItems.length) {
          await _preloadAudio();
        }
      } else {
        print('生成新的训练数据');
        _generateTrainingData().then((_) {
          _preloadAudio();
        });
      }

      if (_trainingItems.isNotEmpty) {
        await _preloadAudio();
      }
    } catch (e) {
      print('加载/生成训练数据错误: $e');
      setState(() {
        _isGenerating = false;
      });

      // 显示错误提示并尝试生成新训练数据
      if (mounted) {
        Get.snackbar(
          AppLocalizations.instance.notifications,
          "${AppLocalizations.instance.failedToLoadTrainingData}$e",
          duration: Duration(seconds: 3),
        );

        // 尝试自动恢复，生成新的训练数据
        Future.delayed(Duration(seconds: 1), () {
          if (_trainingItems.isEmpty && mounted) {
            print('尝试自动恢复，生成新的训练数据');
            _generateTrainingData().then((_) {
              _preloadAudio();
            });
          }
        });
      }
    }
  }

  // 生成训练数据
  Future<void> _generateTrainingData() async {
    try {
      // 清理笔记内容中可能的AI思考部分
      String cleanedContent = _cleanNoteContent(widget.noteContent);

      // 调用AI服务生成训练数据
      final items = await _aiService.generateTranslationTraining(cleanedContent);

      setState(() {
        _trainingItems = items;
        _currentItemIndex = 0;
        _currentSentenceIndex = 0;
        _userResponses = {};
        _evaluations = {};
        _isGenerating = false;
        _showShowTranslationButton = false;
      });

      // 保存训练数据到数据库
      await _saveTrainingData();

      // 显示生成成功提示
      Get.snackbar(
        AppLocalizations.instance.notifications,
        AppLocalizations.instance.trainingDataGenerationSuccess,
        duration: Duration(seconds: 2),
      );
    } catch (e) {
      print('生成训练数据错误: $e');
      setState(() {
        _isGenerating = false;
      });

      // 显示错误提示
      Get.snackbar(AppLocalizations.instance.trainingDataGenerationFailed, e.toString());
    }
  }

  // 清理笔记内容中的AI思考部分
  String _cleanNoteContent(String content) {
    // 移除"思考过程："或"Reasoning:"开始的段落
    final cleanedLines = content
        .split('\n')
        .where((line) =>
            !line.trim().startsWith("思考过程：") && !line.trim().startsWith("Reasoning:") && !line.trim().startsWith("> "))
        .toList();

    // 移除markdown格式的思考部分标题
    String cleaned = cleanedLines
        .join('\n')
        .replaceAll(RegExp(r'###\s*思考过程.*?###\s*内容', dotAll: true), '')
        .replaceAll(RegExp(r'<reasoning>.*?</reasoning>', dotAll: true), '');

    return cleaned;
  }

  // 保存训练数据到数据库
  Future<void> _saveTrainingData() async {
    try {
      print('===== 开始保存训练进度 =====');
      print('当前项目索引: $_currentItemIndex');
      print('当前句子索引: $_currentSentenceIndex');
      print('训练项目总数: ${_trainingItems.length}');
      print('用户响应数: ${_userResponses.length}');
      print('评估结果数: ${_evaluations.length}');
      print('当前训练ID: $_trainingId');

      // 首先验证数据库表是否存在
      try {
        // 确保数据库表已创建
        await _databaseHelper.verifyTables(await _databaseHelper.database);
      } catch (tableError) {
        print('验证数据库表时出错: $tableError');
      }

      // 更新 TranslationTraining 对象
      final updatedTraining = TranslationTraining(
        id: _trainingId,
        noteId: widget.noteId,
        items: _trainingItems,
        userResponses: _userResponses,
        evaluations: _evaluations,
        completed: false,
        score: 0.0,
        currentItemIndex: _currentItemIndex,
        currentSentenceIndex: _currentSentenceIndex,
        statistics: {},
        totalTime: Duration.zero,
      );

      // 保存到数据库
      final savedId = await _databaseHelper.saveTranslationTraining(updatedTraining);
      print('保存成功，训练ID: $savedId');

      setState(() {
        _trainingId = savedId;
      });
      print('===== 保存训练进度完成 =====\n');
    } catch (e) {
      print('保存训练数据错误: $e');
      print('错误堆栈: ${StackTrace.current}');
      if (mounted) {
        Get.snackbar(
          AppLocalizations.instance.notifications,
          "${AppLocalizations.instance.saveFailed}$e",
        );
      }
    }
  }

  // 播放当前项目
  Future<void> _playCurrentItem() async {
    if (_trainingItems.isEmpty || _currentItemIndex >= _trainingItems.length) {
      return;
    }

    final currentItem = _trainingItems[_currentItemIndex];
    setState(() => _isDownloading = true);

    try {
      // 修改为播放当前句子，而不是整个teacherExplanation
      if (currentItem.sentences.isNotEmpty) {
        // 重置句子索引和播放状态
        setState(() {
          _currentSentenceIndex = 0;
          _resetSentencePlayState();
        });
        await _playCurrentSentence();
      } else {
        // 如果没有句子，则播放整个解释（兼容旧数据）
        await _ttsService.speakText(
          currentItem.teacherExplanation,
          languageCode: 'en-US',
        );
      }
    } catch (e) {
      print('播放错误: $e');
      Get.snackbar(
        AppLocalizations.instance.notifications,
        "${AppLocalizations.instance.playbackFailed}$e",
      );
    } finally {
      setState(() => _isDownloading = false);
    }
  }

  // 播放当前句子
  Future<void> _playCurrentSentence() async {
    if (_trainingItems.isEmpty || _currentItemIndex >= _trainingItems.length) {
      return;
    }

    final item = _trainingItems[_currentItemIndex];
    if (_currentSentenceIndex >= item.sentences.length) {
      print('警告: 尝试播放无效的句子索引 $_currentSentenceIndex，最大索引为 ${item.sentences.length - 1}');
      setState(() {
        _currentSentenceIndex = 0; // 重置为第一个句子
      });
    }

    print('播放当前句子: 项目索引=$_currentItemIndex, 句子索引=$_currentSentenceIndex');

    setState(() {
      _isDownloading = true;
      // 不再提前设置播放状态，等待实际播放开始后再更新
    });

    try {
      // 增加播放计数
      setState(() {
        _currentSentencePlayCount++;
        // 检查是否达到显示翻译按钮的条件
        if (_currentSentencePlayCount >= 5) {
          _showShowTranslationButton = true;
        }
      });

      // 开始播放，播放状态会通过监听器自动更新
      await _ttsService.speakText(
        item.sentences[_currentSentenceIndex],
        languageCode: 'en-US',
      );

      // 如果播放开始后播放状态没有自动更新，手动检查并更新
      final currentState = await _ttsService.getCurrentPlayerState();
      if (mounted && _playerState != currentState) {
        setState(() {
          _playerState = currentState;
        });
      }
    } catch (e) {
      print('播放句子错误: $e');
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _playerState = PlayerState.stopped;
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  // 开始录音
  Future<void> _startRecording() async {
    final hasPermission = await _voiceService.checkPermission();
    if (!hasPermission) {
      Get.snackbar(
        AppLocalizations.instance.notifications,
        AppLocalizations.instance.microphonePermissionRequired,
      );
      return;
    }

    setState(() {
      _isRecording = true;
    });

    await _voiceService.startRecording();
  }

  // 停止录音并转写
  Future<void> _stopRecordingAndTranscribe() async {
    try {
      setState(() {
        _isRecording = false;
      });

      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('正在转写语音...'),
            ],
          ),
        ),
      );

      final text = await _voiceService.stopRecordingAndTranscribe(language: 'en');

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      if (text != null && text.isNotEmpty) {
        setState(() {
          _responseController.text = text;
        });
      } else {
        Get.snackbar(
          AppLocalizations.instance.notifications,
          AppLocalizations.instance.speechRecognitionFailed,
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      print('转写错误: $e');
      Get.snackbar(
        AppLocalizations.instance.notifications,
        "${AppLocalizations.instance.voiceTranscriptionFailed}$e",
      );
    }
  }

  // 记录所有用户响应（用于调试）
  void _logUserResponses() {
    print('===== 用户响应记录 =====');
    print('总共有 ${_userResponses.length} 条响应');
    _userResponses.forEach((sentence, response) {
      print('原句: $sentence');
      print('响应: $response');
      print('-------------------');
    });
  }

  // 修改提交响应方法
  Future<void> _submitResponse() async {
    if (_responseController.text.isEmpty) return;

    final currentItem = _trainingItems[_currentItemIndex];
    final currentSentence = currentItem.sentences[_currentSentenceIndex];

    print('提交响应: 用户输入 "${_responseController.text}" 对应原句 "$currentSentence"');

    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [CircularProgressIndicator(), SizedBox(width: 20), Text('正在评估你的理解...')],
        ),
      ),
    );

    try {
      print('正在调用AI评估用户理解...');
      // 调用AI服务评估用户理解
      final evaluationResult = await _aiService.evaluateSentenceUnderstanding(
        currentSentence,
        _responseController.text,
      );

      print('收到AI评估结果: $evaluationResult');

      // 提取剩余配额并发送更新事件
      int remainingQuota = evaluationResult['remaining_quota'] as int? ?? 0;
      print('剩余配额: $remainingQuota');

      // 发送事件更新Profile页面显示
      NoteEvents.eventBus.fire(QuotaUpdatedEvent(remainingQuota));

      // 保存用户响应
      if (!_userResponses.containsKey(_currentItemIndex)) {
        _userResponses[_currentItemIndex] = {};
      }
      _userResponses[_currentItemIndex]![_currentSentenceIndex] = _responseController.text;

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示评估结果对话框
      bool shouldContinue = await _showEvaluationDialog(evaluationResult);

      if (shouldContinue) {
        print('用户选择继续到下一句');
        _responseController.clear();

        if (_currentSentenceIndex >= currentItem.sentences.length - 1) {
          print('当前是最后一个句子，显示整体评估对话框');
          // 先保存当前进度
          await _saveProgress(source: '完成最后一个句子');
          await _showOverallEvaluationDialog();
        } else {
          print('移动到下一个句子: ${_currentSentenceIndex + 1}');
          setState(() {
            _currentSentenceIndex++;
            // 重置句子播放状态
            _resetSentencePlayState();
          });
          // 保存新的进度
          await _saveProgress(source: '移动到下一句');
          _preloadAudio().then((_) {
            _playCurrentSentence();
          });
        }
      } else {
        print('用户选择再试一次');
        _responseController.clear();
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      print('评估过程中出错: $e');
      // 显示错误提示
      Get.snackbar(
        AppLocalizations.instance.notifications,
        "${AppLocalizations.instance.evaluationFailed}$e",
      );
    }
  }

  // 显示评估结果对话框
  Future<bool> _showEvaluationDialog(Map<String, dynamic> evaluationResult) async {
    bool? understood = evaluationResult['understood'] as bool?;
    String feedback = evaluationResult['feedback'] as String? ?? '无法获取反馈';

    // 获取剩余配额信息
    int remainingQuota = evaluationResult['remaining_quota'] as int? ?? 0;

    final userResponse = _userResponses[_currentItemIndex]![_currentSentenceIndex] ?? _responseController.text;

    print('显示评估对话框: understood=$understood, feedback=$feedback');
    print('用户响应: $userResponse');
    print('剩余配额: $remainingQuota');

    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: Text(understood == true ? '理解正确 👍' : '需要改进 🤔'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('你的理解:'),
                Padding(
                  padding: const EdgeInsets.only(left: 8.0, top: 4.0, bottom: 8.0),
                  child: Text(userResponse),
                ),
                Divider(),
                Text('反馈:'),
                Padding(
                  padding: const EdgeInsets.only(left: 8.0, top: 4.0),
                  child: Text(feedback),
                ),
                Divider(),
                // 显示剩余配额信息
                Row(
                  children: [
                    Icon(Icons.data_usage, size: 18, color: Colors.blue),
                    SizedBox(width: 8),
                    Text(
                      '剩余AI使用次数: $remainingQuota',
                      style: TextStyle(
                        fontSize: 14,
                        color: remainingQuota < 10 ? Colors.red : Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false); // 返回false表示再试一次
                },
                child: Text(AppLocalizations.instance.tryMore),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(true); // 返回true表示继续
                },
                child: Text(AppLocalizations.instance.nextSentence),
              ),
            ],
          ),
        ) ??
        false; // 默认返回false（再试一次）
  }

  // 显示整体评估对话框
  Future<void> _showOverallEvaluationDialog() async {
    final currentItem = _trainingItems[_currentItemIndex];

    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [CircularProgressIndicator(), SizedBox(width: 20), Text('正在生成整体评估...')],
        ),
      ),
    );

    try {
      print('准备生成整体评估...');

      // 获取当前项目的所有句子
      final List<String> teacherContent = currentItem.sentences;

      // 获取当前项目的用户响应
      final Map<String, String> itemResponses = {};
      final currentItemUserResponses = _userResponses[_currentItemIndex] ?? {};

      for (int i = 0; i < teacherContent.length; i++) {
        final response = currentItemUserResponses[i];
        if (response != null) {
          itemResponses[teacherContent[i]] = response;
        }
      }

      print('当前项目句子数: ${teacherContent.length}, 用户响应数: ${itemResponses.length}');

      // 检查是否所有句子都有响应
      if (itemResponses.length < teacherContent.length) {
        print('警告: 用户没有响应所有句子，缺少 ${teacherContent.length - itemResponses.length} 个响应');

        // 对于没有响应的句子，添加一个空响应
        for (int i = 0; i < teacherContent.length; i++) {
          if (!currentItemUserResponses.containsKey(i)) {
            itemResponses[teacherContent[i]] = "(未提供响应)";
          }
        }
      }

      // 调用AI服务生成整体评估
      final evaluationResult = await _aiService.generateOverallEvaluation(
        teacherContent,
        itemResponses,
      );

      print('收到整体评估结果: $evaluationResult');

      // 提取剩余配额并发送更新事件
      int remainingQuota = evaluationResult['remaining_quota'] as int? ?? 0;
      print('剩余配额: $remainingQuota');

      // 发送事件更新Profile页面显示
      NoteEvents.eventBus.fire(QuotaUpdatedEvent(remainingQuota));

      // 保存整体评估结果到 _evaluations
      _evaluations[_currentItemIndex] = Map<String, dynamic>.from(evaluationResult);

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示整体评估结果对话框
      bool shouldMoveToNext = await showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder: (context) => AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.assessment, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('学习评估'),
                ],
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 总体评分
                    Text('总体理解水平:', style: TextStyle(fontWeight: FontWeight.bold)),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: LinearProgressIndicator(
                            value: (evaluationResult['overallScore'] as int) / 100,
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getScoreColor(evaluationResult['overallScore'] as int),
                            ),
                            minHeight: 10,
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          '${evaluationResult['overallScore']}%',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            color: _getScoreColor(evaluationResult['overallScore'] as int),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),

                    // 主要优点
                    Row(
                      children: [
                        Icon(Icons.thumb_up, color: Colors.green, size: 20),
                        SizedBox(width: 8),
                        Text('主要优点:', style: TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                    SizedBox(height: 4),
                    ...List.generate(
                      (evaluationResult['strengths'] as List?)?.length ?? 0,
                      (index) => Padding(
                        padding: const EdgeInsets.only(left: 16.0, top: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('• ', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green[700])),
                            Expanded(
                              child: Text(
                                (evaluationResult['strengths'] as List?)?[index] ?? "暂无数据",
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // 需要提高的方面
                    Row(
                      children: [
                        Icon(Icons.trending_up, color: Colors.orange, size: 20),
                        SizedBox(width: 8),
                        Text('需要改进的方面:', style: TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                    SizedBox(height: 4),
                    ...List.generate(
                      (evaluationResult['areasToImprove'] as List?)?.length ?? 0,
                      (index) => Padding(
                        padding: const EdgeInsets.only(left: 16.0, top: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('• ', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange[700])),
                            Expanded(
                              child: Text(
                                (evaluationResult['areasToImprove'] as List?)?[index] ?? "暂无数据",
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // 改进建议
                    Row(
                      children: [
                        Icon(Icons.lightbulb_outline, color: Colors.amber, size: 20),
                        SizedBox(width: 8),
                        Text('改进建议:', style: TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 16.0, top: 8.0),
                      child: Text(evaluationResult['suggestions'] as String? ?? '暂无建议'),
                    ),
                    SizedBox(height: 16),

                    Divider(),
                    // 鼓励性总结
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.favorite, color: Colors.red[400], size: 20),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              evaluationResult['encouragement'] as String? ?? '继续加油！',
                              style: TextStyle(
                                fontStyle: FontStyle.italic,
                                color: Colors.blue[800],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 16),
                    Divider(),

                    // 显示剩余配额信息
                    Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: remainingQuota < 10 ? Colors.red[50] : Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: remainingQuota < 10 ? Colors.red[200]! : Colors.blue[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.data_usage, color: remainingQuota < 10 ? Colors.red : Colors.blue, size: 20),
                          SizedBox(width: 8),
                          Text(
                            '剩余AI使用次数: $remainingQuota',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: remainingQuota < 10 ? Colors.red[800] : Colors.blue[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton.icon(
                  icon: Icon(Icons.refresh),
                  label: Text('重试项目'),
                  onPressed: () {
                    Navigator.of(context).pop(false); // 返回false表示重试
                  },
                ),
                ElevatedButton.icon(
                  icon: Icon(Icons.arrow_forward),
                  label: Text(_currentItemIndex >= _trainingItems.length - 1 ? '查看训练总结' : '进入下一项'),
                  onPressed: () {
                    Navigator.of(context).pop(true); // 返回true表示继续
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ) ??
          false; // 默认返回false（重试）

      if (shouldMoveToNext) {
        print('用户选择进入下一项');

        // 检查是否已经是最后一个项目
        if (_currentItemIndex >= _trainingItems.length - 1) {
          print('这是最后一个项目，显示训练汇总');
          // 直接显示训练汇总，不再尝试加载下一个项目
          _showTrainingSummary();
          return;
        }

        setState(() {
          _currentItemIndex++;
          _currentSentenceIndex = 0;
          _showTranslation = false;
        });

        try {
          // 使用统一的保存方法
          await _saveProgress(source: '完成当前项目，进入下一项');
          print('准备加载音频');

          // 重置播放器并预加载下一个项目的音频
          await _ttsService.resetPlayer();
          await _preloadAudio();
          await _playCurrentSentence();
        } catch (e) {
          print('加载过程中出错: $e');
          if (mounted) {
            Get.snackbar(
              AppLocalizations.instance.notifications,
              "${AppLocalizations.instance.loadFailed}$e",
            );
          }
        }
      } else {
        print('用户选择重试项目');
        setState(() {
          _currentSentenceIndex = 0;
        });
        _preloadAudio().then((_) {
          _playCurrentSentence();
        });
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      print('生成整体评估过程中出错: $e');
      // 显示错误提示
      Get.snackbar(
        AppLocalizations.instance.notifications,
        "${AppLocalizations.instance.overallAssessmentFailed}$e",
      );

      // 出错时默认进入下一项
      _moveToNextItem();
    }
  }

  // 统一的保存进度方法
  Future<void> _saveProgress({String source = ''}) async {
    try {
      print('\n===== 开始保存训练进度 (来源: $source) =====');
      print('当前项目索引: $_currentItemIndex');
      print('当前句子索引: $_currentSentenceIndex');
      print('训练项目总数: ${_trainingItems.length}');
      print('用户响应数: ${_userResponses.length}');
      print('评估结果数: ${_evaluations.length}');
      print('当前训练ID: $_trainingId');

      // 先获取最新的训练记录
      final existingTraining = await _databaseHelper.getTranslationTrainingByNoteId(widget.noteId);
      if (existingTraining != null) {
        print('找到现有训练记录，ID: ${existingTraining.id}');
        // 使用现有记录的ID
        _trainingId = existingTraining.id;
      }

      // 更新 TranslationTraining 对象
      final updatedTraining = TranslationTraining(
        id: _trainingId,
        noteId: widget.noteId,
        items: _trainingItems,
        userResponses: _userResponses,
        evaluations: _evaluations,
        completed: false,
        score: 0.0,
        currentItemIndex: _currentItemIndex,
        currentSentenceIndex: _currentSentenceIndex,
        statistics: {},
        totalTime: Duration.zero,
      );

      // 保存到数据库并等待完成
      final savedId = await _databaseHelper.saveTranslationTraining(updatedTraining);

      // 立即更新状态中的训练ID
      setState(() {
        _trainingId = savedId;
      });

      print('保存成功，训练ID: $savedId');
      print('更新后的训练ID: $_trainingId');
      print('===== 保存训练进度完成 =====\n');

      // 验证保存是否成功
      final savedTraining = await _databaseHelper.getTranslationTrainingByNoteId(widget.noteId);
      if (savedTraining != null) {
        print('验证保存结果:');
        print('保存的项目索引: ${savedTraining.currentItemIndex}');
        print('保存的句子索引: ${savedTraining.currentSentenceIndex}');
        print('保存的训练ID: ${savedTraining.id}');

        // 如果保存的结果与预期不符，尝试再次保存
        if (savedTraining.currentItemIndex != _currentItemIndex ||
            savedTraining.currentSentenceIndex != _currentSentenceIndex) {
          print('警告：保存结果与预期不符，尝试再次保存...');
          // 使用验证到的ID再次保存
          final retryTraining = TranslationTraining(
            id: savedTraining.id, // 使用验证到的ID
            noteId: widget.noteId,
            items: _trainingItems,
            userResponses: _userResponses,
            evaluations: _evaluations,
            completed: false,
            score: 0.0,
            currentItemIndex: _currentItemIndex,
            currentSentenceIndex: _currentSentenceIndex,
            statistics: {},
            totalTime: Duration.zero,
          );

          final retrySavedId = await _databaseHelper.saveTranslationTraining(retryTraining);
          setState(() {
            _trainingId = retrySavedId;
          });
          print('重试保存完成，新的训练ID: $retrySavedId');
        }
      }
    } catch (e) {
      print('保存训练数据错误: $e');
      print('错误堆栈: ${StackTrace.current}');
      if (mounted) {
        Get.snackbar(
          AppLocalizations.instance.notifications,
          "${AppLocalizations.instance.saveFailed}$e",
        );
      }
    }
  }

  // 重置句子播放状态
  void _resetSentencePlayState() {
    _currentSentencePlayCount = 0;
    _showShowTranslationButton = false;
    _showTranslation = false;
    // 重置播放速度为1.0x
    _setPlaybackRate(1.0);
  }

  // 修改移动到下一个训练项的方法
  Future<void> _moveToNextItem() async {
    await _ttsService.stop();

    if (_currentItemIndex < _trainingItems.length - 1) {
      setState(() {
        _currentItemIndex++;
        _currentSentenceIndex = 0;
        // 重置句子播放状态
        _resetSentencePlayState();
      });

      try {
        await _saveProgress(source: '移动到下一项');
        await _ttsService.resetPlayer();
        await _preloadAudio();
        await _playCurrentSentence();
      } catch (e) {
        if (mounted) {
          Get.snackbar(
            AppLocalizations.instance.notifications,
            "${AppLocalizations.instance.loadFailed}$e",
          );
        }
      }
    } else {
      _showCompletionDialog();
    }
  }

  // 修改移动到上一个训练项的方法
  Future<void> _moveToPreviousItem() async {
    await _ttsService.stop();

    if (_currentItemIndex > 0) {
      setState(() {
        _currentItemIndex--;
        _currentSentenceIndex = 0;
        // 重置句子播放状态
        _resetSentencePlayState();
      });

      try {
        await _saveProgress(source: '移动到上一项');
        await _ttsService.resetPlayer();
        await _preloadAudio();
        await _playCurrentSentence();
      } catch (e) {
        if (mounted) {
          Get.snackbar(
            AppLocalizations.instance.notifications,
            "${AppLocalizations.instance.loadFailed}$e",
          );
        }
      }
    }
  }

  // 显示完成对话框
  void _showCompletionDialog() {
    // 记录所有响应
    _logUserResponses();

    // 先显示最后一个项目的整体评估，然后再显示完成对话框
    if (_currentItemIndex >= 0 && _currentItemIndex < _trainingItems.length) {
      _showOverallEvaluationDialog().then((_) {
        // 评估完成后显示训练汇总页面
        _showTrainingSummary();
      });
    } else {
      // 如果没有有效的当前项目，直接显示训练汇总页面
      _showTrainingSummary();
    }
  }

  // 显示训练汇总页面
  void _showTrainingSummary() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        insetPadding: EdgeInsets.all(16),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.assessment, color: Colors.white),
                    SizedBox(width: 8),
                    Text(
                      '训练汇总',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Spacer(),
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // 内容区域
              Expanded(
                child: _buildSummaryContent(),
              ),

              // 底部按钮
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(4),
                    bottomRight: Radius.circular(4),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // TextButton.icon(
                    //   icon: Icon(Icons.share),
                    //   label: Text('分享报告'),
                    //   onPressed: () {
                    //     // 显示分享暂未实现的提示
                    //     ScaffoldMessenger.of(context).showSnackBar(
                    //       SnackBar(content: Text('分享功能正在开发中，敬请期待！')),
                    //     );
                    //   },
                    // ),
                    SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: Text('完成训练'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建训练汇总内容
  Widget _buildSummaryContent() {
    // 收集汇总统计
    int totalSentences = 0;
    int respondedSentences = 0;

    for (int i = 0; i < _trainingItems.length; i++) {
      final item = _trainingItems[i];
      totalSentences += item.sentences.length;

      if (_userResponses.containsKey(i)) {
        respondedSentences += _userResponses[i]!.length;
      }
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 统计卡片
          Card(
            elevation: 3,
            margin: EdgeInsets.only(bottom: 20),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '训练统计',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem('总知识点', '${_trainingItems.length}', Icons.list_alt, Colors.blue),
                      _buildStatItem('总句子数', '$totalSentences', Icons.record_voice_over, Colors.green),
                      _buildStatItem('完成度', '${(respondedSentences * 100 / totalSentences).round()}%',
                          Icons.check_circle, Colors.orange),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // 各项目汇总
          for (int i = 0; i < _trainingItems.length; i++) _buildItemSummary(i),
        ],
      ),
    );
  }

  // 构建单个统计项
  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  // 构建单个训练项的汇总
  Widget _buildItemSummary(int itemIndex) {
    final item = _trainingItems[itemIndex];
    final userResponses = _userResponses[itemIndex] ?? {};
    final evaluation = _evaluations[itemIndex];

    // 计算此项目的完成度
    int totalSentences = item.sentences.length;
    int respondedSentences = userResponses.length;
    double completionRate = totalSentences > 0 ? respondedSentences / totalSentences : 0;

    return Card(
      elevation: 2,
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: ExpansionTile(
        title: Row(
          children: [
            Text(
              '知识点 ${itemIndex + 1}',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(width: 8),
            SizedBox(
              width: 50,
              height: 4,
              child: LinearProgressIndicator(
                value: completionRate,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  completionRate >= 1 ? Colors.green : Colors.orange,
                ),
              ),
            ),
          ],
        ),
        subtitle: Text(
          item.blackboardContent.substring(0, item.blackboardContent.length > 50 ? 50 : item.blackboardContent.length) +
              (item.blackboardContent.length > 50 ? '...' : ''),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 黑板内容
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.instance.blackboard,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green[800],
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(item.blackboardContent),
                    ],
                  ),
                ),
                SizedBox(height: 16),

                // 句子和用户响应
                Text(
                  '句子与你的理解:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 8),

                for (int sentIndex = 0; sentIndex < item.sentences.length; sentIndex++)
                  _buildSentenceResponsePair(item.sentences[sentIndex], userResponses[sentIndex] ?? '未提供响应', sentIndex),

                // 整体评估（如果有）
                if (evaluation != null) ...[
                  SizedBox(height: 16),
                  Divider(),
                  SizedBox(height: 8),
                  Text(
                    '整体评估:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 8),

                  // 总体得分
                  if (evaluation.containsKey('overallScore')) ...[
                    Row(
                      children: [
                        Text('理解水平: '),
                        Expanded(
                          child: LinearProgressIndicator(
                            value: (evaluation['overallScore'] as int) / 100,
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getScoreColor(evaluation['overallScore'] as int),
                            ),
                            minHeight: 8,
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          '${evaluation['overallScore']}%',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _getScoreColor(evaluation['overallScore'] as int),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12),
                  ],

                  // 优点
                  if (evaluation.containsKey('strengths') && (evaluation['strengths'] as List).isNotEmpty) ...[
                    Text(
                      '优点:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    ),
                    SizedBox(height: 4),
                    ...List.generate(
                      (evaluation['strengths'] as List).length,
                      (index) => Padding(
                        padding: EdgeInsets.only(left: 16, bottom: 4),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('• ', style: TextStyle(color: Colors.green[700])),
                            Expanded(
                              child: Text(
                                (evaluation['strengths'] as List)[index] as String,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 8),
                  ],

                  // 需要改进的地方
                  if (evaluation.containsKey('areasToImprove') &&
                      (evaluation['areasToImprove'] as List).isNotEmpty) ...[
                    Text(
                      '需要改进:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange[700],
                      ),
                    ),
                    SizedBox(height: 4),
                    ...List.generate(
                      (evaluation['areasToImprove'] as List).length,
                      (index) => Padding(
                        padding: EdgeInsets.only(left: 16, bottom: 4),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('• ', style: TextStyle(color: Colors.orange[700])),
                            Expanded(
                              child: Text(
                                (evaluation['areasToImprove'] as List)[index] as String,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 8),
                  ],

                  // 建议
                  if (evaluation.containsKey('suggestions') && (evaluation['suggestions'] as String).isNotEmpty) ...[
                    Text(
                      '改进建议:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                    SizedBox(height: 4),
                    Padding(
                      padding: EdgeInsets.only(left: 16),
                      child: Text(evaluation['suggestions'] as String),
                    ),
                  ],
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建句子和用户响应对
  Widget _buildSentenceResponsePair(String sentence, String response, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 原句
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '句子 ${index + 1}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Spacer(),
                    Icon(Icons.volume_up, size: 16, color: Colors.blue),
                  ],
                ),
                SizedBox(height: 4),
                Text(
                  sentence,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // 用户响应
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '你的理解',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Text(
                  response,
                  style: TextStyle(
                    fontStyle: response == '未提供响应' ? FontStyle.italic : FontStyle.normal,
                    color: response == '未提供响应' ? Colors.grey : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 根据分数获取颜色
  Color _getScoreColor(int score) {
    if (score >= 90) return Colors.green[700]!;
    if (score >= 75) return Colors.blue[700]!;
    if (score >= 60) return Colors.orange[700]!;
    return Colors.red[700]!;
  }

  // 修改重新生成语音文件的方法
  void _regenerateAudio() {
    _ttsService.stop();
    _playCurrentItem();
  }

  // 格式化时间为 mm:ss 格式
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return "$minutes:$seconds";
  }

  // 设置播放速度
  void _setPlaybackRate(double rate) async {
    await _ttsService.setPlaybackRate(rate);
    setState(() {
      _playbackRate = rate;
    });
  }

  // 修改预加载方法
  Future<void> _preloadAudio() async {
    if (_trainingItems.isEmpty || _currentItemIndex >= _trainingItems.length) {
      return;
    }

    setState(() {
      _isDownloading = true;
    });

    try {
      final currentItem = _trainingItems[_currentItemIndex];

      if (_currentSentenceIndex >= currentItem.sentences.length) {
        setState(() {
          _currentSentenceIndex = 0;
        });
      }

      if (currentItem.sentences.isNotEmpty) {
        await _ttsService.prepareAudio(
          currentItem.sentences[_currentSentenceIndex],
          languageCode: 'en-US',
        );
      } else {
        await _ttsService.prepareAudio(
          currentItem.teacherExplanation,
          languageCode: 'en-US',
        );
      }

      setState(() {
        _playerState = PlayerState.paused;
        _isDownloading = false;
      });
    } catch (e) {
      setState(() {
        _isDownloading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.instance;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.translationTraining),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _isGenerating
                ? null
                : () async {
                    // 显示确认对话框
                    final bool? shouldRegenerate = await showDialog<bool>(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: Text('重新生成训练内容'),
                        content: Text('这将清除当前的训练进度。确定要重新生成吗？'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            child: Text('取消'),
                          ),
                          ElevatedButton(
                            onPressed: () => Navigator.of(context).pop(true),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                            child: Text('确定'),
                          ),
                        ],
                      ),
                    );

                    if (shouldRegenerate == true) {
                      // 显示加载对话框
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => Center(
                          child: Card(
                            child: Container(
                              padding: EdgeInsets.all(24),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CircularProgressIndicator(),
                                  SizedBox(height: 16),
                                  Text('正在重新生成训练内容，将消耗1次AI配额...'),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );

                      try {
                        // 删除旧的训练数据
                        if (_trainingId != null) {
                          await _databaseHelper.deleteTranslationTraining(_trainingId!);
                          _trainingId = null;
                        }
                        // 生成新的训练数据
                        await _generateTrainingData();

                        // 关闭加载对话框
                        if (mounted) Navigator.of(context).pop();
                      } catch (e) {
                        // 关闭加载对话框
                        if (mounted) Navigator.of(context).pop();
                        // 显示错误提示
                        Get.snackbar(
                          AppLocalizations.instance.notifications,
                          "${AppLocalizations.instance.regenerateTrainingContentFailed}$e",
                        );
                      }
                    }
                  },
            tooltip: '重新生成',
          ),
        ],
      ),
      // 使用SafeArea包裹整个内容，确保内容不会被系统UI遮挡
      body: _isGenerating
          ? Center(
              child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在生成训练内容，将消耗1次AI配额...'),
              ],
            ))
          : _trainingItems.isEmpty
              ? Center(child: Text('没有可用的训练内容'))
              : _buildTrainingContent(),
    );
  }

  Widget _buildTrainingContent() {
    if (_currentItemIndex >= _trainingItems.length) return Container();

    final currentItem = _trainingItems[_currentItemIndex];

    // 使用Column和Expanded来确保按钮始终在底部
    return Column(
      children: [
        // 主内容区域，使用Expanded确保它占据所有可用空间
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8.0), // 为底部按钮留出一点空间
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 进度指示器
                    LinearProgressIndicator(
                      value: (_currentItemIndex + 1) / _trainingItems.length,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '${AppLocalizations.instance.knowledgeItem} ${_currentItemIndex + 1}/${_trainingItems.length}',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    SizedBox(height: 16),

                    // 黑板内容 - 使用卡片显示
                    Card(
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.green[200]!, width: 1),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${AppLocalizations.instance.blackboard}  (${_currentSentenceIndex + 1}/${currentItem.sentences.length})',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: Colors.green[800],
                              ),
                            ),
                            Divider(color: Colors.green[200]),
                            Text(
                              currentItem.blackboardContent,
                              style: TextStyle(
                                fontSize: 16,
                                height: 1.5,
                              ),
                            ),

                            // 显示长单词
                            Builder(
                              builder: (context) {
                                final longWords = _extractLongWords(currentItem.sentences.isNotEmpty
                                    ? currentItem.sentences.join(' ')
                                    : currentItem.teacherExplanation);

                                // 只有当有长单词时才显示
                                if (longWords.isEmpty) return SizedBox();
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Divider(color: Colors.green[200]),
                                    InkWell(
                                      onTap: () {
                                        setState(() {
                                          _isLongWordsExpanded = !_isLongWordsExpanded;
                                        });
                                      },
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            AppLocalizations.instance.keyWords,
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 15,
                                              color: Colors.green[800],
                                            ),
                                          ),
                                          Icon(
                                            _isLongWordsExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                            color: Colors.green[800],
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (_isLongWordsExpanded) ...[
                                      SizedBox(height: 8),
                                      Wrap(
                                        spacing: 8,
                                        runSpacing: 8,
                                        children: longWords
                                            .map((word) => GestureDetector(
                                                  onTap: () {
                                                    // 使用底部弹出框显示翻译，避免打断用户流程
                                                    _showWordTranslation(word);
                                                  },
                                                  child: Container(
                                                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                                    decoration: BoxDecoration(
                                                      color: Colors.green[50],
                                                      borderRadius: BorderRadius.circular(8),
                                                      border: Border.all(color: Colors.green[300]!),
                                                    ),
                                                    child: Text(
                                                      word,
                                                      style: TextStyle(
                                                        color: Colors.green[800],
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                ))
                                            .toList(),
                                      ),
                                    ],
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 12),
                    // 教师解释部分
                    Card(
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue[200]!, width: 1),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  AppLocalizations.instance.teacherExplanation,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 15, // 减小字体大小
                                    color: Colors.blue[800],
                                  ),
                                  overflow: TextOverflow.ellipsis, // 文本溢出时显示省略号
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min, // 使Row占用最小空间
                                  children: [
                                    // 播放/暂停按钮
                                    SizedBox(
                                      width: 36,
                                      height: 36,
                                      child: InkWell(
                                        onTap: _isDownloading
                                            ? null
                                            : () async {
                                                try {
                                                  // 检查当前实际播放状态
                                                  final actualState = await _ttsService.getCurrentPlayerState();

                                                  // 如果UI状态与实际状态不一致，先同步状态
                                                  if (_playerState != actualState) {
                                                    setState(() {
                                                      _playerState = actualState;
                                                    });
                                                  }

                                                  if (_playerState == PlayerState.playing) {
                                                    print('暂停播放');
                                                    await _ttsService.pause();
                                                  } else if (_playerState == PlayerState.paused) {
                                                    print('继续播放');
                                                    await _ttsService.resume();
                                                  } else {
                                                    print('开始播放当前句子');
                                                    await _playCurrentSentence();
                                                  }

                                                  // 操作后检查状态是否正确更新，如果没有则手动更新
                                                  await Future.delayed(Duration(milliseconds: 100));
                                                  final newState = await _ttsService.getCurrentPlayerState();
                                                  if (mounted && _playerState != newState) {
                                                    setState(() {
                                                      _playerState = newState;
                                                    });
                                                  }
                                                } catch (e) {
                                                  print('播放控制错误: $e');
                                                  if (mounted) {
                                                    Get.snackbar(
                                                      AppLocalizations.instance.notifications,
                                                      "${AppLocalizations.instance.playbackControlFailed}$e",
                                                    );
                                                  }
                                                }
                                              },
                                        child: Tooltip(
                                          message: _playerState == PlayerState.playing
                                              ? AppLocalizations.instance.stopping
                                              : (_playerState == PlayerState.paused
                                                  ? AppLocalizations.instance.continuePlay
                                                  : AppLocalizations.instance.speak),
                                          child: Icon(
                                            _playerState == PlayerState.playing
                                                ? Icons.pause // 播放中显示暂停图标
                                                : _playerState == PlayerState.paused
                                                    ? Icons.play_arrow // 暂停时显示播放图标
                                                    : Icons.play_arrow, // 停止状态显示播放图标
                                            color: _isDownloading ? Colors.grey : Colors.blue[700],
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 4), // 添加小间距
                                    // 停止按钮
                                    SizedBox(
                                      width: 36,
                                      height: 36,
                                      child: InkWell(
                                        onTap: _playerState != PlayerState.stopped && !_isDownloading
                                            ? () => _ttsService.stop()
                                            : null,
                                        child: Tooltip(
                                          message: AppLocalizations.instance.stop,
                                          child: Icon(
                                            Icons.stop,
                                            color: _isDownloading || _playerState == PlayerState.stopped
                                                ? Colors.grey
                                                : Colors.blue[700],
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 4), // 添加小间距
                                    // 播放速度按钮
                                    SizedBox(
                                      width: 36,
                                      height: 36,
                                      child: InkWell(
                                        onTap: _isDownloading
                                            ? null
                                            : () {
                                                double newRate = _playbackRate == 1.0
                                                    ? 0.8
                                                    : _playbackRate == 0.8
                                                        ? 0.6
                                                        : 1.0;
                                                _setPlaybackRate(newRate);
                                              },
                                        child: Tooltip(
                                          message: AppLocalizations.instance.adjustPlaybackSpeed,
                                          child: Center(
                                            // 添加Center确保文本在容器中居中
                                            child: Text(
                                              '${_playbackRate}x',
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.bold,
                                                color: _isDownloading ? Colors.grey : Colors.blue[700],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 4), // 添加小间距
                                    // 显示/隐藏文本按钮
                                    SizedBox(
                                      width: 36,
                                      height: 36,
                                      child: Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          InkWell(
                                            onTap: _showShowTranslationButton
                                                ? () => setState(() => _showTranslation = !_showTranslation)
                                                : null,
                                            child: Tooltip(
                                              message: _showTranslation
                                                  ? AppLocalizations.instance.hideText
                                                  : AppLocalizations.instance.showText,
                                              child: Icon(
                                                _showTranslation ? Icons.visibility_off : Icons.visibility,
                                                color: _showShowTranslationButton ? Colors.blue[700] : Colors.grey,
                                              ),
                                            ),
                                          ),
                                          // 显示倒数计数，只在需要的时候显示
                                          if (_currentSentencePlayCount < 5)
                                            Positioned(
                                              right: 0,
                                              top: 0,
                                              child: Container(
                                                width: 12,
                                                height: 12,
                                                alignment: Alignment.center,
                                                decoration: BoxDecoration(
                                                  color: Colors.red,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: Text(
                                                  '${5 - _currentSentencePlayCount}',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 8,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 4), // 添加小间距
                                    // 重新生成语音按钮
                                    SizedBox(
                                      width: 36,
                                      height: 36,
                                      child: InkWell(
                                        onTap: _isDownloading ? null : _regenerateAudio,
                                        child: Tooltip(
                                          message: AppLocalizations.instance.regenerateAudio,
                                          child: Icon(
                                            Icons.refresh,
                                            color: _isDownloading ? Colors.grey : Colors.blue[700],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Divider(color: Colors.blue[200]),
                            // 播放进度条
                            StreamBuilder<Duration>(
                              key: ValueKey('progress_$_currentItemIndex'),
                              stream: _ttsService.positionStream,
                              builder: (context, snapshot) {
                                final position = snapshot.data ?? Duration.zero;
                                return StreamBuilder<Duration?>(
                                  stream: _ttsService.durationStream,
                                  builder: (context, durationSnapshot) {
                                    final duration =
                                        _isDownloading ? null : durationSnapshot.data ?? Duration(seconds: 30);
                                    final progress = (duration != null && duration.inMilliseconds > 0)
                                        ? position.inMilliseconds / duration.inMilliseconds
                                        : 0.0;
                                    return Column(
                                      children: [
                                        SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Text(
                                              _isDownloading ? "00:00" : _formatDuration(position),
                                              style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                                            ),
                                            Expanded(
                                              child: SliderTheme(
                                                data: SliderTheme.of(context).copyWith(
                                                  thumbShape: RoundSliderThumbShape(enabledThumbRadius: 8),
                                                  overlayShape: RoundSliderOverlayShape(overlayRadius: 14),
                                                  trackHeight: 4,
                                                ),
                                                child: Slider(
                                                  value: progress.clamp(0.0, 1.0),
                                                  onChanged: (_playerState != PlayerState.stopped && !_isDownloading)
                                                      ? (value) {
                                                          final newPosition = duration != null
                                                              ? Duration(
                                                                  milliseconds:
                                                                      (value * duration.inMilliseconds).round())
                                                              : Duration.zero;
                                                          _ttsService.seekTo(newPosition);
                                                        }
                                                      : null,
                                                  activeColor: Colors.blue[700],
                                                  inactiveColor: Colors.blue[200],
                                                ),
                                              ),
                                            ),
                                            Text(
                                              _isDownloading ? "--:--" : _formatDuration(duration ?? Duration.zero),
                                              style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                                            ),
                                          ],
                                        ),
                                      ],
                                    );
                                  },
                                );
                              },
                            ),
                            if (_showTranslation)
                              Container(
                                constraints: BoxConstraints(maxHeight: 200), // 限制最大高度
                                margin: EdgeInsets.only(top: 8),
                                child: SingleChildScrollView(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // 只显示当前句子
                                      if (currentItem.sentences.isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 4.0),
                                          child: Text(
                                            currentItem.sentences[_currentSentenceIndex],
                                            style: TextStyle(
                                              fontSize: 16,
                                              height: 1.5,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.blue[900],
                                              backgroundColor: Colors.yellow[100],
                                            ),
                                          ),
                                        )
                                      else
                                        // 兼容旧数据，显示整个解释
                                        Text(
                                          currentItem.teacherExplanation,
                                          style: TextStyle(
                                            fontSize: 16,
                                            height: 1.5,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 12),
                    // 用户响应部分
                    Card(
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.orange[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.orange[200]!, width: 1),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.instance.yourUnderstanding,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 15, // 减小字体大小
                                color: Colors.orange[800],
                              ),
                              overflow: TextOverflow.ellipsis, // 文本溢出时显示省略号
                            ),
                            Divider(color: Colors.orange[200]),
                            TextField(
                              controller: _responseController,
                              maxLines: 3,
                              readOnly: true,
                              decoration: InputDecoration(
                                hintText: AppLocalizations.instance.sayYourUnderstanding,
                                border: OutlineInputBorder(),
                                filled: true,
                                fillColor: Colors.white,
                              ),
                              style: TextStyle(fontSize: 12),
                            ),
                            SizedBox(height: 12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                ElevatedButton.icon(
                                  icon: Icon(_isRecording ? Icons.stop : Icons.mic, color: Colors.white),
                                  label: Text(
                                      _isRecording
                                          ? AppLocalizations.instance.stop
                                          : AppLocalizations.instance.inputByVoice,
                                      style: TextStyle(color: Colors.white)),
                                  onPressed: _isRecording ? _stopRecordingAndTranscribe : _startRecording,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: _isRecording ? Colors.red : Colors.blue,
                                    // 减小按钮内边距
                                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  ),
                                ),
                                Row(
                                  children: [
                                    // 添加上一句和下一句按钮
                                    if (currentItem.sentences.length > 1)
                                      IconButton(
                                        icon: Icon(Icons.arrow_back_ios),
                                        onPressed: _currentSentenceIndex > 0
                                            ? () async {
                                                print('切换到上一句: ${_currentSentenceIndex - 1}');
                                                _ttsService.stop();
                                                setState(() {
                                                  _currentSentenceIndex--;
                                                  // 重置句子播放状态
                                                  _resetSentencePlayState();
                                                });
                                                // 使用统一的保存方法
                                                await _saveProgress(source: '手动切换到上一句');
                                                _preloadAudio().then((_) {
                                                  _playCurrentSentence();
                                                });
                                              }
                                            : null,
                                        tooltip: '上一句',
                                        padding: EdgeInsets.all(8),
                                        constraints: BoxConstraints(),
                                      ),
                                    if (currentItem.sentences.length > 1)
                                      IconButton(
                                        icon: Icon(Icons.arrow_forward_ios),
                                        onPressed: _currentSentenceIndex < currentItem.sentences.length - 1
                                            ? () async {
                                                print('切换到下一句: ${_currentSentenceIndex + 1}');
                                                _ttsService.stop();
                                                setState(() {
                                                  _currentSentenceIndex++;
                                                  // 重置句子播放状态
                                                  _resetSentencePlayState();
                                                });
                                                // 使用统一的保存方法
                                                await _saveProgress(source: '手动切换到下一句');
                                                _preloadAudio().then((_) {
                                                  _playCurrentSentence();
                                                });
                                              }
                                            : null,
                                        tooltip: '下一句',
                                        padding: EdgeInsets.all(8),
                                        constraints: BoxConstraints(),
                                      ),
                                    SizedBox(width: 4),
                                    ElevatedButton(
                                      onPressed: _submitResponse,
                                      style: ElevatedButton.styleFrom(
                                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                                        minimumSize: Size(60, 36),
                                      ),
                                      child: Text(AppLocalizations.instance.submit),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // 底部导航按钮 - 固定在底部
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0), // 减小垂直内边距
          child: SafeArea(
            top: false, // 只在底部添加安全区域
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton.icon(
                  icon: Icon(Icons.arrow_back),
                  label: Text(AppLocalizations.instance.preItem),
                  onPressed: _currentItemIndex > 0
                      ? () async {
                          await _moveToPreviousItem();
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
                ElevatedButton.icon(
                  icon: Icon(
                    _currentItemIndex >= _trainingItems.length - 1 ? Icons.assessment : Icons.arrow_forward,
                    color: _currentItemIndex >= _trainingItems.length - 1 ? Colors.white : Colors.red[400],
                  ),
                  label: Text(_currentItemIndex >= _trainingItems.length - 1
                      ? AppLocalizations.instance.watchSummary
                      : AppLocalizations.instance.nextItem),
                  onPressed: _currentItemIndex < _trainingItems.length - 1
                      ? () async {
                          await _moveToNextItem();
                        }
                      : () {
                          // 直接显示训练汇总
                          _showTrainingSummary();
                        },
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    backgroundColor: _currentItemIndex >= _trainingItems.length - 1 ? Colors.green : null,
                    foregroundColor: _currentItemIndex >= _trainingItems.length - 1 ? Colors.white : null,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 提取长单词（6个或更多字母）
  List<String> _extractLongWords(String text) {
    // 使用正则表达式匹配单词（连续的字母）
    final RegExp wordRegex = RegExp(r'\b[a-zA-Z]{6,}\b');

    // 查找所有匹配项
    final matches = wordRegex.allMatches(text);

    // 转换为Set去重，然后转回List
    final Set<String> uniqueWords = {};
    for (final match in matches) {
      uniqueWords.add(match.group(0)!.toLowerCase());
    }

    // 按字母顺序排序
    final sortedWords = uniqueWords.toList()..sort();
    return sortedWords;
  }

  // 显示单词翻译
  void _showWordTranslation(String word) {
    // 显示底部弹出框
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _WordTranslationBottomSheet(
        word: word,
        ttsService: _ttsService,
      ),
    );
  }
}

// 单词翻译底部弹出框组件
class _WordTranslationBottomSheet extends StatefulWidget {
  final String word;
  final TTSService ttsService;

  const _WordTranslationBottomSheet({
    required this.word,
    required this.ttsService,
  });

  @override
  State<_WordTranslationBottomSheet> createState() => _WordTranslationBottomSheetState();
}

class _WordTranslationBottomSheetState extends State<_WordTranslationBottomSheet> {
  bool _isLoading = true;
  String _translation = '';
  List<String> _alternatives = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _translateWord();
  }

  // 翻译单词
  Future<void> _translateWord() async {
    try {
      final translateService = TranslateService();
      final result = await translateService.translate(
        text: widget.word,
        targetLang: 'zh', // 默认翻译成中文
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
          _translation = result.data;
          _alternatives = result.alternatives;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = '翻译失败: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题栏
              Row(
                children: [
                  Icon(Icons.translate, color: Colors.blue[700]),
                  SizedBox(width: 8),
                  Text(
                    widget.word,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Spacer(),
                  IconButton(
                    icon: Icon(Icons.volume_up),
                    onPressed: () {
                      widget.ttsService.speakText(widget.word, languageCode: 'en-US');
                    },
                    tooltip: '发音',
                  ),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: '关闭',
                  ),
                ],
              ),
              Divider(),

              // 内容区域 - 使用固定高度的容器确保加载状态和结果状态具有相同高度
              SizedBox(
                height: 150, // 设置一个固定高度，确保加载状态和结果状态一致
                width: double.infinity,
                child: _isLoading
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: 40, // 给加载指示器一个固定高度
                              width: 40, // 固定宽度
                              child: CircularProgressIndicator(),
                            ),
                            SizedBox(height: 16),
                            Text('正在翻译...'),
                          ],
                        ),
                      )
                    : _errorMessage != null
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: 40,
                                  width: 40,
                                  child: Icon(Icons.error_outline, color: Colors.red, size: 36),
                                ),
                                SizedBox(height: 16),
                                Text(_errorMessage!, style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          )
                        : SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('翻译:', style: TextStyle(fontWeight: FontWeight.bold)),
                                SizedBox(height: 8),
                                Container(
                                  padding: EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.blue[50],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  width: double.infinity,
                                  child: Text(
                                    _translation,
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                                if (_alternatives.isNotEmpty) ...[
                                  SizedBox(height: 16),
                                  Text('其他翻译:', style: TextStyle(fontWeight: FontWeight.bold)),
                                  SizedBox(height: 8),
                                  ...List.generate(
                                    _alternatives.length,
                                    (index) => Padding(
                                      padding: const EdgeInsets.only(bottom: 4.0),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                                          Expanded(child: Text(_alternatives[index])),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
