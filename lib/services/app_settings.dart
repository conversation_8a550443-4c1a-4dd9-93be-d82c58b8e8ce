// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';

import '../constants/layout_config.dart';

class AppSettings extends GetxController {
  final SharedPreferences _prefs;

  // 使用 Rx 变量替代普通变量
  final _locale = const Locale('zh', '').obs;
  final _colorScheme = FlexScheme.amber.obs;
  final _defaultDeleteDays = 15.obs;
  final _timelineLayout = TimelineLayout.double.obs;
  final _isInitialized = false.obs;

  // Getters for reactive variables
  Locale get locale => _locale.value;
  FlexScheme get colorScheme => _colorScheme.value;
  int get defaultDeleteDays => _defaultDeleteDays.value;
  TimelineLayout get timelineLayout => _timelineLayout.value;

  AppSettings(this._prefs) {
    _loadSettingsSync();
  }

  static const String _localeKey = 'locale';
  static const String _colorSchemeKey = 'colorScheme';
  static const String _timelineLayoutKey = 'timeline_layout';

  Future<bool> get isInitialized async {
    if (!_isInitialized.value) {
      await loadSettings();
      _isInitialized.value = true;
    }
    return _isInitialized.value;
  }

  void _loadSettingsSync() {
    final String? localeString = _prefs.getString(_localeKey);
    if (localeString != null) {
      final parts = localeString.split('_');
      _locale.value = Locale(parts[0], parts.length > 1 ? parts[1] : '');
    }

    final String? colorSchemeString = _prefs.getString(_colorSchemeKey);
    if (colorSchemeString != null) {
      _colorScheme.value = FlexScheme.values.firstWhere(
        (e) => e.toString() == colorSchemeString,
        orElse: () => FlexScheme.amber,
      );
    }

    _defaultDeleteDays.value = _prefs.getInt('defaultDeleteDays') ?? 15;

    final layoutIndex = _prefs.getInt(_timelineLayoutKey);
    _timelineLayout.value = layoutIndex != null ? TimelineLayout.values[layoutIndex] : TimelineLayout.double;
  }

  void setLocale(Locale newLocale) {
    _locale.value = newLocale;
    _prefs.setString(_localeKey, '${newLocale.languageCode}_${newLocale.countryCode}');
    Get.updateLocale(newLocale); // 立即更新语言
  }

  void setColorScheme(FlexScheme newColorScheme) {
    _colorScheme.value = newColorScheme;
    _prefs.setString(_colorSchemeKey, newColorScheme.toString());
    Get.changeTheme(theme);
  }

  Future<void> setTimelineLayout(TimelineLayout layout) async {
    _timelineLayout.value = layout;
    await _prefs.setInt(_timelineLayoutKey, layout.index);
  }

  ThemeData get theme => FlexThemeData.light(
        scheme: _colorScheme.value,
        subThemesData: const FlexSubThemesData(
          interactionEffects: true,
          tintedDisabledControls: true,
          blendOnColors: true,
          useTextTheme: true,
        ),
      );

  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    // ... 加载其他设置
    _defaultDeleteDays.value = prefs.getInt('defaultDeleteDays') ?? 15;
  }

  Future<void> setDefaultDeleteDays(int days) async {
    if (days != _defaultDeleteDays.value) {
      _defaultDeleteDays.value = days;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('defaultDeleteDays', days);
    }
  }
}
