// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';

// Project imports:
import '../helpers/database_helper.dart';
import '../models/note.dart';
import '../services/version_service.dart';
import '../services/app_settings.dart';
import '../core/services/dialog_queue_manager.dart';

class StartupService extends GetxController {
  static StartupService get instance => Get.find<StartupService>();

  final RxBool _isFirstLaunch = true.obs;
  final RxBool _isAppInitialized = false.obs;
  final RxBool _shouldShowSplash = true.obs;
  final RxDouble _initializationProgress = 0.0.obs;
  final RxString _currentInitStep = ''.obs;

  // 启动时间控制
  static const Duration _minimumSplashDuration = Duration(milliseconds: 2000); // 最少显示2秒
  DateTime? _splashStartTime;

  bool get isFirstLaunch => _isFirstLaunch.value;
  bool get isAppInitialized => _isAppInitialized.value;
  bool get shouldShowSplash => _shouldShowSplash.value;
  double get initializationProgress => _initializationProgress.value;
  String get currentInitStep => _currentInitStep.value;

  @override
  void onInit() {
    super.onInit();
    _checkAppState();
  }

  void _checkAppState() {
    // 应用恢复前台时，如果已经初始化过，则不显示启动页面
    if (_isAppInitialized.value) {
      _shouldShowSplash.value = false;
    }
  }

  Future<void> initializeApp() async {
    if (_isAppInitialized.value) {
      return;
    }

    // 记录启动开始时间
    _splashStartTime = DateTime.now();

    try {
      _updateProgress(0.1, '正在初始化系统设置...');
      await Future.delayed(const Duration(milliseconds: 200));

      // 设置应用只支持竖屏方向
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      _updateProgress(0.2, '正在加载应用设置...');
      await Future.delayed(const Duration(milliseconds: 200));

      // 初始化SharedPreferences和AppSettings
      final prefs = await SharedPreferences.getInstance();
      final appSettings = AppSettings(prefs);

      // 使用 Get.put 将 AppSettings 注入到 GetX 依赖管理中
      Get.put(appSettings, permanent: true);

      _updateProgress(0.4, '正在检查首次启动...');
      await Future.delayed(const Duration(milliseconds: 200));

      // 检查是否首次启动
      await _checkFirstLaunch();

      _updateProgress(0.6, '正在初始化版本服务...');
      await Future.delayed(const Duration(milliseconds: 200));

      // 初始化版本服务
      final versionService = VersionService();
      await versionService.init();

      _updateProgress(0.8, '正在设置系统UI...');
      await Future.delayed(const Duration(milliseconds: 200));

      // 设置系统UI样式
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ));

      _updateProgress(0.9, '正在完成初始化...');
      await Future.delayed(const Duration(milliseconds: 200));

      // 版本检查将在 DialogQueueManager 中进行，避免重复调用

      // 等待最小显示时间
      await _waitForMinimumDuration();

      _updateProgress(1.0, '初始化完成');

      // 标记应用已初始化
      _isAppInitialized.value = true;
      _shouldShowSplash.value = false;

      // 初始化启动对话框队列
      _initializeStartupDialogs();
    } catch (e) {
      print('App initialization error: $e');
      // 即使出错也要等待最小时间，然后标记为已初始化
      await _waitForMinimumDuration();
      _isAppInitialized.value = true;
      _shouldShowSplash.value = false;
    }
  }

  Future<void> _waitForMinimumDuration() async {
    if (_splashStartTime != null) {
      final elapsed = DateTime.now().difference(_splashStartTime!);
      final remaining = _minimumSplashDuration - elapsed;

      if (remaining.inMilliseconds > 0) {
        _updateProgress(0.95, '即将完成...');
        await Future.delayed(remaining);
      }
    }
  }

  Future<void> _checkFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFirstLaunch = prefs.getBool('is_first_launch') ?? true;
      _isFirstLaunch.value = isFirstLaunch;

      if (isFirstLaunch) {
        _updateProgress(0.5, '正在创建引导内容...');

        // 创建并保存引导笔记
        final guideNote = Note.createGuideNote();
        final db = DatabaseHelper();
        await db.insertNote(guideNote);

        // 创建并保存使用指南笔记
        final howToNote = Note.createHowToNote();
        await db.insertNote(howToNote);

        // 标记已不是首次启动
        await prefs.setBool('is_first_launch', false);
        _isFirstLaunch.value = false;
      }
    } catch (e) {
      print('Error in _checkFirstLaunch: $e');
    }
  }

  void _updateProgress(double progress, String step) {
    _initializationProgress.value = progress;
    _currentInitStep.value = step;
  }

  Future<bool> checkForceUpdate() async {
    try {
      final versionService = VersionService();
      return await versionService.checkForceUpdate();
    } catch (e) {
      print('Error checking force update: $e');
      return false;
    }
  }

  void onAppResumed() {
    // 应用恢复前台时，如果已经初始化过，则不显示启动页面
    if (_isAppInitialized.value) {
      _shouldShowSplash.value = false;
    }
  }

  void onAppPaused() {
    // 应用进入后台时的处理
  }

  /// 初始化启动对话框队列
  void _initializeStartupDialogs() {
    // 延迟执行以确保主界面已经加载完成
    Future.delayed(const Duration(milliseconds: 500), () {
      if (Get.isRegistered<DialogQueueManager>()) {
        final dialogManager = DialogQueueManager.instance;
        dialogManager.initializeStartupDialogs();
      }
    });
  }
}
