// Enhanced AI service with enterprise-grade error handling
// Integrates all error handling, retry, and offline capabilities

import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart' hide Response;
import 'package:dio/dio.dart' as dio;
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../core/error_handling/app_error.dart';
import '../core/error_handling/retry_manager.dart';
import '../core/error_handling/error_handler_service.dart';
import '../core/connectivity/connectivity_service.dart';
import '../core/offline/offline_queue_manager.dart';
import '../core/ui/loading_state_manager.dart';
import '../helpers/database_helper.dart';
import '../l10n/localization.dart';
import '../models/note.dart';
import '../models/segment.dart';
import '../services/event_bus.dart';
import '../services/auth_service.dart';
import '../utils/markdown_util.dart';

/// Configuration constants - migrated from existing AppConfig
class _Config {
  // Use the same configuration as the existing AppConfig
  static String get apiBaseUrl {
    // Import the existing config to maintain consistency
    const environment = Environment.prod; // Match existing config
    const baseUrls = {
      Environment.dev: 'http://localhost:8080',
      Environment.prod: 'https://martina-server.onrender.com',
    };
    const apiVersion = 'v1';
    final baseUrl = baseUrls[environment]!;
    return '$baseUrl/api/$apiVersion';
  }

  // Network timeouts matching existing AIService configuration
  static const Duration connectTimeout = Duration(seconds: 120);
  static const Duration receiveTimeout = Duration(seconds: 120);
  static const Duration sendTimeout = Duration(seconds: 120);

  // API endpoints
  static const String chatCompletionsEndpoint = '/chat/completions';
  static const String chatCompletionsProEndpoint = '/chat/completionsPro';
  static const String addQuotaEndpoint = '/chat/add-quota';
}

// Environment enum to match existing config
enum Environment { dev, prod }

/// Enhanced AI service with comprehensive error handling
class EnhancedAIService extends GetxService {
  static EnhancedAIService get instance => Get.find<EnhancedAIService>();

  late Dio _dio;
  final Map<int, CancelToken> _cancelTokens = {};

  // Service dependencies
  final ErrorHandlerService _errorHandler = ErrorHandlerService.instance;
  final ConnectivityService _connectivity = ConnectivityService.instance;
  final OfflineQueueManager _offlineQueue = OfflineQueueManager.instance;
  final LoadingStateManager _loadingManager = LoadingStateManager.instance;

  // Configuration
  static const int _maxConcurrentRequests = 3;
  int _activeRequests = 0;

  // System prompts migrated from existing AIService
  static const String _classificationPrompt = '''
你是一个专业的文本分类助手，需要：
1. 准确理解文本的主题和内容
2. 为文本生成简明扼要的标题
3. 对文本进行多层次分类
4. 分类要准确且具有层级关系
5. 必须以JSON格式返回，包含以下字段：
   - title: 内容的简明标题(不超过20字)
   - category: 一级分类
   - subCategory: 二级分类
   - subSubCategory: 三级分类
''';

  static const String _creationPrompt = '''
你是一个专业的知识助手，你的目标是提供清晰、准确且有帮助的回答。
**必须以JSON格式返回，包含以下字段：
- title: 内容的简明标题(不超过20字)
- category: 一级分类
- subCategory: 二级分类
- subSubCategory: 三级分类
- content: [{subTitle: 子标题, subContent: 本段详细内容},{subTitle: 子标题, subContent: 本段详细内容}...]
''';

  static const String _creationPromptPro = '''
你是一个专业的知识助手，你的目标是提供清晰、准确且富有洞察力的回答提纲，而不是给出具体回答。
**必须以JSON格式返回，包含以下字段：
- title: 内容的简明标题(不超过20字)
- category: 一级分类
- subCategory: 二级分类
- subSubCategory: 三级分类
- content: [{subTitle: 子标题, subContent: 本段内容摘要},{subTitle: 子标题, subContent: 本段内容摘要}...]
请记住，请提供回答的提纲，而不是给出具体回答。
''';

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeService();
  }

  @override
  void onClose() {
    _cancelAllRequests();
    _dio.close(force: true);
    super.onClose();
  }

  /// Initialize the service with proper configuration
  Future<void> _initializeService() async {
    _initDio();
    _registerOfflineOperations();

    if (kDebugMode) {
      print('EnhancedAIService initialized');
    }
  }

  /// Initialize Dio with enhanced configuration
  void _initDio() {
    _dio = Dio(BaseOptions(
      connectTimeout: _Config.connectTimeout,
      receiveTimeout: _Config.receiveTimeout,
      sendTimeout: _Config.sendTimeout,
      validateStatus: (status) => status != null && status < 500,
    ));

    // Add comprehensive interceptors
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        _logRequest(options);
        return handler.next(options);
      },
      onResponse: (response, handler) {
        _logResponse(response);
        return handler.next(response);
      },
      onError: (error, handler) async {
        await _handleDioError(error);
        return handler.next(error);
      },
    ));
  }

  /// Register offline operation executors
  void _registerOfflineOperations() {
    _offlineQueue.registerExecutor(OperationType.askAI, _executeOfflineAskAI);
    _offlineQueue.registerExecutor(OperationType.generateQuiz, _executeOfflineGenerateQuiz);
  }

  /// Enhanced askAI method with comprehensive error handling
  Future<Note> askAI(
    Note note, {
    bool noUpdateContent = true,
    bool proMode = false,
  }) async {
    // Check rate limiting
    if (_activeRequests >= _maxConcurrentRequests) {
      throw BusinessError(
        type: BusinessErrorType.invalidInput,
        code: 'RATE_LIMITED',
        message: 'Too many concurrent requests',
        userMessage: 'Please wait for other requests to complete before starting a new one.',
      );
    }

    _activeRequests++;

    // Start loading indicator
    final loadingId = _loadingManager.startLoading(
      type: LoadingType.aiRequest,
      message: noUpdateContent ? 'Analyzing content...' : (proMode ? 'Generating outline...' : 'Generating content...'),
      estimatedDuration: const Duration(seconds: 30),
      isCancellable: true,
      onCancel: () => cancelRequest(note.id ?? 0),
    );

    try {
      // Check connectivity
      if (!_connectivity.isOnline) {
        return await _handleOfflineAskAI(note, noUpdateContent: noUpdateContent, proMode: proMode);
      }

      // Check network quality
      if (!_connectivity.isQualitySufficient(NetworkQuality.poor)) {
        throw NetworkError(
          type: NetworkErrorType.timeout,
          code: 'POOR_CONNECTION',
          message: 'Network quality insufficient for AI requests',
          userMessage:
              'Your connection is too slow for AI requests. Please try again when you have a better connection.',
        );
      }

      // Execute with retry logic
      final result = await RetryPatterns.networkOperation(
        () => _executeAskAI(note, noUpdateContent: noUpdateContent, proMode: proMode),
        operationName: 'askAI',
      );

      if (result.isSuccess) {
        _loadingManager.completeLoading(loadingId);
        return result.data!;
      } else {
        throw result.error!;
      }
    } catch (error) {
      _loadingManager.completeLoading(loadingId);
      await _errorHandler.handleError(
        error,
        context: 'askAI - noteId: ${note.id}, proMode: $proMode',
        onRetry: () => askAI(note, noUpdateContent: noUpdateContent, proMode: proMode),
      );
      rethrow;
    } finally {
      _activeRequests--;
    }
  }

  /// Execute AI request with proper error handling
  Future<Note> _executeAskAI(
    Note note, {
    required bool noUpdateContent,
    required bool proMode,
  }) async {
    // Update note status
    Note updatedNote = note.copyWith(
      status: Note.STATUS_PENDING,
      errorMessage: '',
      updatedAt: DateTime.now(),
    )..isUpdating = true;

    // Set up cancellation
    final cancelToken = CancelToken();
    if (note.id != null) {
      _cancelTokens[note.id!] = cancelToken;
    }

    try {
      // Prepare request
      final systemPrompt = _getSystemPrompt(noUpdateContent, proMode);
      final endpoint = proMode
          ? '${_Config.apiBaseUrl}${_Config.chatCompletionsProEndpoint}'
          : '${_Config.apiBaseUrl}${_Config.chatCompletionsEndpoint}';

      // Make request
      final response = await _dio.post(
        endpoint,
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await AuthService().getToken()}',
            'Content-Type': 'application/json; charset=utf-8',
          },
        ),
        data: {
          "messages": [
            {"role": "system", "content": systemPrompt},
            {"role": "user", "content": note.prompt}
          ],
        },
        cancelToken: cancelToken,
      );

      // Process response
      return await _processAIResponse(response, note, noUpdateContent);
    } catch (error) {
      // Handle specific errors
      if (error is DioException) {
        if (CancelToken.isCancel(error)) {
          updatedNote = note.copyWith(
            status: Note.STATUS_CANCELLED,
            errorMessage: AppLocalizations.instance.userCancel,
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        } else {
          final appError = ErrorFactory.fromException(error, context: 'AI Request');
          updatedNote = note.copyWith(
            status: _mapErrorToNoteStatus(appError),
            errorMessage: appError.userMessage,
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        }
      } else {
        final appError = ErrorFactory.fromException(error, context: 'AI Request');
        updatedNote = note.copyWith(
          status: Note.STATUS_NETWORK_ERROR,
          errorMessage: appError.userMessage,
          updatedAt: DateTime.now(),
        )..isUpdating = false;
      }

      // Update database and notify UI
      await DatabaseHelper.instance.updateNote(updatedNote);
      NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));

      rethrow;
    } finally {
      // Cleanup
      if (note.id != null) {
        _cancelTokens.remove(note.id!);
      }
    }
  }

  /// Handle offline AI requests
  Future<Note> _handleOfflineAskAI(
    Note note, {
    required bool noUpdateContent,
    required bool proMode,
  }) async {
    // Create offline operation
    final operation = QueuedOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: OperationType.askAI,
      priority: OperationPriority.normal,
      data: {
        'noteId': note.id,
        'prompt': note.prompt,
        'noUpdateContent': noUpdateContent,
        'proMode': proMode,
      },
      createdAt: DateTime.now(),
    );

    // Queue for later execution
    await _offlineQueue.enqueue(operation);

    // Update note with offline status
    final offlineNote = note.copyWith(
      status: Note.STATUS_PENDING,
      errorMessage: 'Queued for when connection is restored',
      updatedAt: DateTime.now(),
    )..isUpdating = false;

    await DatabaseHelper.instance.updateNote(offlineNote);
    NoteEvents.eventBus.fire(NoteUpdatedEvent(offlineNote, false));

    return offlineNote;
  }

  /// Execute offline AI operation
  Future<dynamic> _executeOfflineAskAI(QueuedOperation operation) async {
    final noteId = operation.data['noteId'] as int?;
    if (noteId == null) {
      throw BusinessError(
        type: BusinessErrorType.invalidInput,
        code: 'INVALID_OPERATION_DATA',
        message: 'Missing note ID in offline operation',
        userMessage: 'Invalid operation data',
      );
    }

    final note = await DatabaseHelper.instance.getNoteById(noteId);
    if (note == null) {
      throw BusinessError(
        type: BusinessErrorType.invalidInput,
        code: 'NOTE_NOT_FOUND',
        message: 'Note not found for offline operation',
        userMessage: 'Note no longer exists',
      );
    }

    return await _executeAskAI(
      note,
      noUpdateContent: operation.data['noUpdateContent'] ?? true,
      proMode: operation.data['proMode'] ?? false,
    );
  }

  /// Execute offline quiz generation
  Future<dynamic> _executeOfflineGenerateQuiz(QueuedOperation operation) async {
    // Implementation for offline quiz generation
    throw UnimplementedError('Offline quiz generation not yet implemented');
  }

  /// Process AI response and update note
  Future<Note> _processAIResponse(dio.Response response, Note note, bool noUpdateContent) async {
    if (response.statusCode == 200) {
      try {
        final cleanedContent = _cleanAIResponse(json.encode(response.data));
        final aiData = json.decode(cleanedContent);

        Note updatedNote;
        if (noUpdateContent) {
          // Classification mode - only update categories
          updatedNote = note.copyWith(
            category: aiData['category'] ?? '',
            subCategory: aiData['subCategory'] ?? '',
            subSubCategory: aiData['subSubCategory'] ?? '',
            status: Note.STATUS_SUCCESS,
            errorMessage: '',
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        } else {
          // Content generation mode
          final content = _parseAIContent(aiData);
          updatedNote = note.copyWith(
            title: aiData['title'] ?? note.title,
            content: content,
            category: aiData['category'] ?? '',
            subCategory: aiData['subCategory'] ?? '',
            subSubCategory: aiData['subSubCategory'] ?? '',
            status: Note.STATUS_SUCCESS,
            errorMessage: '',
            updatedAt: DateTime.now(),
            reasoning: aiData['reasoning'] ?? '',
            recommendations: List<String>.from(aiData['recommendations'] ?? []),
          )..isUpdating = false;
        }

        // Update database and notify UI
        await DatabaseHelper.instance.updateNote(updatedNote);
        NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, true));

        return updatedNote;
      } catch (parseError) {
        throw SystemError(
          type: SystemErrorType.parseError,
          code: 'AI_RESPONSE_PARSE_ERROR',
          message: 'Failed to parse AI response',
          userMessage: 'The AI response was invalid. Please try again.',
          context: {'response': response.data.toString()},
        );
      }
    } else {
      // Handle HTTP errors
      final appError = _createHttpError(response.statusCode!, response.data);
      throw appError;
    }
  }

  /// Create appropriate error for HTTP status codes
  AppError _createHttpError(int statusCode, dynamic responseData) {
    switch (statusCode) {
      case 400:
        return BusinessError(
          type: BusinessErrorType.unsupportedQuestion,
          code: 'UNSUPPORTED_QUESTION',
          message: 'Unsupported question type',
          userMessage: AppLocalizations.instance.unsupportedQuestion,
        );
      case 401:
        return NetworkError(
          type: NetworkErrorType.unauthorized,
          code: 'UNAUTHORIZED',
          message: 'Authentication failed',
          userMessage: AppLocalizations.instance.loginExpired,
          statusCode: statusCode,
        );
      case 403:
        return BusinessError(
          type: BusinessErrorType.quotaExceeded,
          code: 'QUOTA_EXCEEDED',
          message: 'API quota exceeded',
          userMessage: AppLocalizations.instance.quotaExceeded,
        );
      case 429:
        return NetworkError(
          type: NetworkErrorType.rateLimited,
          code: 'RATE_LIMITED',
          message: 'Rate limit exceeded',
          userMessage: 'Too many requests. Please wait a moment and try again.',
          statusCode: statusCode,
        );
      default:
        return NetworkError(
          type: NetworkErrorType.serverError,
          code: 'HTTP_ERROR',
          message: 'HTTP error $statusCode',
          userMessage: AppLocalizations.instance.networkError,
          statusCode: statusCode,
        );
    }
  }

  /// Map AppError to Note status code
  int _mapErrorToNoteStatus(AppError error) {
    if (error is NetworkError) {
      switch (error.type) {
        case NetworkErrorType.timeout:
          return Note.STATUS_CONNECT_TIMEOUT;
        case NetworkErrorType.noConnection:
          return Note.STATUS_NETWORK_ERROR;
        case NetworkErrorType.unauthorized:
          return Note.STATUS_NOT_LOGIN;
        case NetworkErrorType.serverError:
          return Note.STATUS_HTTP_ERROR;
        default:
          return Note.STATUS_NETWORK_ERROR;
      }
    }

    if (error is BusinessError) {
      switch (error.type) {
        case BusinessErrorType.quotaExceeded:
          return Note.STATUS_QUOTA_EXCEEDED;
        case BusinessErrorType.unsupportedQuestion:
          return Note.STATUS_UNSUPPORTED_QUESTION;
        default:
          return Note.STATUS_HTTP_ERROR;
      }
    }

    if (error is SystemError) {
      switch (error.type) {
        case SystemErrorType.parseError:
          return Note.STATUS_JSON_ERROR;
        default:
          return Note.STATUS_NETWORK_ERROR;
      }
    }

    return Note.STATUS_NETWORK_ERROR;
  }

  /// Get appropriate system prompt
  String _getSystemPrompt(bool noUpdateContent, bool proMode) {
    if (noUpdateContent) {
      return _classificationPrompt;
    }

    return proMode ? _creationPromptPro : _creationPrompt;
  }

  /// Parse AI content into styled text segments
  List<StyledTextSegment> _parseAIContent(Map<String, dynamic> aiData) {
    final content = aiData['content'] as List<dynamic>?;
    if (content == null) return [];

    final segments = <StyledTextSegment>[];
    for (final item in content) {
      if (item is Map<String, dynamic>) {
        final subTitle = item['subTitle'] as String? ?? '';
        final subContent = item['subContent'] as String? ?? '';

        if (subTitle.isNotEmpty) {
          segments.add(StyledTextSegment(text: '## $subTitle\n\n', bold: true));
        }
        if (subContent.isNotEmpty) {
          segments.addAll(MarkdownUtil.parseMarkdown(subContent));
        }
        segments.add(StyledTextSegment(text: '\n\n'));
      }
    }

    return segments;
  }

  /// Clean AI response content
  String _cleanAIResponse(String response) {
    // Remove any markdown code blocks
    String cleaned = response.replaceAll(RegExp(r'```json\s*'), '');
    cleaned = cleaned.replaceAll(RegExp(r'```\s*$'), '');

    // Remove any leading/trailing whitespace
    cleaned = cleaned.trim();

    return cleaned;
  }

  /// Cancel a specific request
  void cancelRequest(int noteId) {
    final cancelToken = _cancelTokens[noteId];
    if (cancelToken != null && !cancelToken.isCancelled) {
      cancelToken.cancel('User cancelled request');
      _cancelTokens.remove(noteId);
    }
  }

  /// Cancel all active requests
  void _cancelAllRequests() {
    for (final cancelToken in _cancelTokens.values) {
      if (!cancelToken.isCancelled) {
        cancelToken.cancel('Service disposed');
      }
    }
    _cancelTokens.clear();
  }

  /// Log request for debugging
  void _logRequest(RequestOptions options) {
    if (kDebugMode) {
      print('AI Request: ${options.method} ${options.path}');
    }
  }

  /// Log response for debugging
  void _logResponse(dio.Response response) {
    if (kDebugMode) {
      print('AI Response: ${response.statusCode} ${response.requestOptions.path}');
    }
  }

  /// Handle Dio errors with proper categorization
  Future<void> _handleDioError(DioException error) async {
    final appError = ErrorFactory.fromException(error, context: 'AI Service');

    // Log error for monitoring
    await _errorHandler.handleError(
      appError,
      context: 'Dio interceptor',
      showToUser: false, // Don't show here, will be handled by calling method
    );
  }

  /// Get service statistics
  Map<String, dynamic> getServiceStats() {
    return {
      'activeRequests': _activeRequests,
      'maxConcurrentRequests': _maxConcurrentRequests,
      'activeCancelTokens': _cancelTokens.length,
      'isOnline': _connectivity.isOnline,
      'networkQuality': _connectivity.networkQuality.name,
    };
  }

  /// Generate quiz questions from note content
  Future<Map<String, dynamic>> generateQuizQuestions(String noteContent) async {
    const String quizSystemPrompt = '''
你是一个专业的题目生成助手，需要：
1. 根据内容生成有价值的测试题目
2. 题目难度要适中，考察要点明确
3. 选项设置要合理，具有区分度
4. 答案解析要详细且易懂
5. 除引用原文外，所有回答内容使用同一种语言
6. 必须返回标准的JSON格式
''';

    // Start loading indicator
    final loadingId = _loadingManager.startLoading(
      type: LoadingType.aiRequest,
      message: 'Generating quiz questions...',
      estimatedDuration: const Duration(seconds: 20),
      isCancellable: false,
    );

    try {
      final result = await RetryPatterns.networkOperation(
        () async {
          final response = await _dio.post(
            '${_Config.apiBaseUrl}${_Config.chatCompletionsEndpoint}',
            options: Options(
              headers: {
                'Authorization': 'Bearer ${await AuthService().getToken()}',
                'Content-Type': 'application/json; charset=utf-8',
              },
            ),
            data: {
              "messages": [
                {"role": "system", "content": quizSystemPrompt},
                {"role": "user", "content": AppLocalizations.instance.getQuizQuestion(noteContent)}
              ],
              "response_format": {"type": "json_object"}
            },
          );

          if (response.statusCode == 200) {
            final cleanedContent = _cleanAIResponse(json.encode(response.data));

            return {
              'questions_json': cleanedContent,
              'remaining_quota': response.data['remaining_quota'],
            };
          } else {
            throw NetworkError(
              type: NetworkErrorType.serverError,
              code: 'QUIZ_GENERATION_FAILED',
              message: 'Failed to generate quiz: ${response.statusCode}',
              userMessage: 'Failed to generate quiz questions. Please try again.',
              statusCode: response.statusCode,
            );
          }
        },
        operationName: 'generateQuiz',
      );

      if (result.isSuccess) {
        _loadingManager.completeLoading(loadingId);
        return result.data!;
      } else {
        throw result.error!;
      }
    } catch (error) {
      _loadingManager.completeLoading(loadingId);
      await _errorHandler.handleError(
        error,
        context: 'generateQuizQuestions',
        onRetry: () => generateQuizQuestions(noteContent),
      );
      rethrow;
    }
  }

  /// Reset service (useful after login/logout)
  void reset() {
    _cancelAllRequests();
    _dio.close(force: true);
    _initDio();
  }
}
