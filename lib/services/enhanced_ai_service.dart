// Enhanced AI service with enterprise-grade error handling
// Integrates all error handling, retry, and offline capabilities

import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart' hide Response;
import 'package:dio/dio.dart' as dio;
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../core/error_handling/app_error.dart';
import '../core/error_handling/retry_manager.dart';
import '../core/error_handling/error_handler_service.dart';
import '../core/connectivity/connectivity_service.dart';
import '../core/offline/offline_queue_manager.dart';
import '../helpers/database_helper.dart';
import '../l10n/localization.dart';
import '../models/note.dart';
import '../models/segment.dart';
import '../services/event_bus.dart';
import '../services/auth_service.dart';
import '../utils/markdown_util.dart';

/// Configuration constants
class _Config {
  static const String apiBaseUrl = 'https://api.example.com'; // Replace with actual API URL
}

/// Enhanced AI service with comprehensive error handling
class EnhancedAIService extends GetxService {
  static EnhancedAIService get instance => Get.find<EnhancedAIService>();

  late Dio _dio;
  final Map<int, CancelToken> _cancelTokens = {};

  // Service dependencies
  final ErrorHandlerService _errorHandler = ErrorHandlerService.instance;
  final ConnectivityService _connectivity = ConnectivityService.instance;
  final OfflineQueueManager _offlineQueue = OfflineQueueManager.instance;

  // Configuration
  static const Duration _defaultTimeout = Duration(seconds: 120);
  static const int _maxConcurrentRequests = 3;
  int _activeRequests = 0;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeService();
  }

  @override
  void onClose() {
    _cancelAllRequests();
    _dio.close(force: true);
    super.onClose();
  }

  /// Initialize the service with proper configuration
  Future<void> _initializeService() async {
    _initDio();
    _registerOfflineOperations();

    if (kDebugMode) {
      print('EnhancedAIService initialized');
    }
  }

  /// Initialize Dio with enhanced configuration
  void _initDio() {
    _dio = Dio(BaseOptions(
      connectTimeout: _defaultTimeout,
      receiveTimeout: _defaultTimeout,
      sendTimeout: _defaultTimeout,
      validateStatus: (status) => status != null && status < 500,
    ));

    // Add comprehensive interceptors
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        _logRequest(options);
        return handler.next(options);
      },
      onResponse: (response, handler) {
        _logResponse(response);
        return handler.next(response);
      },
      onError: (error, handler) async {
        await _handleDioError(error);
        return handler.next(error);
      },
    ));
  }

  /// Register offline operation executors
  void _registerOfflineOperations() {
    _offlineQueue.registerExecutor(OperationType.askAI, _executeOfflineAskAI);
    _offlineQueue.registerExecutor(OperationType.generateQuiz, _executeOfflineGenerateQuiz);
  }

  /// Enhanced askAI method with comprehensive error handling
  Future<Note> askAI(
    Note note, {
    bool noUpdateContent = true,
    bool proMode = false,
  }) async {
    // Check rate limiting
    if (_activeRequests >= _maxConcurrentRequests) {
      throw BusinessError(
        type: BusinessErrorType.invalidInput,
        code: 'RATE_LIMITED',
        message: 'Too many concurrent requests',
        userMessage: 'Please wait for other requests to complete before starting a new one.',
      );
    }

    _activeRequests++;

    try {
      // Check connectivity
      if (!_connectivity.isOnline) {
        return await _handleOfflineAskAI(note, noUpdateContent: noUpdateContent, proMode: proMode);
      }

      // Check network quality
      if (!_connectivity.isQualitySufficient(NetworkQuality.poor)) {
        throw NetworkError(
          type: NetworkErrorType.timeout,
          code: 'POOR_CONNECTION',
          message: 'Network quality insufficient for AI requests',
          userMessage:
              'Your connection is too slow for AI requests. Please try again when you have a better connection.',
        );
      }

      // Execute with retry logic
      final result = await RetryPatterns.networkOperation(
        () => _executeAskAI(note, noUpdateContent: noUpdateContent, proMode: proMode),
        operationName: 'askAI',
      );

      if (result.isSuccess) {
        return result.data!;
      } else {
        throw result.error!;
      }
    } catch (error) {
      await _errorHandler.handleError(
        error,
        context: 'askAI - noteId: ${note.id}, proMode: $proMode',
        onRetry: () => askAI(note, noUpdateContent: noUpdateContent, proMode: proMode),
      );
      rethrow;
    } finally {
      _activeRequests--;
    }
  }

  /// Execute AI request with proper error handling
  Future<Note> _executeAskAI(
    Note note, {
    required bool noUpdateContent,
    required bool proMode,
  }) async {
    // Update note status
    Note updatedNote = note.copyWith(
      status: Note.STATUS_PENDING,
      errorMessage: '',
      updatedAt: DateTime.now(),
    )..isUpdating = true;

    // Set up cancellation
    final cancelToken = CancelToken();
    if (note.id != null) {
      _cancelTokens[note.id!] = cancelToken;
    }

    try {
      // Prepare request
      final systemPrompt = _getSystemPrompt(noUpdateContent, proMode);
      final endpoint = proMode ? '${_Config.apiBaseUrl}/chat/completionsPro' : '${_Config.apiBaseUrl}/chat/completions';

      // Make request
      final response = await _dio.post(
        endpoint,
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await AuthService().getToken()}',
            'Content-Type': 'application/json; charset=utf-8',
          },
        ),
        data: {
          "messages": [
            {"role": "system", "content": systemPrompt},
            {"role": "user", "content": note.prompt}
          ],
        },
        cancelToken: cancelToken,
      );

      // Process response
      return await _processAIResponse(response, note, noUpdateContent);
    } catch (error) {
      // Handle specific errors
      if (error is DioException) {
        if (CancelToken.isCancel(error)) {
          updatedNote = note.copyWith(
            status: Note.STATUS_CANCELLED,
            errorMessage: AppLocalizations.instance.userCancel,
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        } else {
          final appError = ErrorFactory.fromException(error, context: 'AI Request');
          updatedNote = note.copyWith(
            status: _mapErrorToNoteStatus(appError),
            errorMessage: appError.userMessage,
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        }
      } else {
        final appError = ErrorFactory.fromException(error, context: 'AI Request');
        updatedNote = note.copyWith(
          status: Note.STATUS_NETWORK_ERROR,
          errorMessage: appError.userMessage,
          updatedAt: DateTime.now(),
        )..isUpdating = false;
      }

      // Update database and notify UI
      await DatabaseHelper.instance.updateNote(updatedNote);
      NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));

      rethrow;
    } finally {
      // Cleanup
      if (note.id != null) {
        _cancelTokens.remove(note.id!);
      }
    }
  }

  /// Handle offline AI requests
  Future<Note> _handleOfflineAskAI(
    Note note, {
    required bool noUpdateContent,
    required bool proMode,
  }) async {
    // Create offline operation
    final operation = QueuedOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: OperationType.askAI,
      priority: OperationPriority.normal,
      data: {
        'noteId': note.id,
        'prompt': note.prompt,
        'noUpdateContent': noUpdateContent,
        'proMode': proMode,
      },
      createdAt: DateTime.now(),
    );

    // Queue for later execution
    await _offlineQueue.enqueue(operation);

    // Update note with offline status
    final offlineNote = note.copyWith(
      status: Note.STATUS_PENDING,
      errorMessage: 'Queued for when connection is restored',
      updatedAt: DateTime.now(),
    )..isUpdating = false;

    await DatabaseHelper.instance.updateNote(offlineNote);
    NoteEvents.eventBus.fire(NoteUpdatedEvent(offlineNote, false));

    return offlineNote;
  }

  /// Execute offline AI operation
  Future<dynamic> _executeOfflineAskAI(QueuedOperation operation) async {
    final noteId = operation.data['noteId'] as int?;
    if (noteId == null) {
      throw BusinessError(
        type: BusinessErrorType.invalidInput,
        code: 'INVALID_OPERATION_DATA',
        message: 'Missing note ID in offline operation',
        userMessage: 'Invalid operation data',
      );
    }

    final note = await DatabaseHelper.instance.getNoteById(noteId);
    if (note == null) {
      throw BusinessError(
        type: BusinessErrorType.invalidInput,
        code: 'NOTE_NOT_FOUND',
        message: 'Note not found for offline operation',
        userMessage: 'Note no longer exists',
      );
    }

    return await _executeAskAI(
      note,
      noUpdateContent: operation.data['noUpdateContent'] ?? true,
      proMode: operation.data['proMode'] ?? false,
    );
  }

  /// Execute offline quiz generation
  Future<dynamic> _executeOfflineGenerateQuiz(QueuedOperation operation) async {
    // Implementation for offline quiz generation
    throw UnimplementedError('Offline quiz generation not yet implemented');
  }

  /// Process AI response and update note
  Future<Note> _processAIResponse(dio.Response response, Note note, bool noUpdateContent) async {
    if (response.statusCode == 200) {
      try {
        final cleanedContent = _cleanAIResponse(json.encode(response.data));
        final aiData = json.decode(cleanedContent);

        Note updatedNote;
        if (noUpdateContent) {
          // Classification mode - only update categories
          updatedNote = note.copyWith(
            category: aiData['category'] ?? '',
            subCategory: aiData['subCategory'] ?? '',
            subSubCategory: aiData['subSubCategory'] ?? '',
            status: Note.STATUS_SUCCESS,
            errorMessage: '',
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        } else {
          // Content generation mode
          final content = _parseAIContent(aiData);
          updatedNote = note.copyWith(
            title: aiData['title'] ?? note.title,
            content: content,
            category: aiData['category'] ?? '',
            subCategory: aiData['subCategory'] ?? '',
            subSubCategory: aiData['subSubCategory'] ?? '',
            status: Note.STATUS_SUCCESS,
            errorMessage: '',
            updatedAt: DateTime.now(),
            reasoning: aiData['reasoning'] ?? '',
            recommendations: List<String>.from(aiData['recommendations'] ?? []),
          )..isUpdating = false;
        }

        // Update database and notify UI
        await DatabaseHelper.instance.updateNote(updatedNote);
        NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, true));

        return updatedNote;
      } catch (parseError) {
        throw SystemError(
          type: SystemErrorType.parseError,
          code: 'AI_RESPONSE_PARSE_ERROR',
          message: 'Failed to parse AI response',
          userMessage: 'The AI response was invalid. Please try again.',
          context: {'response': response.data.toString()},
        );
      }
    } else {
      // Handle HTTP errors
      final appError = _createHttpError(response.statusCode!, response.data);
      throw appError;
    }
  }

  /// Create appropriate error for HTTP status codes
  AppError _createHttpError(int statusCode, dynamic responseData) {
    switch (statusCode) {
      case 400:
        return BusinessError(
          type: BusinessErrorType.unsupportedQuestion,
          code: 'UNSUPPORTED_QUESTION',
          message: 'Unsupported question type',
          userMessage: AppLocalizations.instance.unsupportedQuestion,
        );
      case 401:
        return NetworkError(
          type: NetworkErrorType.unauthorized,
          code: 'UNAUTHORIZED',
          message: 'Authentication failed',
          userMessage: AppLocalizations.instance.loginExpired,
          statusCode: statusCode,
        );
      case 403:
        return BusinessError(
          type: BusinessErrorType.quotaExceeded,
          code: 'QUOTA_EXCEEDED',
          message: 'API quota exceeded',
          userMessage: AppLocalizations.instance.quotaExceeded,
        );
      case 429:
        return NetworkError(
          type: NetworkErrorType.rateLimited,
          code: 'RATE_LIMITED',
          message: 'Rate limit exceeded',
          userMessage: 'Too many requests. Please wait a moment and try again.',
          statusCode: statusCode,
        );
      default:
        return NetworkError(
          type: NetworkErrorType.serverError,
          code: 'HTTP_ERROR',
          message: 'HTTP error $statusCode',
          userMessage: AppLocalizations.instance.networkError,
          statusCode: statusCode,
        );
    }
  }

  /// Map AppError to Note status code
  int _mapErrorToNoteStatus(AppError error) {
    if (error is NetworkError) {
      switch (error.type) {
        case NetworkErrorType.timeout:
          return Note.STATUS_CONNECT_TIMEOUT;
        case NetworkErrorType.noConnection:
          return Note.STATUS_NETWORK_ERROR;
        case NetworkErrorType.unauthorized:
          return Note.STATUS_NOT_LOGIN;
        case NetworkErrorType.serverError:
          return Note.STATUS_HTTP_ERROR;
        default:
          return Note.STATUS_NETWORK_ERROR;
      }
    }

    if (error is BusinessError) {
      switch (error.type) {
        case BusinessErrorType.quotaExceeded:
          return Note.STATUS_QUOTA_EXCEEDED;
        case BusinessErrorType.unsupportedQuestion:
          return Note.STATUS_UNSUPPORTED_QUESTION;
        default:
          return Note.STATUS_HTTP_ERROR;
      }
    }

    if (error is SystemError) {
      switch (error.type) {
        case SystemErrorType.parseError:
          return Note.STATUS_JSON_ERROR;
        default:
          return Note.STATUS_NETWORK_ERROR;
      }
    }

    return Note.STATUS_NETWORK_ERROR;
  }

  /// Get appropriate system prompt
  String _getSystemPrompt(bool noUpdateContent, bool proMode) {
    if (noUpdateContent) {
      return '''You are a professional knowledge assistant. Your goal is to provide accurate categorization.
**Must return in JSON format with the following fields:
- category: Primary category
- subCategory: Secondary category  
- subSubCategory: Tertiary category''';
    }

    return proMode
        ? '''You are a professional knowledge assistant. Your goal is to provide clear, accurate and insightful answer outlines, not specific answers.
**Must return in JSON format with the following fields:
- title: Concise title of the content (no more than 20 characters)
- category: Primary category
- subCategory: Secondary category
- subSubCategory: Tertiary category
- content: [{subTitle: subtitle, subContent: summary of this section content},{subTitle: subtitle, subContent: summary of this section content}...]
Please remember to provide answer outlines, not specific answers.'''
        : '''You are a professional knowledge assistant. Your goal is to provide clear, accurate and helpful responses.
**Must return in JSON format with the following fields:
- title: Concise title of the content (no more than 20 characters)
- category: Primary category
- subCategory: Secondary category
- subSubCategory: Tertiary category
- content: [{subTitle: subtitle, subContent: detailed content for this section},{subTitle: subtitle, subContent: detailed content for this section}...]''';
  }

  /// Parse AI content into styled text segments
  List<StyledTextSegment> _parseAIContent(Map<String, dynamic> aiData) {
    final content = aiData['content'] as List<dynamic>?;
    if (content == null) return [];

    final segments = <StyledTextSegment>[];
    for (final item in content) {
      if (item is Map<String, dynamic>) {
        final subTitle = item['subTitle'] as String? ?? '';
        final subContent = item['subContent'] as String? ?? '';

        if (subTitle.isNotEmpty) {
          segments.add(StyledTextSegment(text: '## $subTitle\n\n', bold: true));
        }
        if (subContent.isNotEmpty) {
          segments.addAll(MarkdownUtil.parseMarkdown(subContent));
        }
        segments.add(StyledTextSegment(text: '\n\n'));
      }
    }

    return segments;
  }

  /// Clean AI response content
  String _cleanAIResponse(String response) {
    // Remove any markdown code blocks
    String cleaned = response.replaceAll(RegExp(r'```json\s*'), '');
    cleaned = cleaned.replaceAll(RegExp(r'```\s*$'), '');

    // Remove any leading/trailing whitespace
    cleaned = cleaned.trim();

    return cleaned;
  }

  /// Cancel a specific request
  void cancelRequest(int noteId) {
    final cancelToken = _cancelTokens[noteId];
    if (cancelToken != null && !cancelToken.isCancelled) {
      cancelToken.cancel('User cancelled request');
      _cancelTokens.remove(noteId);
    }
  }

  /// Cancel all active requests
  void _cancelAllRequests() {
    for (final cancelToken in _cancelTokens.values) {
      if (!cancelToken.isCancelled) {
        cancelToken.cancel('Service disposed');
      }
    }
    _cancelTokens.clear();
  }

  /// Log request for debugging
  void _logRequest(RequestOptions options) {
    if (kDebugMode) {
      print('AI Request: ${options.method} ${options.path}');
    }
  }

  /// Log response for debugging
  void _logResponse(dio.Response response) {
    if (kDebugMode) {
      print('AI Response: ${response.statusCode} ${response.requestOptions.path}');
    }
  }

  /// Handle Dio errors with proper categorization
  Future<void> _handleDioError(DioException error) async {
    final appError = ErrorFactory.fromException(error, context: 'AI Service');

    // Log error for monitoring
    await _errorHandler.handleError(
      appError,
      context: 'Dio interceptor',
      showToUser: false, // Don't show here, will be handled by calling method
    );
  }

  /// Get service statistics
  Map<String, dynamic> getServiceStats() {
    return {
      'activeRequests': _activeRequests,
      'maxConcurrentRequests': _maxConcurrentRequests,
      'activeCancelTokens': _cancelTokens.length,
      'isOnline': _connectivity.isOnline,
      'networkQuality': _connectivity.networkQuality.name,
    };
  }

  /// Reset service (useful after login/logout)
  void reset() {
    _cancelAllRequests();
    _dio.close(force: true);
    _initDio();
  }
}
