// Dart imports:
import 'dart:convert';
import 'dart:developer' as developer;

// Flutter imports:

// Package imports:
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project imports:
import '../config.dart';
import '../helpers/database_helper.dart';
import '../l10n/localization.dart';
import '../models/note.dart';
import '../utils/markdown_util.dart';
import '../services/event_bus.dart';
import '../services/auth_service.dart';
import '../models/translation_training.dart';

class AIService {
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;

  late Dio _dio;
  final Map<int, CancelToken> _cancelTokens = {};

  // 修改系统提示词为两个不同场景的常量
  static const String _CLASSIFICATION_PROMPT = '''
你是一个专业的文本分类助手，需要：
1. 准确理解文本的主题和内容
2. 为文本生成简明扼要的标题
3. 对文本进行多层次分类
4. 分类要准确且具有层级关系
5. 必须以JSON格式返回，包含以下字段：
   - title: 内容的简明标题(不超过20字)
   - category: 一级分类
   - subCategory: 二级分类
   - subSubCategory: 三级分类
''';

  static const String _CREATION_PROMPT = '''
你是一个专业的知识助手，你的目标是提供清晰、准确且富有洞察力的回答。
在给出最终答案之前，请逐步思考并解释你的推理过程。 具体来说：
1. **理解问题：** 首先，请确保你完全理解用户提出的问题或请求，识别出问题的关键要素和隐含的需求。如果问题不明确或存在歧义，请主动分类进行分析。
2. **分解问题（如果需要）：** 如果问题较为复杂，请将其分解为更小的、更容易处理的子问题。 逐步解决这些子问题，最终组合成对原问题的解答。
3. **不要询问用户问题：** 不要询问用户问题并且期待用户给你回答，如果有问题需要询问用户，你应该基于问题可能的答案预先分类分析
4. **检索相关知识：** 利用你掌握的知识和信息，回忆与问题相关的概念、事实、原理和方法。
5. **进行逻辑推理：** 运用逻辑思维，将检索到的知识与问题连接起来，进行分析、比较、归纳和演绎，推导出可能的解决方案或答案。
6. **逐步构建答案：** 清晰地阐述你的推理过程，说明你是如何从已知信息推导出结论的。 可以使用以下方法来组织你的思考过程：
   *   列出你考虑过的不同可能性或方法。
   *   解释你选择特定方法的原因。
   *   描述你每个步骤的观点。
   *   解释每个步骤背后的逻辑。
7. **给出最终答案：** 在详细解释你的推理过程之后，清晰明确地给出最终的答案或解决方案。
8. **提供解释和支持：** 确保你的答案有充分的解释和支持。 可以包括：
   *   相关的定义和概念。
   *   支持你结论的证据或理由。
   *   相关的例子或类比，帮助用户理解。
   *   可能的例外情况或需要注意的事项。
9. **清晰简洁：** 虽然鼓励详细的推理过程，但也应注意语言的清晰度和简洁性，避免冗余和含糊不清的表达。 使用合适的术语，但也要考虑到用户的理解能力。
10. **格式：** 合理使用 Markdown 格式来组织内容，但是不能出现 table 语法，不能出现 latex 语法
11. **最佳实践：** 优先推荐最佳实践和现代化方法
12. **历史：** 如果有必要，可以按照时间线介绍一些概念
13. **提醒：** 指出潜在的问题和注意事项
14. **思辨：** 尽量在你的回答中应用思辨的思考方式
15. **标题：** 你要给你的回答赋予一个标题
16. **推荐：** 回答的最后需要推荐3个与这个问题相关的问题或推荐关注学习的知识点
17. **回答不要泛泛而谈：** 要把一个知识点深入透彻地进行分析
18. **格式：** 必须以JSON格式返回，包含以下字段：
   - title: 内容的简明标题(不超过20字)
   - category: 一级分类
   - subCategory: 二级分类
   - subSubCategory: 三级分类
   - content: 主要内容(使用Markdown格式)
   - recommendations: [推荐知识点1,推荐知识点2,推荐知识点3] 
请记住，你的目标不仅是给出答案，更是帮助用户理解答案背后的原因和原理。 通过逐步思考和清晰地解释你的推理过程，你将能够提供更有价值、更深入的帮助。
''';

  static const String _CREATION_PROMPT_PRO = '''
你是一个专业的知识助手，你的目标是提供清晰、准确且富有洞察力的回答提纲，而不是给出具体回答。
**必须以JSON格式返回，包含以下字段：
- title: 内容的简明标题(不超过20字)
- category: 一级分类
- subCategory: 二级分类
- subSubCategory: 三级分类
- content: [{subTitle: 子标题, subContent: 本段内容摘要},{subTitle: 子标题, subContent: 本段内容摘要}...]
请记住，请提供回答的提纲，而不是给出具体回答。
''';

  AIService._internal() {
    _initDio();
  }

  // 新增初始化 Dio 的方法
  void _initDio() {
    _dio = Dio();
    _dio.options.connectTimeout = const Duration(seconds: 120);
    _dio.options.receiveTimeout = const Duration(seconds: 120);
    _dio.options.sendTimeout = const Duration(seconds: 120);

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        developer.log('发送请求: ${options.method} ${options.path}');
        return handler.next(options);
      },
      onError: (DioException error, handler) async {
        developer.log('请求错误: ${error.message}');
        // 获取当前正在处理的笔记
        Note? currentNote;
        if (error.requestOptions.data is Map) {
          final requestData = error.requestOptions.data as Map;
          final prompt = requestData["messages"]?[0]?["content"];
          if (prompt != null) {
            currentNote = await DatabaseHelper.instance.getNoteByPrompt(prompt);
          }
        }
        if (error.response?.statusCode == 401) {
          // token 失效，执行登出操作
          await AuthService().logout();

          // 如果找到对应的笔记，更新其状态
          if (currentNote != null) {
            final updatedNote = currentNote.copyWith(
              status: Note.STATUS_NOT_LOGIN,
              errorMessage: AppLocalizations.instance.pleaseLoginFirst,
              updatedAt: DateTime.now(),
            )..isUpdating = false;

            await DatabaseHelper.instance.updateNote(updatedNote);
            NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));
          }

          // 发送登出事件
          NoteEvents.eventBus.fire(LoginStatusChangedEvent(false));
          Get.snackbar(AppLocalizations.instance.message, AppLocalizations.instance.loginExpired);
        } else if (error.response?.statusCode == 403) {
          // 配额不足，更新笔记状态
          if (currentNote != null) {
            final updatedNote = currentNote.copyWith(
              status: Note.STATUS_QUOTA_EXCEEDED, // 配额不足状态码
              errorMessage: AppLocalizations.instance.quotaExceeded,
              updatedAt: DateTime.now(),
            )..isUpdating = false;

            await DatabaseHelper.instance.updateNote(updatedNote);
            NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));
          }
        }

        return handler.next(error);
      },
    ));
  }

  // 合并后的方法
  Future<Note> askAI(Note note, {bool noUpdateContent = true, bool proMode = false}) async {
    Note updatedNote = note.copyWith(
      status: Note.STATUS_PENDING,
      errorMessage: '',
      updatedAt: DateTime.now(),
    )..isUpdating = true;

    final cancelToken = CancelToken();
    if (note.id != null) {
      _cancelTokens[note.id!] = cancelToken;
    }

    try {
      developer.log('askAI: ${note.prompt}');

      // 根据场景选择不同的系统提示词
      final systemPrompt =
          noUpdateContent ? _CLASSIFICATION_PROMPT : (proMode ? _CREATION_PROMPT_PRO : _CREATION_PROMPT);

      final response = await _dio.post(
        proMode ? '${AppConfig.apiBaseUrl}/chat/completionsPro' : '${AppConfig.apiBaseUrl}/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await AuthService().getToken()}',
            'Content-Type': 'application/json; charset=utf-8',
          },
        ),
        data: {
          "messages": [
            {"role": "system", "content": systemPrompt},
            {"role": "user", "content": note.prompt}
          ],
        },
        cancelToken: cancelToken,
      );

      if (response.statusCode == 200) {
        developer.log('response.data: ${response.data}');
        try {
          final cleanedContent = cleanAIResponse(json.encode(response.data));
          final Map<String, dynamic> aiResponseMap = json.decode(cleanedContent);

          // 获取分类图片（异步处理，不等待结果）
          if (aiResponseMap['category'] != null) {
            updatedNote = updatedNote.copyWith(
              category: aiResponseMap['category'],
            );
          }

          // 根据场景创建不同的更新笔记
          if (noUpdateContent) {
            // 仅分类场景：只更新标题和分类信息
            updatedNote = updatedNote.copyWith(
              title: aiResponseMap['title'],
              category: aiResponseMap['category'],
              subCategory: aiResponseMap['subCategory'],
              subSubCategory: aiResponseMap['subSubCategory'],
              status: Note.STATUS_SUCCESS,
              updatedAt: DateTime.now(),
            )..isUpdating = false;
          } else {
            // 创作场景：更新所有字段
            updatedNote = note.copyWith(
              title: aiResponseMap['title'],
              category: aiResponseMap['category'],
              subCategory: aiResponseMap['subCategory'],
              subSubCategory: aiResponseMap['subSubCategory'],
              content: MarkdownUtil.parseMarkdown('\n\n\n${aiResponseMap['content']}\n\n\n'),
              originalContent: MarkdownUtil.parseMarkdown('\n\n\n${aiResponseMap['content']}\n\n\n'),
              status: Note.STATUS_SUCCESS,
              retryCount: 0,
              errorMessage: '',
              updatedAt: DateTime.now(),
              recommendations: aiResponseMap['recommendations'] != null
                  ? (aiResponseMap['recommendations'] as List<dynamic>).map((e) => e.toString()).toList()
                  : [],
              reasoning: aiResponseMap['reasoning'] ?? '',
            )..isUpdating = false;
          }
          NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, true));
          await _saveQuotaToLocal(response.data);
        } catch (jsonError) {
          // JSON解析错误
          developer.log('JSON解析错误: $jsonError');
          developer.log('response.data: ${response.data}');
          updatedNote = note.copyWith(
            status: Note.STATUS_JSON_ERROR,
            errorMessage: '${AppLocalizations.instance.jsonParseError}: $jsonError',
            updatedAt: DateTime.now(),
          )..isUpdating = false;
          NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));
          await _handleQuotaRestore();
        }
      } else if (response.statusCode == 400) {
        // 用户询问了不支持的问题
        updatedNote = note.copyWith(
          status: Note.STATUS_UNSUPPORTED_QUESTION,
          errorMessage: AppLocalizations.instance.unsupportedQuestion,
          updatedAt: DateTime.now(),
        )..isUpdating = false;
        NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));
      } else if (response.statusCode == 403) {
        // 配额不足
        updatedNote = note.copyWith(
          status: Note.STATUS_QUOTA_EXCEEDED,
          errorMessage: AppLocalizations.instance.quotaExceeded,
          updatedAt: DateTime.now(),
        )..isUpdating = false;
        NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));
      } else {
        // 其他HTTP错误
        updatedNote = note.copyWith(
          status: Note.STATUS_HTTP_ERROR,
          errorMessage: AppLocalizations.instance.networkError,
          updatedAt: DateTime.now(),
        )..isUpdating = false;
        NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));
      }
    } catch (error) {
      if (error is DioException) {
        if (CancelToken.isCancel(error)) {
          updatedNote = note.copyWith(
            status: Note.STATUS_CANCELLED,
            errorMessage: AppLocalizations.instance.userCancel,
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        } else if (error.type == DioExceptionType.connectionTimeout) {
          updatedNote = note.copyWith(
            status: Note.STATUS_CONNECT_TIMEOUT,
            errorMessage: AppLocalizations.instance.networkError,
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        } else if (error.type == DioExceptionType.receiveTimeout) {
          updatedNote = note.copyWith(
            status: Note.STATUS_RECEIVE_TIMEOUT,
            errorMessage: AppLocalizations.instance.networkError,
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        } else {
          updatedNote = note.copyWith(
            status: Note.STATUS_NETWORK_ERROR,
            errorMessage: AppLocalizations.instance.unknownError,
            updatedAt: DateTime.now(),
          )..isUpdating = false;
        }
      } else {
        // 其他非网络错误
        updatedNote = note.copyWith(
          status: Note.STATUS_NETWORK_ERROR,
          errorMessage: '${AppLocalizations.instance.otherError}: $error',
          updatedAt: DateTime.now(),
        )..isUpdating = false;
      }
      NoteEvents.eventBus.fire(NoteUpdatedEvent(updatedNote, false));
    } finally {
      if (note.id != null) {
        _cancelTokens.remove(note.id);
      }
    }

    await DatabaseHelper.instance.updateNote(updatedNote);
    return updatedNote;
  }

  // 优化后的 cleanAIResponse 方法
  String cleanAIResponse(String aiResponse) {
    try {
      // 首先解析完整的API响应
      Map<String, dynamic> fullResponse = json.decode(aiResponse);
      String content = fullResponse['choices'][0]['message']['content'];
      String? reasoning = fullResponse['choices'][0]['message']['reasoning'];

      // 处理可能包含的 markdown 代码块
      if (content.contains('```json')) {
        final RegExp jsonBlockRegex = RegExp(r'```json\s*\n(.*?)\n\s*```', dotAll: true);
        final match = jsonBlockRegex.firstMatch(content);
        if (match != null) {
          content = match.group(1)!.trim();
        }
      }

      // 确保内容是JSON格式
      if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
        // 只处理 content 字段中的换行符和引号，保持其他字段不变
        content = content.replaceAllMapped(
          RegExp(r'"content":\s*"(.*?)"', dotAll: true),
          (match) {
            String matchContent = match.group(1)!;
            // 保留content字段中的换行符和格式
            matchContent = matchContent.replaceAll('"', '\\"').replaceAll(r'\\"', '\\"'); // 避免重复转义
            return '"content": "$matchContent"';
          },
        );

        // 只移除 JSON 结构中的换行符，不影响 content 字段的内容
        content = content
            .replaceAll(RegExp(r'\r?\n(?!"content")'), '') // 只替换不在 content 字段中的换行符
            .replaceAll(RegExp(r'\s+(?!"content")'), ' ') // 只替换不在 content 字段中的多余空白
            .trim();

        // 删除可能出现的latex公式的$$符号
        content = content.replaceAll('\$\$', '');

        // 验证最终的 JSON 是否可以正确解析
        Map<String, dynamic> jsonContent = json.decode(content);

        // 如果有reasoning字段，添加到JSON中，并且把reasoning添加到content前面
        if (reasoning != null) {
          // 存储原始的reasoning
          jsonContent['reasoning'] = reasoning;

          // 处理reasoning文本，转换成markdown引用格式
          // String formattedReasoning = reasoning
          //    .split('\n') // 按行分割
          //    .map((line) => line.trim().isNotEmpty ? '> $line\n' : '\n') // 每行前面加上引用符号
          //    .join(); // 合并所有行

          // String finalReasoning = '### 思考过程\n\n$formattedReasoning\n\n### 内容';

          // 在原有content前面添加处理后的reasoning
          if (jsonContent['content'] != null) {
            // jsonContent['content'] = '$finalReasoning\n\n${jsonContent['content']}'; // 暂时取消这个功能
            jsonContent['content'] = '\n\n\n${jsonContent['content']}';
          }

          content = json.encode(jsonContent);
        }
        return content;
      }
      throw Exception('AI响应不是有效的JSON格式');
    } catch (e) {
      throw Exception('JSON解析失败: $aiResponse');
    }
  }

  // 添加取消请求的方法
  void cancelRequest(int noteId) {
    final cancelToken = _cancelTokens[noteId];
    if (cancelToken != null) {
      if (!cancelToken.isCancelled) {
        cancelToken.cancel('用户取消了请求');
        _cancelTokens.remove(noteId);
      }
    } else {
      developer.log('未找到noteId=$noteId的cancelToken');
    }
  }

  // 在服务销毁时取消所有请求
  void dispose() {
    for (var cancelToken in _cancelTokens.values) {
      if (!cancelToken.isCancelled) {
        cancelToken.cancel('Service disposed');
      }
    }
    _cancelTokens.clear();
    _dio.close(force: true);
    _initDio(); // 重新初始化 Dio
  }

  // 新增重置方法，用于登录后调用
  void reset() {
    _dio.close(force: true);
    _initDio();
  }

  // 提取恢复配额的处理逻辑为私有方法
  Future<void> _handleQuotaRestore() async {
    try {
      final quotaInfo = await restoreQuota();
      developer.log('配额已恢复. 剩余配额: ${quotaInfo['remaining_quota']}');
    } catch (restoreError) {
      developer.log('恢复配额失败: $restoreError', error: restoreError);
    }
  }

  // 修改 restoreQuota 方法，添加基本的数据验证
  Future<Map<String, dynamic>> restoreQuota() async {
    try {
      final token = await AuthService().getToken();
      if (token == null) {
        throw Exception('未登录');
      }

      final response = await _dio.post(
        '${AppConfig.apiBaseUrl}/chat/restore-quota',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json; charset=utf-8',
          },
        ),
      );

      if (response.statusCode != 200) {
        throw Exception('恢复配额失败: ${response.statusCode}');
      }

      return response.data;
    } catch (e) {
      developer.log('恢复配额失败: $e', error: e);
      rethrow;
    }
  }

  // 在 AIService 中添加保存配额的方法
  Future<void> _saveQuotaToLocal(Map<String, dynamic> response) async {
    if (response.containsKey('remaining_quota')) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('remaining_quota', response['remaining_quota']);
    }
  }

  // 在 AIService 类中添加新方法
  Future<Map<String, dynamic>> generateQuizQuestions(String noteContent) async {
    // ignore: constant_identifier_names
    const String QUIZ_SYSTEM_PROMPT = '''
你是一个专业的题目生成助手，需要：
1. 根据内容生成有价值的测试题目
2. 题目难度要适中，考察要点明确
3. 选项设置要合理，具有区分度
4. 答案解析要详细且易懂
5. 除引用原文外，所有回答内容使用同一种语言
6. 必须返回标准的JSON格式
''';

    try {
      final response = await _dio.post(
        '${AppConfig.apiBaseUrl}/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await AuthService().getToken()}',
            'Content-Type': 'application/json; charset=utf-8',
          },
        ),
        data: {
          "messages": [
            {"role": "system", "content": QUIZ_SYSTEM_PROMPT},
            {"role": "user", "content": AppLocalizations.instance.getQuizQuestion(noteContent)}
          ],
          "response_format": {"type": "json_object"}
        },
      );

      if (response.statusCode == 200) {
        final cleanedContent = cleanAIResponse(json.encode(response.data));

        // 返回完整的响应数据
        return {
          'questions_json': cleanedContent,
          'remaining_quota': response.data['remaining_quota'],
        };
      } else {
        throw Exception('Failed to generate quiz: ${response.statusCode}');
      }
    } catch (e) {
      developer.log('Error generating quiz:', error: e);
      rethrow;
    }
  }

  // 修改 addQuota 方法
  Future<Map<String, dynamic>?> addQuota({int count = 5}) async {
    try {
      final response = await _dio.post(
        '${AppConfig.apiBaseUrl}/chat/add-quota',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await AuthService().getToken()}',
            'Content-Type': 'application/json; charset=utf-8',
          },
        ),
        data: {
          'count': count,
        },
      );

      if (response.statusCode == 200) {
        // 更新本地存储的配额
        final prefs = await SharedPreferences.getInstance();
        final remainingQuota = response.data['remaining_quota'] as int;
        await prefs.setInt('remaining_quota', remainingQuota);
        return response.data;
      }
      return null;
    } catch (e) {
      rethrow;
    }
  }

  // 在 AIService 类中添加方法
  Future<List<TranslationTrainingItem>> generateTranslationTraining(String noteContent) async {
    try {
      final prompt = AppConfig.translationTrainingPrompt.replaceAll('{noteContent}', noteContent);

      // 直接使用_dio发送请求，而不是使用_sendRequest方法
      String? token = await AuthService().getToken();
      if (token == null) {
        throw Exception('未登录');
      }

      final response = await _dio.post(
        '${AppConfig.apiBaseUrl}/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await AuthService().getToken()}',
            'Content-Type': 'application/json; charset=utf-8',
          },
        ),
        data: {
          'messages': [
            {'role': 'system', 'content': "你是一名英语教学专家，擅长为中文学习者创建简洁有效的英语学习材料。请始终以JSON对象格式返回响应。"},
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.7,
          'response_format': {"type": "json_object"}
        },
      );

      if (response.statusCode == 200) {
        // 从响应中提取内容
        final content = response.data['choices'][0]['message']['content'];
        print('AI返回的原始内容: $content');

        // 尝试解析JSON
        try {
          // 解析JSON对象
          final Map<String, dynamic> jsonData = jsonDecode(content);

          // 从JSON对象中提取trainingItems数组
          if (jsonData.containsKey('trainingItems')) {
            final List<dynamic> trainingItems = jsonData['trainingItems'];
            return trainingItems
                .map((item) => TranslationTrainingItem(
                      blackboardContent: item['blackboardContent'] ?? '',
                      teacherExplanation: item['teacherExplanation'] ?? '',
                    ))
                .toList();
          } else {
            throw Exception('返回的JSON对象中没有trainingItems字段');
          }
        } catch (jsonError) {
          print('JSON解析错误: $jsonError');
          print('原始内容: $content');
          throw Exception('无法解析AI返回的JSON数据');
        }
      } else if (response.statusCode == 403) {
        // 配额不足
        print('配额不足: 403 Forbidden');
        Get.snackbar(
          AppLocalizations.instance.message,
          AppLocalizations.instance.quotaExceeded,
          duration: Duration(seconds: 5),
        );
        throw Exception(AppLocalizations.instance.quotaExceeded);
      } else {
        throw Exception('请求失败: ${response.statusCode}');
      }
    } catch (e) {
      print('生成翻译训练内容失败: $e');
      // 如果是DioException，检查是否是403错误
      if (e is DioException && e.response?.statusCode == 403) {
        print('配额不足: 403 Forbidden');
        Get.snackbar(
          AppLocalizations.instance.message,
          AppLocalizations.instance.quotaExceeded,
          duration: Duration(seconds: 5),
        );
        throw Exception(AppLocalizations.instance.quotaExceeded);
      }
      rethrow;
    }
  }

  Future<Map<String, dynamic>> evaluateSentenceUnderstanding(String originalSentence, String userResponse) async {
    try {
      final prompt = AppConfig.sentenceUnderstandingPrompt
          .replaceAll('{originalSentence}', originalSentence)
          .replaceAll('{userResponse}', userResponse);

      final response = await _sendRequest(
        prompt: prompt,
        systemMessage: "你是一名精通中英文的语言评估专家。",
        temperature: 0.3,
        responseFormat: {"type": "json_object"},
      );

      // 解析评估内容
      final evaluationContent = jsonDecode(response['content']);

      // 添加剩余配额信息到返回结果中
      evaluationContent['remaining_quota'] = response['remaining_quota'];

      return evaluationContent;
    } catch (e) {
      print('评估句子理解失败: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> generateOverallEvaluation(
      List<String> teacherContent, Map<String, String> userResponses) async {
    try {
      final teacherText = teacherContent.join('\n');
      final userText = userResponses.entries.map((e) => "${e.key}: ${e.value}").join('\n');

      final prompt = AppConfig.overallEvaluationPrompt
          .replaceAll('{teacherContent}', teacherText)
          .replaceAll('{userResponses}', userText);

      final response = await _sendRequest(
        prompt: prompt,
        systemMessage: "你是一名英语教育专家，擅长评估学习效果并提供有建设性的反馈。",
        temperature: 0.5,
        responseFormat: {"type": "json_object"},
      );

      // 解析评估内容
      final evaluationContent = jsonDecode(response['content']);

      // 添加剩余配额信息到返回结果中
      evaluationContent['remaining_quota'] = response['remaining_quota'];

      return evaluationContent;
    } catch (e) {
      print('生成综合评价失败: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> _sendRequest({
    required String prompt,
    required String systemMessage,
    double temperature = 0.7,
    Map<String, String>? responseFormat,
  }) async {
    try {
      String? token = await AuthService().getToken();
      if (token == null) {
        throw Exception('未登录');
      }

      // 构建请求体
      final Map<String, dynamic> requestBody = {
        'messages': [
          {'role': 'system', 'content': systemMessage},
          {'role': 'user', 'content': prompt},
        ],
        'temperature': temperature,
      };

      // 添加响应格式（如果提供）
      if (responseFormat != null) {
        requestBody['response_format'] = responseFormat;
      }

      // 发送请求
      final response = await _dio.post(
        '${AppConfig.apiBaseUrl}/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json; charset=utf-8',
          },
        ),
        data: requestBody,
      );

      if (response.statusCode == 200) {
        print(response.data);

        // 创建返回结果Map，包含需要的信息
        final result = <String, dynamic>{
          'content': response.data['choices'][0]['message']['content'],
          'remaining_quota': response.data['remaining_quota'] ?? 0,
          'total_quota': response.data['total_quota'] ?? 0,
          'used_quota': response.data['used_quota'] ?? 0,
        };

        // 保存配额到本地
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt('remaining_quota', result['remaining_quota']);

        return result;
      } else if (response.statusCode == 403) {
        // 配额不足
        print('配额不足: 403 Forbidden');
        Get.snackbar(
          AppLocalizations.instance.message,
          AppLocalizations.instance.quotaExceeded,
          duration: Duration(seconds: 5),
        );
        throw Exception(AppLocalizations.instance.quotaExceeded);
      } else {
        throw Exception('请求失败: ${response.statusCode}');
      }
    } catch (e) {
      print('发送请求错误: $e');
      // 如果是DioException，检查是否是403错误
      if (e is DioException && e.response?.statusCode == 403) {
        print('配额不足: 403 Forbidden');
        Get.snackbar(
          AppLocalizations.instance.message,
          AppLocalizations.instance.quotaExceeded,
          duration: Duration(seconds: 5),
        );
        throw Exception(AppLocalizations.instance.quotaExceeded);
      }
      rethrow;
    }
  }
}
