// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:url_launcher/url_launcher.dart';

// Project imports:
import '../config.dart';

class VersionService {
  static final VersionService _instance = VersionService._internal();
  factory VersionService() => _instance;

  static const String _KEY_LATEST_VERSION = 'latest_version';
  static const String _KEY_FORCE_UPDATE = 'force_update';
  static const String _KEY_SHOWN_VERSION = 'shown_version_dialog'; // 记录已显示过对话框的版本

  // 获取当前版本号
  static String get currentVersion => AppConfig.CURRENT_VERSION;

  late SharedPreferences _prefs;
  final Dio _dio = Dio();

  VersionService._internal();

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // 检查是否需要强制更新
  Future<bool> checkForceUpdate() async {
    final String? latestVersion = _prefs.getString(_KEY_LATEST_VERSION);
    final bool forceUpdate = _prefs.getBool(_KEY_FORCE_UPDATE) ?? false;
    final String? shownVersion = _prefs.getString(_KEY_SHOWN_VERSION);

    debugPrint('checkForceUpdate - latestVersion: $latestVersion');
    debugPrint('checkForceUpdate - currentVersion: $currentVersion');
    debugPrint('checkForceUpdate - forceUpdate: $forceUpdate');
    debugPrint('checkForceUpdate - shownVersion: $shownVersion');

    // 如果没有获取到最新版本信息，不需要更新
    if (latestVersion == null) {
      debugPrint('checkForceUpdate - no latest version stored, returning false');
      return false;
    }

    // 如果已经显示过这个版本的对话框，不再显示
    if (shownVersion == latestVersion) {
      debugPrint('checkForceUpdate - already shown dialog for version $latestVersion, returning false');
      return false;
    }

    // 比较版本号，只有当最新版本大于当前版本时才需要更新
    final bool needsUpdate = _compareVersions(latestVersion, currentVersion) > 0;
    debugPrint('checkForceUpdate - needsUpdate: $needsUpdate (latest > current)');
    debugPrint('checkForceUpdate - final result: ${needsUpdate && forceUpdate}');

    return needsUpdate && forceUpdate;
  }

  // 比较两个版本号
  // 返回值：
  // 1: version1 > version2
  // 0: version1 = version2
  // -1: version1 < version2
  int _compareVersions(String version1, String version2) {
    final List<int> v1Parts = version1.split('.').map(int.parse).toList();
    final List<int> v2Parts = version2.split('.').map(int.parse).toList();

    for (int i = 0; i < v1Parts.length && i < v2Parts.length; i++) {
      if (v1Parts[i] > v2Parts[i]) {
        return 1;
      }
      if (v1Parts[i] < v2Parts[i]) {
        return -1;
      }
    }

    return v1Parts.length.compareTo(v2Parts.length);
  }

  // 在后台检查更新
  Future<void> checkUpdateInBackground() async {
    try {
      final baseUrl =
          AppConfig.environment == Environment.dev ? 'http://localhost:8080' : 'https://martina-server.onrender.com';
      final response = await _dio.get('$baseUrl/status');

      if (response.statusCode == 200) {
        final data = response.data;
        final String latestVersion = data['latest_version'];
        final bool forceUpdate = data['force_update'];

        debugPrint('checkUpdateInBackground - received latestVersion: $latestVersion');
        debugPrint('checkUpdateInBackground - received forceUpdate: $forceUpdate');

        // 使用 _compareVersions 方法来比较版本号
        final needsUpdate = _compareVersions(latestVersion, currentVersion) > 0;
        debugPrint('checkUpdateInBackground - needsUpdate: $needsUpdate');

        if (needsUpdate) {
          await _prefs.setString(_KEY_LATEST_VERSION, latestVersion);
          await _prefs.setBool(_KEY_FORCE_UPDATE, forceUpdate);
          debugPrint('checkUpdateInBackground - saved update info');
        } else {
          // 如果不需要更新，清除之前的更新信息
          await clearUpdateInfo();
          debugPrint('checkUpdateInBackground - cleared update info (no update needed)');
        }
      }
    } catch (e) {
      debugPrint('检查更新失败: $e');
      // 如果网络请求失败，清除可能存在的旧更新信息
      await clearUpdateInfo();
      debugPrint('checkUpdateInBackground - cleared update info due to error');
    }
  }

  // 打开官网
  Future<void> openOfficialWebsite() async {
    try {
      final Uri url = Uri.parse('https://tempognize.click');
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } catch (e) {
      debugPrint('打开官网失败: $e');
    }
  }

  // 清除更新信息
  Future<void> clearUpdateInfo() async {
    await _prefs.remove(_KEY_LATEST_VERSION);
    await _prefs.remove(_KEY_FORCE_UPDATE);
  }

  // 标记版本对话框已显示
  Future<void> markVersionDialogShown(String version) async {
    await _prefs.setString(_KEY_SHOWN_VERSION, version);
    debugPrint('markVersionDialogShown - marked version $version as shown');
  }

  // 比较两个版本号（公开方法）
  int compareVersions(String version1, String version2) => _compareVersions(version1, version2);
}
