import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import 'enhanced_ai_service.dart';

class TokenStatus {
  final bool valid;
  final DateTime? expiresAt;
  final int? userId;

  TokenStatus({
    required this.valid,
    this.expiresAt,
    this.userId,
  });

  factory TokenStatus.fromJson(Map<String, dynamic> json) {
    return TokenStatus(
      valid: json['valid'] ?? false,
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at']) : null,
      userId: json['user_id'],
    );
  }
}

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _emailKey = 'user_email';
  static const String _lastEmailKey = 'last_email';

  // 单例模式
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  Future<bool> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.apiBaseUrl}/users/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        await _saveToken(data['token']);
        await _saveEmail(email);
        await _saveLastEmail(email);

        // 保存配额信息
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt('remaining_quota', data['remaining_quota']);
        await prefs.setInt('total_quota', data['total_quota']);
        await prefs.setInt('used_quota', data['used_quota']);
        await prefs.setInt('quota_level', data['level']);

        // Reset enhanced AI service
        if (Get.isRegistered<EnhancedAIService>()) {
          EnhancedAIService.instance.reset();
        }

        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Login error: $e');
      rethrow;
    }
  }

  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  Future<void> _saveEmail(String email) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_emailKey, email);
  }

  Future<String?> getUserEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_emailKey);
  }

  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.remove(_tokenKey),
        prefs.remove(_emailKey),
        // 清除其他相关数据
        prefs.remove('remaining_quota'),
        prefs.remove('total_quota'),
        prefs.remove('used_quota'),
        prefs.remove('quota_level'),
      ]);
    } catch (e) {
      debugPrint('Logout error: $e');
      rethrow;
    }
  }

  Future<TokenStatus> checkTokenStatus() async {
    try {
      final token = await getToken();
      if (token == null) {
        return TokenStatus(valid: false);
      }

      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/users/profile'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        return TokenStatus(valid: true);
      }

      return TokenStatus(valid: false);
    } catch (e) {
      if (e is DioException && e.response?.statusCode == 401) {
        return TokenStatus(valid: false);
      }
      rethrow;
    }
  }

  Future<int> getRemainingQuota() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt('remaining_quota') ?? 0;
  }

  // 检查用户是否已登录
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null;
  }

  Future<void> _saveLastEmail(String email) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastEmailKey, email);
  }

  Future<String?> getLastEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_lastEmailKey);
  }

  Future<Dio> _getDio() async {
    final dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: const Duration(seconds: 5),
      receiveTimeout: const Duration(seconds: 10),
    ));

    final token = await getToken();
    if (token != null) {
      dio.options.headers['Authorization'] = 'Bearer $token';
    }

    return dio;
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      if (error.response?.data is Map) {
        return error.response?.data['error']?.toString() ?? error.message ?? '未知错误';
      }
      return error.message ?? '网络请求错误';
    }
    return error.toString();
  }

  Future<void> deleteAccount() async {
    final dio = await _getDio();
    try {
      await dio.delete('/users/account');
      await logout();
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> clearStoredEmail() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_lastEmailKey);
  }
}

// Token 状态响应模型
class TokenStatusResponse {
  final bool valid;

  TokenStatusResponse({required this.valid});
}
