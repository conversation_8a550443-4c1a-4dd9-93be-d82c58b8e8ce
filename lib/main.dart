// Flutter imports:
import 'package:flutter/material.dart';
// Package imports:
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';

// Project imports:
import 'l10n/localization.dart';
import 'routes/app_routes.dart';
import 'services/startup_service.dart';
import 'services/app_settings.dart';
import 'core/services/dialog_queue_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化对话框队列管理器
  Get.put(DialogQueueManager(), permanent: true);

  // 初始化启动服务
  Get.put(StartupService(), permanent: true);

  runApp(const MainApp());
}

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final startupService = StartupService.instance;
    switch (state) {
      case AppLifecycleState.resumed:
        startupService.onAppResumed();
        break;
      case AppLifecycleState.paused:
        startupService.onAppPaused();
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StartupService>(
      init: StartupService.instance,
      builder: (startupService) {
        return GetMaterialApp(
          title: 'Tempognize',
          debugShowCheckedModeBanner: false,
          theme: startupService.isAppInitialized && Get.isRegistered<AppSettings>()
              ? Get.find<AppSettings>().theme
              : ThemeData.light(),
          builder: (context, child) {
            // 初始化工具类
            AppLocalizations.init(context);
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                padding: MediaQuery.of(context).padding.copyWith(bottom: 0),
                viewPadding: MediaQuery.of(context).viewPadding.copyWith(bottom: 0),
                viewInsets: MediaQuery.of(context).viewInsets.copyWith(bottom: 0),
              ),
              child: child!,
            );
          },
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('zh', ''),
            Locale('en', ''),
          ],
          locale: startupService.isAppInitialized && Get.isRegistered<AppSettings>()
              ? Get.find<AppSettings>().locale
              : const Locale('zh', ''),
          initialRoute: startupService.shouldShowSplash ? Routes.splash : Routes.main,
          getPages: AppPages.pages,
          defaultTransition: Transition.native,
          transitionDuration: const Duration(milliseconds: 230),
          enableLog: true,
        );
      },
    );
  }
}
