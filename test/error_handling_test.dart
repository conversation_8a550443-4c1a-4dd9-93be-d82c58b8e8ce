// Comprehensive tests for the enterprise-grade error handling system
// Tests all error scenarios, retry mechanisms, and offline functionality

import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart';
import '../lib/core/error_handling/app_error.dart';
import '../lib/core/error_handling/retry_manager.dart';
import '../lib/core/error_handling/error_handler_service.dart';
import '../lib/core/offline/offline_queue_manager.dart';

void main() {
  group('Error Handling System Tests', () {
    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
    });

    tearDown(() {
      // Clean up after each test
      Get.reset();
    });

    group('AppError Tests', () {
      test('NetworkError should be created correctly', () {
        final error = NetworkError(
          type: NetworkErrorType.timeout,
          code: 'NETWORK_TIMEOUT',
          message: 'Request timed out',
          userMessage: 'Connection timeout',
          statusCode: 408,
        );

        expect(error.type, NetworkErrorType.timeout);
        expect(error.code, 'NETWORK_TIMEOUT');
        expect(error.isRetryable, true);
        expect(error.severity, ErrorSeverity.medium);
        expect(error.maxRetries, 3);
      });

      test('BusinessError should not be retryable', () {
        final error = BusinessError(
          type: BusinessErrorType.quotaExceeded,
          code: 'QUOTA_EXCEEDED',
          message: 'API quota exceeded',
          userMessage: 'You have exceeded your quota',
        );

        expect(error.isRetryable, false);
        expect(error.maxRetries, 0);
        expect(error.severity, ErrorSeverity.medium);
      });

      test('SystemError should handle database errors correctly', () {
        final error = SystemError(
          type: SystemErrorType.databaseError,
          code: 'DATABASE_ERROR',
          message: 'Database connection failed',
          userMessage: 'Data storage error',
        );

        expect(error.isRetryable, true);
        expect(error.severity, ErrorSeverity.critical);
        expect(error.maxRetries, 2);
      });

      test('ErrorFactory should create correct error from DioException', () {
        final dioException = DioException(
          requestOptions: RequestOptions(path: '/test'),
          type: DioExceptionType.connectionTimeout,
        );

        final error = ErrorFactory.fromException(dioException);

        expect(error, isA<NetworkError>());
        final networkError = error as NetworkError;
        expect(networkError.type, NetworkErrorType.timeout);
        expect(networkError.code, 'NETWORK_TIMEOUT');
        expect(networkError.isRetryable, true);
      });
    });

    group('RetryManager Tests', () {
      test('should retry failed operations with exponential backoff', () async {
        int attemptCount = 0;
        
        final result = await RetryManager.execute(
          () async {
            attemptCount++;
            if (attemptCount < 3) {
              throw NetworkError(
                type: NetworkErrorType.timeout,
                code: 'TEST_TIMEOUT',
                message: 'Test timeout',
                userMessage: 'Test timeout',
              );
            }
            return 'success';
          },
          config: const RetryConfig(
            maxAttempts: 3,
            baseDelay: Duration(milliseconds: 10),
          ),
        );

        expect(result.isSuccess, true);
        expect(result.data, 'success');
        expect(result.attemptCount, 3);
        expect(attemptCount, 3);
      });

      test('should not retry non-retryable errors', () async {
        int attemptCount = 0;
        
        final result = await RetryManager.execute(
          () async {
            attemptCount++;
            throw BusinessError(
              type: BusinessErrorType.quotaExceeded,
              code: 'QUOTA_EXCEEDED',
              message: 'Quota exceeded',
              userMessage: 'Quota exceeded',
            );
          },
          config: const RetryConfig(maxAttempts: 3),
        );

        expect(result.isSuccess, false);
        expect(result.attemptCount, 3);
        expect(attemptCount, 1); // Should only attempt once
      });

      test('circuit breaker should prevent calls when open', () async {
        const circuitBreakerKey = 'test_circuit';
        
        // Trigger circuit breaker by failing multiple times
        for (int i = 0; i < 6; i++) {
          await RetryManager.execute(
            () async => throw NetworkError(
              type: NetworkErrorType.serverError,
              code: 'SERVER_ERROR',
              message: 'Server error',
              userMessage: 'Server error',
            ),
            circuitBreakerKey: circuitBreakerKey,
            config: const RetryConfig(maxAttempts: 1),
          );
        }

        // Next call should fail immediately due to circuit breaker
        final result = await RetryManager.execute(
          () async => 'success',
          circuitBreakerKey: circuitBreakerKey,
          config: const RetryConfig(maxAttempts: 1),
        );

        expect(result.isSuccess, false);
        expect(result.error?.code, 'CIRCUIT_BREAKER_OPEN');
      });
    });

    group('OfflineQueueManager Tests', () {
      late OfflineQueueManager queueManager;

      setUp(() {
        queueManager = OfflineQueueManager();
        Get.put(queueManager);
      });

      test('should enqueue operations correctly', () async {
        final operation = QueuedOperation(
          id: 'test_1',
          type: OperationType.askAI,
          priority: OperationPriority.normal,
          data: {'test': 'data'},
          createdAt: DateTime.now(),
        );

        await queueManager.enqueue(operation);

        expect(queueManager.queueLength, 1);
        expect(queueManager.queue.first.id, 'test_1');
      });

      test('should sort queue by priority', () async {
        final lowPriorityOp = QueuedOperation(
          id: 'low',
          type: OperationType.askAI,
          priority: OperationPriority.low,
          data: {},
          createdAt: DateTime.now(),
        );

        final highPriorityOp = QueuedOperation(
          id: 'high',
          type: OperationType.askAI,
          priority: OperationPriority.high,
          data: {},
          createdAt: DateTime.now(),
        );

        await queueManager.enqueue(lowPriorityOp);
        await queueManager.enqueue(highPriorityOp);

        expect(queueManager.queue.first.id, 'high');
        expect(queueManager.queue.last.id, 'low');
      });

      test('should register and execute operations', () async {
        bool executorCalled = false;
        
        queueManager.registerExecutor(OperationType.askAI, (operation) async {
          executorCalled = true;
          return 'executed';
        });

        final operation = QueuedOperation(
          id: 'test_exec',
          type: OperationType.askAI,
          priority: OperationPriority.normal,
          data: {},
          createdAt: DateTime.now(),
        );

        await queueManager.enqueue(operation);
        
        // Simulate connectivity and processing
        // In real implementation, this would be triggered by connectivity changes
        
        expect(executorCalled, false); // Should not execute immediately when offline
      });
    });

    group('Error Integration Tests', () {
      test('should handle complete error flow', () async {
        // Simulate a network error that should be retried
        int attemptCount = 0;
        
        final result = await RetryPatterns.networkOperation(() async {
          attemptCount++;
          if (attemptCount == 1) {
            throw DioException(
              requestOptions: RequestOptions(path: '/test'),
              type: DioExceptionType.connectionTimeout,
            );
          }
          return 'success';
        });

        expect(result.isSuccess, true);
        expect(result.data, 'success');
        expect(attemptCount, 2);
      });

      test('should handle authentication errors correctly', () async {
        final result = await RetryPatterns.networkOperation(() async {
          throw DioException(
            requestOptions: RequestOptions(path: '/test'),
            response: Response(
              requestOptions: RequestOptions(path: '/test'),
              statusCode: 401,
            ),
          );
        });

        expect(result.isSuccess, false);
        expect(result.error, isA<NetworkError>());
        
        final networkError = result.error as NetworkError;
        expect(networkError.type, NetworkErrorType.unauthorized);
        expect(networkError.isRetryable, false);
      });

      test('should handle quota exceeded errors', () async {
        final result = await RetryPatterns.networkOperation(() async {
          throw DioException(
            requestOptions: RequestOptions(path: '/test'),
            response: Response(
              requestOptions: RequestOptions(path: '/test'),
              statusCode: 403,
            ),
          );
        });

        expect(result.isSuccess, false);
        expect(result.error, isA<NetworkError>());
        
        final networkError = result.error as NetworkError;
        expect(networkError.type, NetworkErrorType.forbidden);
      });
    });

    group('Error Serialization Tests', () {
      test('should serialize and deserialize errors correctly', () {
        final originalError = NetworkError(
          type: NetworkErrorType.timeout,
          code: 'NETWORK_TIMEOUT',
          message: 'Request timed out',
          userMessage: 'Connection timeout',
          statusCode: 408,
          context: {'url': '/test', 'method': 'POST'},
        );

        final json = originalError.toJson();
        expect(json['code'], 'NETWORK_TIMEOUT');
        expect(json['type'], 'timeout');
        expect(json['statusCode'], 408);
        expect(json['context']['url'], '/test');
      });

      test('should handle error context correctly', () {
        final error = SystemError(
          type: SystemErrorType.parseError,
          code: 'PARSE_ERROR',
          message: 'Failed to parse JSON',
          userMessage: 'Data format error',
          context: {
            'input': '{"invalid": json}',
            'line': 1,
            'column': 15,
          },
        );

        final json = error.toJson();
        expect(json['context']['input'], '{"invalid": json}');
        expect(json['context']['line'], 1);
        expect(json['context']['column'], 15);
      });
    });

    group('Performance Tests', () {
      test('should handle high volume of errors efficiently', () async {
        final stopwatch = Stopwatch()..start();
        
        // Create 1000 errors
        for (int i = 0; i < 1000; i++) {
          ErrorFactory.fromException(
            Exception('Test error $i'),
            context: 'Performance test',
          );
        }
        
        stopwatch.stop();
        
        // Should complete within reasonable time (adjust threshold as needed)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      test('retry manager should handle concurrent operations', () async {
        final futures = <Future>[];
        
        for (int i = 0; i < 10; i++) {
          futures.add(RetryManager.execute(
            () async {
              await Future.delayed(const Duration(milliseconds: 10));
              return 'success_$i';
            },
            config: const RetryConfig(maxAttempts: 2),
          ));
        }
        
        final results = await Future.wait(futures);
        
        expect(results.length, 10);
        for (int i = 0; i < 10; i++) {
          final result = results[i] as RetryResult;
          expect(result.isSuccess, true);
          expect(result.data, 'success_$i');
        }
      });
    });
  });
}
