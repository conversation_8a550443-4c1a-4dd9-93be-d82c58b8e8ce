// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Package imports:
import 'package:get/get.dart';

// Project imports:
import 'package:tempognize/core/services/dialog_queue_manager.dart';

void main() {
  group('DialogQueueManager Tests', () {
    late DialogQueueManager dialogManager;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      dialogManager = DialogQueueManager();
      dialogManager.enableTestMode(); // Prevent automatic dialog processing
      Get.put(dialogManager, permanent: true);
    });

    tearDown(() {
      Get.reset();
    });

    test('should initialize correctly', () {
      expect(dialogManager.isInitialized, isTrue);
      expect(dialogManager.isDialogShowing, isFalse);
    });

    test('should enqueue dialogs correctly', () {
      final config1 = DialogConfig(
        type: DialogType.versionUpdate,
        id: 'test1',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Test 1')),
      );

      final config2 = DialogConfig(
        type: DialogType.notification,
        id: 'test2',
        priority: 2,
        builder: (context) => const AlertDialog(title: Text('Test 2')),
      );

      dialogManager.enqueueDialog(config1);
      dialogManager.enqueueDialog(config2);

      // Should not add duplicate dialogs
      dialogManager.enqueueDialog(config1);

      expect(dialogManager.dialogQueue.length, equals(2));
      // Should be sorted by priority (lower number = higher priority)
      expect(dialogManager.dialogQueue.first.id, equals('test1'));
      expect(dialogManager.dialogQueue.last.id, equals('test2'));
    });

    test('should remove dialogs correctly', () {
      final config = DialogConfig(
        type: DialogType.versionUpdate,
        id: 'test',
        builder: (context) => const AlertDialog(title: Text('Test')),
      );

      dialogManager.enqueueDialog(config);
      expect(dialogManager.dialogQueue.length, equals(1));

      dialogManager.removeDialog('test');
      expect(dialogManager.dialogQueue.length, equals(0));
    });

    test('should clear queue correctly', () {
      final config1 = DialogConfig(
        type: DialogType.versionUpdate,
        id: 'test1',
        builder: (context) => const AlertDialog(title: Text('Test 1')),
      );

      final config2 = DialogConfig(
        type: DialogType.notification,
        id: 'test2',
        builder: (context) => const AlertDialog(title: Text('Test 2')),
      );

      dialogManager.enqueueDialog(config1);
      dialogManager.enqueueDialog(config2);
      expect(dialogManager.dialogQueue.length, equals(2));

      dialogManager.clearQueue();
      expect(dialogManager.dialogQueue.length, equals(0));
    });

    test('should sort dialogs by priority', () {
      final config1 = DialogConfig(
        type: DialogType.notification,
        id: 'low_priority',
        priority: 10,
        builder: (context) => const AlertDialog(title: Text('Low Priority')),
      );

      final config2 = DialogConfig(
        type: DialogType.versionUpdate,
        id: 'high_priority',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('High Priority')),
      );

      final config3 = DialogConfig(
        type: DialogType.tutorial,
        id: 'medium_priority',
        priority: 5,
        builder: (context) => const AlertDialog(title: Text('Medium Priority')),
      );

      dialogManager.enqueueDialog(config1);
      dialogManager.enqueueDialog(config2);
      dialogManager.enqueueDialog(config3);

      expect(dialogManager.dialogQueue.length, equals(3));
      expect(dialogManager.dialogQueue[0].id, equals('high_priority'));
      expect(dialogManager.dialogQueue[1].id, equals('medium_priority'));
      expect(dialogManager.dialogQueue[2].id, equals('low_priority'));
    });
  });
}
