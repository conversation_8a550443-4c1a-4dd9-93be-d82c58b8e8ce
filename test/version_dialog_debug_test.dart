// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Package imports:
import 'package:get/get.dart';

// Project imports:
import 'package:tempognize/core/services/dialog_queue_manager.dart';

void main() {
  group('Version Dialog Debug Tests', () {
    late DialogQueueManager dialogManager;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      dialogManager = DialogQueueManager();
      dialogManager.enableTestMode(); // Prevent automatic dialog processing
      Get.put(dialogManager, permanent: true);
    });

    tearDown(() {
      Get.reset();
    });

    test('should show detailed debug info when version dialog is NOT queued', () async {
      print('\n=== Testing scenario where version dialog should NOT be shown ===');
      
      // This simulates the scenario from your logs:
      // - Server returns version 1.0.8
      // - Current version is 1.1.4
      // - needsUpdate = false (1.0.8 < 1.1.4)
      // - Dialog should NOT be queued
      
      // Verify no version dialogs are in queue initially
      final versionDialogs = dialogManager.dialogQueue
          .where((dialog) => dialog.type == DialogType.versionUpdate)
          .toList();
      
      expect(versionDialogs.length, equals(0));
      print('✅ Confirmed: No version dialogs in queue initially');
      
      // Verify total queue is empty
      expect(dialogManager.dialogQueue.length, equals(0));
      print('✅ Confirmed: Dialog queue is empty');
      
      print('=== Test completed successfully ===\n');
    });

    test('should show what happens when version dialog IS queued', () async {
      print('\n=== Testing scenario where version dialog SHOULD be shown ===');
      
      // This simulates what would happen if there was a real update:
      // - Server returns version 1.2.0
      // - Current version is 1.1.4
      // - needsUpdate = true (1.2.0 > 1.1.4)
      // - forceUpdate = true
      // - Dialog SHOULD be queued
      
      // Manually add a version dialog to see the debug output
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update',
        priority: 1,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));
      
      // Verify version dialog was queued
      final versionDialogs = dialogManager.dialogQueue
          .where((dialog) => dialog.type == DialogType.versionUpdate)
          .toList();
      
      expect(versionDialogs.length, equals(1));
      expect(versionDialogs.first.id, equals('version_update'));
      print('✅ Confirmed: Version dialog was queued successfully');
      
      // Verify it's first in queue (highest priority)
      expect(dialogManager.dialogQueue.first.id, equals('version_update'));
      print('✅ Confirmed: Version dialog has highest priority');
      
      print('=== Test completed successfully ===\n');
    });

    test('should demonstrate the difference between the two scenarios', () async {
      print('\n=== Demonstrating the key difference ===');
      
      // Scenario 1: No update needed (your current situation)
      print('Scenario 1: Server version 1.0.8, Current version 1.1.4');
      print('- needsUpdate = false (1.0.8 < 1.1.4)');
      print('- Dialog should NOT be queued');
      print('- Expected result: No version dialog appears');
      
      expect(dialogManager.dialogQueue.length, equals(0));
      print('✅ Queue is empty - no dialog will be shown');
      
      // Scenario 2: Update needed
      print('\nScenario 2: Server version 1.2.0, Current version 1.1.4');
      print('- needsUpdate = true (1.2.0 > 1.1.4)');
      print('- forceUpdate = true');
      print('- Dialog SHOULD be queued');
      print('- Expected result: Version dialog appears');
      
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update_scenario2',
        priority: 1,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));
      
      expect(dialogManager.dialogQueue.length, equals(1));
      print('✅ Queue has 1 dialog - version dialog will be shown');
      
      print('\n=== Key insight ===');
      print('If you are seeing a version dialog when you should not,');
      print('it means the dialog is being queued from somewhere else,');
      print('or there is a timing/caching issue.');
      
      print('=== Test completed successfully ===\n');
    });
  });
}
