// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Package imports:
import 'package:get/get.dart';

// Project imports:
import 'package:tempognize/core/services/dialog_queue_manager.dart';
import 'package:tempognize/services/startup_service.dart';

void main() {
  group('Startup Dialog Integration Tests', () {
    late DialogQueueManager dialogManager;
    late StartupService startupService;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      
      // Initialize services
      dialogManager = DialogQueueManager();
      dialogManager.enableTestMode(); // Prevent automatic dialog processing
      Get.put(dialogManager, permanent: true);
      
      startupService = StartupService();
      Get.put(startupService, permanent: true);
    });

    tearDown(() {
      Get.reset();
    });

    test('should initialize DialogQueueManager correctly', () {
      expect(dialogManager.isInitialized, isTrue);
      expect(dialogManager.isDialogShowing, isFalse);
      expect(dialogManager.dialogQueue.length, equals(0));
    });

    test('should handle startup service initialization', () {
      expect(startupService.isAppInitialized, isFalse);
      expect(startupService.shouldShowSplash, isTrue);
    });

    test('should register services correctly in GetX', () {
      expect(Get.isRegistered<DialogQueueManager>(), isTrue);
      expect(Get.isRegistered<StartupService>(), isTrue);
    });

    test('should have correct dialog priorities defined', () {
      // Test that our priority system is correctly defined
      const versionPriority = 1;    // Highest priority
      const notificationPriority = 2;
      const tutorialPriority = 3;
      const permissionPriority = 4; // Lowest priority

      expect(versionPriority < notificationPriority, isTrue);
      expect(notificationPriority < tutorialPriority, isTrue);
      expect(tutorialPriority < permissionPriority, isTrue);
    });

    test('should handle dialog queue operations', () {
      // Test basic queue operations
      expect(dialogManager.dialogQueue.isEmpty, isTrue);

      // Add a test dialog
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'test_dialog',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Test')),
      ));

      expect(dialogManager.dialogQueue.length, equals(1));
      expect(dialogManager.dialogQueue.first.id, equals('test_dialog'));

      // Remove dialog
      dialogManager.removeDialog('test_dialog');
      expect(dialogManager.dialogQueue.isEmpty, isTrue);
    });

    test('should maintain dialog order with mixed priorities', () {
      // Add dialogs in random order
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'permission',
        priority: 4,
        builder: (context) => const AlertDialog(title: Text('Permission')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Version')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.notification,
        id: 'notification',
        priority: 2,
        builder: (context) => const AlertDialog(title: Text('Notification')),
      ));

      // Should be sorted by priority
      final queue = dialogManager.dialogQueue;
      expect(queue.length, equals(4));
      expect(queue[0].id, equals('version'));      // Priority 1
      expect(queue[1].id, equals('notification')); // Priority 2
      expect(queue[2].id, equals('tutorial'));     // Priority 3
      expect(queue[3].id, equals('permission'));   // Priority 4
    });

    test('should handle test mode correctly', () {
      // Test mode should be enabled
      expect(dialogManager.isDialogShowing, isFalse);
      
      // Add a dialog - should not auto-process in test mode
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'test',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Test')),
      ));

      // Dialog should remain in queue
      expect(dialogManager.dialogQueue.length, equals(1));
      expect(dialogManager.isDialogShowing, isFalse);
    });
  });
}
