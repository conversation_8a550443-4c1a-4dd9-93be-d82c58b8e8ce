// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Package imports:
import 'package:get/get.dart';

// Project imports:
import 'package:tempognize/core/services/dialog_queue_manager.dart';

void main() {
  group('Startup Without Microphone Permission Tests', () {
    late DialogQueueManager dialogManager;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      dialogManager = DialogQueueManager();
      dialogManager.enableTestMode(); // Prevent automatic dialog processing
      Get.put(dialogManager, permanent: true);
    });

    tearDown(() {
      Get.reset();
    });

    test('should not include microphone permission in startup sequence', () async {
      // Simulate the startup dialog initialization
      await dialogManager.initializeStartupDialogs();

      // Verify that no microphone permission dialog was added during startup
      final microphoneDialogs = dialogManager.dialogQueue
          .where((dialog) => dialog.type == DialogType.permission)
          .toList();

      expect(microphoneDialogs.length, equals(0));
    });

    test('should only have version, notification, and tutorial dialogs in startup', () async {
      // Add some test dialogs to simulate what would happen during startup
      // (Note: In real app, these would be added conditionally based on actual conditions)
      
      // Simulate version dialog (if update available)
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));

      // Simulate notification dialog (if new notifications)
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.notification,
        id: 'notification',
        priority: 2,
        builder: (context) => const AlertDialog(title: Text('Notification')),
      ));

      // Simulate tutorial dialog (if should show)
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      // Verify only these three types are present (no permission dialogs)
      expect(dialogManager.dialogQueue.length, equals(3));
      
      final dialogTypes = dialogManager.dialogQueue.map((d) => d.type).toList();
      expect(dialogTypes.contains(DialogType.versionUpdate), isTrue);
      expect(dialogTypes.contains(DialogType.notification), isTrue);
      expect(dialogTypes.contains(DialogType.tutorial), isTrue);
      expect(dialogTypes.contains(DialogType.permission), isFalse);
    });

    test('should maintain correct priority order without microphone permission', () {
      // Add dialogs in random order
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.notification,
        id: 'notification',
        priority: 2,
        builder: (context) => const AlertDialog(title: Text('Notification')),
      ));

      // Should be sorted by priority: Version (1) → Notification (2) → Tutorial (3)
      expect(dialogManager.dialogQueue.length, equals(3));
      expect(dialogManager.dialogQueue[0].id, equals('version_update'));
      expect(dialogManager.dialogQueue[1].id, equals('notification'));
      expect(dialogManager.dialogQueue[2].id, equals('tutorial'));
    });

    test('should allow microphone permission to be added manually when needed', () {
      // This simulates what would happen when user tries to record
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      ));

      expect(dialogManager.dialogQueue.length, equals(1));
      expect(dialogManager.dialogQueue.first.type, equals(DialogType.permission));
      expect(dialogManager.dialogQueue.first.id, equals('microphone_permission'));
    });

    test('should handle mixed startup and permission dialogs correctly', () {
      // Add startup dialogs
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      // Later, user tries to record and permission dialog is added
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      ));

      // Should be sorted correctly: Version (1) → Tutorial (3) → Permission (4)
      expect(dialogManager.dialogQueue.length, equals(3));
      expect(dialogManager.dialogQueue[0].id, equals('version_update'));
      expect(dialogManager.dialogQueue[1].id, equals('tutorial'));
      expect(dialogManager.dialogQueue[2].id, equals('microphone_permission'));
    });
  });
}
