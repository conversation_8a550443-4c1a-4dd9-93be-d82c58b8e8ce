// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Package imports:
import 'package:get/get.dart';

// Project imports:
import 'package:tempognize/core/services/dialog_queue_manager.dart';

void main() {
  group('Sequential Dialog Flow Tests', () {
    late DialogQueueManager dialogManager;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      dialogManager = DialogQueueManager();
      dialogManager.enableTestMode(); // Prevent automatic dialog processing
      Get.put(dialogManager, permanent: true);
    });

    tearDown(() {
      Get.reset();
    });

    test('should queue dialogs in correct priority order', () {
      // Simulate the startup dialog sequence
      
      // Add tutorial dialog (priority 3)
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      // Add version update dialog (priority 1 - highest)
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));

      // Add notification dialog (priority 2)
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.notification,
        id: 'notification',
        priority: 2,
        builder: (context) => const AlertDialog(title: Text('Notification')),
      ));

      // Add microphone permission (priority 4 - lowest)
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4,
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      ));

      // Verify correct order: version (1), notification (2), tutorial (3), permission (4)
      expect(dialogManager.dialogQueue.length, equals(4));
      expect(dialogManager.dialogQueue[0].id, equals('version_update'));
      expect(dialogManager.dialogQueue[1].id, equals('notification'));
      expect(dialogManager.dialogQueue[2].id, equals('tutorial'));
      expect(dialogManager.dialogQueue[3].id, equals('microphone_permission'));
    });

    test('should prevent duplicate dialogs', () {
      // Add the same dialog twice
      final config = DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      );

      dialogManager.enqueueDialog(config);
      dialogManager.enqueueDialog(config); // Duplicate

      // Should only have one dialog
      expect(dialogManager.dialogQueue.length, equals(1));
      expect(dialogManager.dialogQueue[0].id, equals('tutorial'));
    });

    test('should handle dialog types correctly', () {
      // Add different types of dialogs
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Version')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'permission',
        priority: 4,
        builder: (context) => const AlertDialog(title: Text('Permission')),
      ));

      // Verify types are preserved
      expect(dialogManager.dialogQueue[0].type, equals(DialogType.versionUpdate));
      expect(dialogManager.dialogQueue[1].type, equals(DialogType.tutorial));
      expect(dialogManager.dialogQueue[2].type, equals(DialogType.permission));
    });

    test('should clear queue correctly', () {
      // Add multiple dialogs
      for (int i = 0; i < 3; i++) {
        dialogManager.enqueueDialog(DialogConfig(
          type: DialogType.custom,
          id: 'dialog_$i',
          priority: i,
          builder: (context) => AlertDialog(title: Text('Dialog $i')),
        ));
      }

      expect(dialogManager.dialogQueue.length, equals(3));

      // Clear queue
      dialogManager.clearQueue();
      expect(dialogManager.dialogQueue.length, equals(0));
    });
  });
}
