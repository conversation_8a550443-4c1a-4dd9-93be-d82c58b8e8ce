// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Package imports:
import 'package:get/get.dart';

// Project imports:
import 'package:tempognize/core/services/dialog_queue_manager.dart';

void main() {
  group('Microphone Permission Flow Tests', () {
    late DialogQueueManager dialogManager;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      dialogManager = DialogQueueManager();
      dialogManager.enableTestMode(); // Prevent automatic dialog processing
      Get.put(dialogManager, permanent: true);
    });

    tearDown(() {
      Get.reset();
    });

    test('should queue microphone permission dialog with correct priority', () {
      // Add microphone permission dialog
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4, // Lowest priority
        barrierDismissible: false,
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      ));

      expect(dialogManager.dialogQueue.length, equals(1));
      expect(dialogManager.dialogQueue.first.type, equals(DialogType.permission));
      expect(dialogManager.dialogQueue.first.id, equals('microphone_permission'));
      expect(dialogManager.dialogQueue.first.priority, equals(4));
      expect(dialogManager.dialogQueue.first.barrierDismissible, isFalse);
    });

    test('should maintain correct order with microphone permission as lowest priority', () {
      // Add dialogs in reverse priority order
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4, // Lowest priority
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.notification,
        id: 'notification',
        priority: 2,
        builder: (context) => const AlertDialog(title: Text('Notification')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update',
        priority: 1, // Highest priority
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));

      // Should be sorted by priority with microphone permission last
      expect(dialogManager.dialogQueue.length, equals(4));
      expect(dialogManager.dialogQueue[0].id, equals('version_update'));    // Priority 1
      expect(dialogManager.dialogQueue[1].id, equals('notification'));      // Priority 2
      expect(dialogManager.dialogQueue[2].id, equals('tutorial'));          // Priority 3
      expect(dialogManager.dialogQueue[3].id, equals('microphone_permission')); // Priority 4
    });

    test('should handle microphone permission dialog configuration correctly', () {
      final config = DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      );

      dialogManager.enqueueDialog(config);

      final queuedDialog = dialogManager.dialogQueue.first;
      expect(queuedDialog.type, equals(DialogType.permission));
      expect(queuedDialog.id, equals('microphone_permission'));
      expect(queuedDialog.priority, equals(4));
      expect(queuedDialog.barrierDismissible, isFalse);
    });

    test('should prevent duplicate microphone permission dialogs', () {
      final config = DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      );

      // Add the same dialog twice
      dialogManager.enqueueDialog(config);
      dialogManager.enqueueDialog(config);

      // Should only have one dialog
      expect(dialogManager.dialogQueue.length, equals(1));
      expect(dialogManager.dialogQueue.first.id, equals('microphone_permission'));
    });

    test('should handle complete startup sequence with microphone permission', () {
      // Simulate complete startup sequence
      
      // Add all startup dialogs
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.notification,
        id: 'notification',
        priority: 2,
        builder: (context) => const AlertDialog(title: Text('Notification')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      ));

      // Verify complete sequence
      expect(dialogManager.dialogQueue.length, equals(4));
      
      // Verify order: Version → Notification → Tutorial → Microphone Permission
      expect(dialogManager.dialogQueue[0].id, equals('version_update'));
      expect(dialogManager.dialogQueue[1].id, equals('notification'));
      expect(dialogManager.dialogQueue[2].id, equals('tutorial'));
      expect(dialogManager.dialogQueue[3].id, equals('microphone_permission'));
      
      // Verify microphone permission is last and non-dismissible
      final micDialog = dialogManager.dialogQueue.last;
      expect(micDialog.type, equals(DialogType.permission));
      expect(micDialog.barrierDismissible, isFalse);
    });
  });
}
