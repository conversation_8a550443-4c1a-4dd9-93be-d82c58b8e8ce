// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Package imports:
import 'package:get/get.dart';

// Project imports:
import 'package:tempognize/core/services/dialog_queue_manager.dart';
import 'package:tempognize/services/version_service.dart';

void main() {
  group('Fixed Dialog Behavior Tests', () {
    late DialogQueueManager dialogManager;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      dialogManager = DialogQueueManager();
      dialogManager.enableTestMode(); // Prevent automatic dialog processing
      Get.put(dialogManager, permanent: true);
    });

    tearDown(() {
      Get.reset();
    });

    test('should include microphone permission in startup sequence again', () async {
      // Simulate the startup dialog initialization
      await dialogManager.initializeStartupDialogs();

      // The method should be called without errors
      // (In real app, microphone permission dialog would be added if user hasn't seen explanation)
      expect(true, isTrue); // Test passes if no exceptions thrown
    });

    test('should handle complete startup sequence with microphone permission', () {
      // Add all startup dialogs including microphone permission
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.notification,
        id: 'notification',
        priority: 2,
        builder: (context) => const AlertDialog(title: Text('Notification')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      ));

      // Verify complete sequence: Version → Notification → Tutorial → Microphone Permission
      expect(dialogManager.dialogQueue.length, equals(4));
      expect(dialogManager.dialogQueue[0].id, equals('version_update'));
      expect(dialogManager.dialogQueue[1].id, equals('notification'));
      expect(dialogManager.dialogQueue[2].id, equals('tutorial'));
      expect(dialogManager.dialogQueue[3].id, equals('microphone_permission'));
      
      // Verify microphone permission is last and non-dismissible
      final micDialog = dialogManager.dialogQueue.last;
      expect(micDialog.type, equals(DialogType.permission));
      expect(micDialog.barrierDismissible, isFalse);
    });

    test('should maintain correct priority order with microphone permission restored', () {
      // Add dialogs in random order
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'microphone_permission',
        priority: 4, // Lowest priority
        builder: (context) => const AlertDialog(title: Text('Microphone Permission')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version_update',
        priority: 1, // Highest priority
        builder: (context) => const AlertDialog(title: Text('Version Update')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.notification,
        id: 'notification',
        priority: 2,
        builder: (context) => const AlertDialog(title: Text('Notification')),
      ));

      // Should be sorted by priority with microphone permission last
      expect(dialogManager.dialogQueue.length, equals(4));
      expect(dialogManager.dialogQueue[0].id, equals('version_update'));    // Priority 1
      expect(dialogManager.dialogQueue[1].id, equals('notification'));      // Priority 2
      expect(dialogManager.dialogQueue[2].id, equals('tutorial'));          // Priority 3
      expect(dialogManager.dialogQueue[3].id, equals('microphone_permission')); // Priority 4
    });

    test('should handle version service with shown version tracking', () {
      // Test that VersionService has the new method
      final versionService = VersionService();
      
      // These methods should exist
      expect(versionService.compareVersions, isNotNull);
      expect(versionService.markVersionDialogShown, isNotNull);
    });

    test('should handle dialog types correctly with restored microphone permission', () {
      // Add different types of dialogs
      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.versionUpdate,
        id: 'version',
        priority: 1,
        builder: (context) => const AlertDialog(title: Text('Version')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.tutorial,
        id: 'tutorial',
        priority: 3,
        builder: (context) => const AlertDialog(title: Text('Tutorial')),
      ));

      dialogManager.enqueueDialog(DialogConfig(
        type: DialogType.permission,
        id: 'permission',
        priority: 4,
        builder: (context) => const AlertDialog(title: Text('Permission')),
      ));

      // Verify types are preserved and ordered correctly
      expect(dialogManager.dialogQueue[0].type, equals(DialogType.versionUpdate));
      expect(dialogManager.dialogQueue[1].type, equals(DialogType.tutorial));
      expect(dialogManager.dialogQueue[2].type, equals(DialogType.permission));
    });
  });
}
