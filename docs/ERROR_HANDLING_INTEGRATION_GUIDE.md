# Enterprise-Grade Error Handling Integration Guide

This guide provides comprehensive instructions for integrating and using the new enterprise-grade error handling system in your Flutter app.

## Overview

The new error handling system provides:
- **Comprehensive Error Categorization**: Structured error types with proper severity levels
- **Intelligent Retry Mechanisms**: Exponential backoff with circuit breaker patterns
- **Offline Support**: Queue-based operations with automatic sync when online
- **Enhanced User Feedback**: Context-aware error messages and loading states
- **Monitoring & Debugging**: Comprehensive logging and analytics dashboard

## Architecture Components

### Core Components

1. **AppError System** (`lib/core/error_handling/app_error.dart`)
   - Base error classes with proper categorization
   - Network, Business, and System error types
   - Automatic retry configuration based on error type

2. **Retry Manager** (`lib/core/error_handling/retry_manager.dart`)
   - Exponential backoff with jitter
   - Circuit breaker pattern for preventing cascading failures
   - Configurable retry policies

3. **Connectivity Service** (`lib/core/connectivity/connectivity_service.dart`)
   - Real-time network monitoring
   - Network quality assessment
   - Offline/online state management

4. **Offline Queue Manager** (`lib/core/offline/offline_queue_manager.dart`)
   - Operation queuing for offline scenarios
   - Priority-based processing
   - Automatic retry when connectivity restored

5. **Error Handler Service** (`lib/core/error_handling/error_handler_service.dart`)
   - Centralized error processing
   - User-friendly error presentation
   - Rate limiting and deduplication

6. **Enhanced AI Service** (`lib/services/enhanced_ai_service.dart`)
   - Replaces the original AI service
   - Integrated error handling and offline support
   - Comprehensive retry logic

## Integration Steps

### 1. Update Dependencies

Add the required dependency to your `pubspec.yaml`:

```yaml
dependencies:
  connectivity_plus: ^6.0.5
```

### 2. Initialize Services

The services are automatically initialized in `main.dart`:

```dart
// Initialize enterprise-grade error handling services
await ServiceInitializer.initialize();

// Initialize loading state manager
Get.put(LoadingStateManager(), permanent: true);
```

### 3. Replace Existing AI Service Usage

**Before:**
```dart
final aiService = AIService();
final result = await aiService.askAI(note);
```

**After:**
```dart
final aiService = EnhancedAIService.instance;
try {
  final result = await aiService.askAI(note);
  // Handle success
} catch (error) {
  // Error is automatically handled by ErrorHandlerService
  // Additional custom handling can be added here
}
```

### 4. Update Note Cards

Replace existing `NoteCard` widgets with `EnhancedNoteCard`:

**Before:**
```dart
NoteCard(
  note: note,
  onRetryAI: (note) => _retryAI(note),
  // ... other properties
)
```

**After:**
```dart
EnhancedNoteCard(
  note: note,
  onRetryAI: (note) => _retryAI(note),
  // ... other properties
)
```

### 5. Add Loading State Management

For operations that need loading indicators:

```dart
final loadingManager = LoadingStateManager.instance;

// Start loading
final loadingId = loadingManager.startLoading(
  type: LoadingType.aiRequest,
  message: 'Processing your request...',
  estimatedDuration: Duration(seconds: 30),
  isCancellable: true,
  onCancel: () => _cancelOperation(),
);

try {
  // Perform operation
  final result = await someAsyncOperation();
  
  // Update progress if needed
  loadingManager.updateProgress(loadingId, progress: 50.0);
  
  // Complete loading
  loadingManager.completeLoading(loadingId);
} catch (error) {
  loadingManager.completeLoading(loadingId);
  rethrow;
}
```

## Usage Examples

### 1. Handling Network Operations

```dart
// The enhanced AI service automatically handles network errors
try {
  final result = await EnhancedAIService.instance.askAI(note);
  // Success - result contains the updated note
} catch (error) {
  // Error is automatically presented to user
  // Add custom logic if needed
  if (error is NetworkError && error.type == NetworkErrorType.unauthorized) {
    // Navigate to login
    Get.toNamed('/login');
  }
}
```

### 2. Custom Error Handling

```dart
try {
  await someOperation();
} catch (error) {
  await ErrorHandlerService.instance.handleError(
    error,
    context: 'Custom operation',
    showToUser: true,
    enableRetry: true,
    onRetry: () => someOperation(),
  );
}
```

### 3. Offline Operations

```dart
// Operations are automatically queued when offline
final operation = QueuedOperation(
  id: DateTime.now().millisecondsSinceEpoch.toString(),
  type: OperationType.askAI,
  priority: OperationPriority.high,
  data: {'noteId': note.id, 'prompt': note.prompt},
  createdAt: DateTime.now(),
);

await OfflineQueueManager.instance.enqueue(operation);
```

### 4. Monitoring and Debugging

Access the monitoring dashboard:

```dart
// Add to your debug menu or settings
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => ErrorMonitoringDashboard(),
  ),
);
```

## Configuration Options

### Service Configuration

```dart
await ServiceInitializer.initialize(
  config: ServiceConfig(
    enableErrorLogging: true,
    enableOfflineSupport: true,
    enableConnectivityMonitoring: true,
    enableEnhancedAI: true,
    enableDebugMode: kDebugMode,
  ),
);
```

### Error Handler Configuration

```dart
final errorHandler = ErrorHandlerService(
  config: ErrorHandlerConfig(
    enableLogging: true,
    enableUserNotifications: true,
    enableRetryPrompts: true,
    notificationDuration: Duration(seconds: 4),
    enableDebugMode: kDebugMode,
  ),
);
```

### Retry Configuration

```dart
final result = await RetryManager.execute(
  () => someOperation(),
  config: RetryConfig(
    maxAttempts: 3,
    baseDelay: Duration(seconds: 1),
    maxDelay: Duration(seconds: 30),
    backoffMultiplier: 2.0,
    enableJitter: true,
  ),
);
```

## Best Practices

### 1. Error Categorization

Always use appropriate error types:

```dart
// For network issues
throw NetworkError(
  type: NetworkErrorType.timeout,
  code: 'NETWORK_TIMEOUT',
  message: 'Request timed out',
  userMessage: 'Connection is taking too long. Please try again.',
);

// For business logic issues
throw BusinessError(
  type: BusinessErrorType.quotaExceeded,
  code: 'QUOTA_EXCEEDED',
  message: 'API quota exceeded',
  userMessage: 'You have reached your usage limit.',
);
```

### 2. Contextual Information

Provide context for better debugging:

```dart
try {
  await operation();
} catch (error) {
  await ErrorHandlerService.instance.handleError(
    error,
    context: 'User ID: ${user.id}, Operation: ${operation.name}',
  );
}
```

### 3. Loading States

Use appropriate loading types:

```dart
// For AI operations
LoadingType.aiRequest

// For data synchronization
LoadingType.dataSync

// For file uploads
LoadingType.fileUpload

// For authentication
LoadingType.authentication
```

### 4. Offline Handling

Design operations to work offline:

```dart
// Check connectivity before operations
if (ConnectivityService.instance.isOnline) {
  // Perform online operation
} else {
  // Queue for later or provide offline alternative
}
```

## Migration from Existing Code

### 1. Replace AIService

Find all instances of `AIService()` and replace with `EnhancedAIService.instance`.

### 2. Update Error Handling

Replace try-catch blocks with the new error handling patterns.

### 3. Update UI Components

Replace `NoteCard` with `EnhancedNoteCard` for better error display.

### 4. Add Loading States

Identify long-running operations and add appropriate loading indicators.

## Testing

The system includes comprehensive tests in `test/error_handling_test.dart`. Run tests with:

```bash
flutter test test/error_handling_test.dart
```

## Monitoring

Access real-time monitoring through:

1. **Error Monitoring Dashboard**: Visual interface for debugging
2. **Service Health Checks**: Programmatic health monitoring
3. **Log Export**: Export logs for external analysis

## Troubleshooting

### Common Issues

1. **Services not initialized**: Ensure `ServiceInitializer.initialize()` is called in main.dart
2. **Missing dependencies**: Run `flutter pub get` after updating pubspec.yaml
3. **Connectivity issues**: Check device permissions for network access

### Debug Information

Access debug information:

```dart
// Service status
final status = ServiceInitializer.getServiceStatus();

// Error statistics
final errorStats = ErrorHandlerService.instance.getErrorStats();

// Connectivity information
final connectivity = ConnectivityService.instance.getConnectivityStats();
```

## Performance Considerations

- Error logging is optimized for minimal performance impact
- Retry mechanisms use exponential backoff to prevent overwhelming servers
- Circuit breakers prevent cascading failures
- Offline queue is persisted to prevent data loss

## Security

- Error messages are sanitized to prevent information leakage
- Sensitive data is not logged in production builds
- Network operations use proper authentication headers
- Offline data is stored securely

This integration guide ensures a smooth transition to the new enterprise-grade error handling system while maintaining backward compatibility and improving overall app reliability.
