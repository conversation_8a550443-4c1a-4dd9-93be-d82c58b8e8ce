name: tempognize
description: "Tempognize"
publish_to: "none"
version: 1.1.4+1

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  get: ^4.6.6
  shared_preferences: ^2.0.8
  flex_color_scheme: ^8.1.0
  http: ^1.2.0
  sqflite: ^2.0.3
  path: ^1.8.0
  path_provider: ^2.1.2
  uuid: ^4.3.3
  flutter_markdown: ^0.7.4+3
  event_bus: ^2.0.0
  pasteboard: ^0.3.0
  confetti: ^0.8.0
  share_plus: ^10.1.2
  font_awesome_flutter: ^10.8.0
  dio: ^5.4.0
  url_launcher: ^6.2.5
  webview_flutter: ^4.10.0
  image_picker: ^1.1.2
  image_cropper: ^8.1.0
  image: ^4.5.2
  gal: ^2.3.1
  widgets_to_image: ^1.0.0
  flutter_sound_record: ^3.3.2
  flutter_staggered_grid_view: ^0.7.0
  audioplayers: ^5.0.0
  crypto: ^3.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  import_sorter: ^4.6.0
  flutter_launcher_icons: ^0.14.2

flutter:
  uses-material-design: true
  assets:
    - assets/images/bg.png
    - assets/icon/icon.png
    - assets/images/qrcode.png

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icon/icon.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/icon/icon.png"
  min_sdk_android: 21
  remove_alpha_ios: true
  ios_content_mode: center
