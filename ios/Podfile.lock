PODS:
  - audioplayers_darwin (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_sound_record (0.0.1):
    - Flutter
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_sound_record (from `.symlinks/plugins/flutter_sound_record/ios`)
  - gal (from `.symlinks/plugins/gal/darwin`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - TOCropViewController

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  flutter_sound_record:
    :path: ".symlinks/plugins/flutter_sound_record/ios"
  gal:
    :path: ".symlinks/plugins/gal/darwin"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  audioplayers_darwin: ccf9c770ee768abb07e26d90af093f7bab1c12ab
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_sound_record: abedfcafaeed722f2ea3d8f5d2da53e7cea1680b
  gal: baecd024ebfd13c441269ca7404792a7152fde89
  image_cropper: 5f162dcf988100dc1513f9c6b7eb42cd6fbf9156
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  pasteboard: 49088aeb6119d51f976a421db60d8e1ab079b63c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  webview_flutter_wkwebview: 44d4dee7d7056d5ad185d25b38404436d56c547c

PODFILE CHECKSUM: 9d8d1770d47a428fcb57a5d5f228a34e6d072ae7

COCOAPODS: 1.16.2
